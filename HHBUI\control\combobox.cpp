﻿#include "pch.h"
#include "combobox.h"
#include <common/winapi.h>

HHBUI::UIComboBox::UIComboBox(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-combobox", lpszName, dwStyle, dwStyleEx, nID, dwTextFormat);
	p_data.pArray = new UIarray();
}

void HHBUI::UIComboBox::Popup()
{
	if (GetTickCount64() - p_data.nProcessTime < 200)
	{
		EndMenu();
		p_data.nProcessTime = GetTickCount64();
		return;
	}
	else if(IsVisible())
	{
		if (p_data.pWnd)
		{
			delete p_data.pWnd;
			p_data.pWnd = nullptr;
			return;
		}
		p_data.pWnd = new UIWnd(0, 0, 0, 0, NULL, WS_BORDER | WS_SYSMENU | WS_POPUP, WS_EX_TOPMOST | WS_EX_TOOLWINDOW | WS_EX_LAYERED,
			UISTYLE_NOINHERITBKG | UISTYLE_NOCAPTIONTOPMOST | UISTYLE_NOTITLEBAR | UISTYLE_POPUPWINDOW | UISTYLE_NOSHADOW | EWS_COMBOWINDOW, m_data.pWnd->GethWnd(), NULL, (size_t)this, OnWndMsgCrProc);

		UIColor background; UIColor border; UIColor text_normal;
		GetColor(color_background, background);
		if (background.empty())
			background = UIColor(255, 255, 255, 255);
		p_data.pWnd->SetBackgColor(background);
		GetColor(color_border, border);
		p_data.pWnd->SetBorderColor(border, 1);
		//p_data.pWnd->SetShadowColor(border);

		size_t count = p_data.pArray->size();
		if (count == 0 || count > 8)
			count = 8;

		RECT tmp{};   auto unknown = m_data.Frame_c;
		combobox_ToScreen(unknown.left, unknown.top);
		tmp.left = unknown.left;
		tmp.top = unknown.top + unknown.bottom + 5;
		tmp.right = (p_data.droppedwidth == 0 ? m_data.Frame.right - m_data.Frame.left : p_data.droppedwidth);
		tmp.bottom = (LONG)(count * p_data.itemheight + 2 + 2);

		RECT screen, desk{};
		HHBUI::UIWinApi::GetWndScreenRectEx(m_data.pWnd->GethWnd(), screen, desk);
		if (tmp.top + tmp.bottom - screen.bottom > 0)//超出屏幕底部
		{
			unknown.bottom = m_data.Frame.bottom - m_data.Frame.top;
			tmp.top -= tmp.bottom + unknown.bottom + UIEngine::fScale(4);
		}
		if (tmp.left - screen.right - screen.left + tmp.right + 20 > 0)//超出屏幕右边
		{
			tmp.left = screen.right - tmp.right - 10;
		}
		if (tmp.left < 0)//超出屏幕左边
		{
			tmp.left = 0;
		}
		p_data.pWnd->Move(tmp.left, tmp.top, tmp.right, tmp.bottom);


		p_data.listview = new UIList(p_data.pWnd, 0, 0, 100, 200, eos_scroll_controlbutton | eos_scroll_v | eos_elvs_verticallist, 0, 0, Middle | Left);
		p_data.listview->SetMsgProc(On_free_Proc);
		p_data.listview->SetlParam((size_t)this);
		GetColor(color_text_normal, text_normal);
		p_data.listview->SetCrHot(p_data.ItemCr[0], p_data.ItemCr[1], p_data.ItemCr[2]);
		p_data.listview->SetItemHeight(p_data.itemheight);
		if (p_data.itemwidth == 0)
			p_data.itemwidth = tmp.right;
		p_data.listview->SetItemWidth(p_data.itemwidth);
		p_data.listview->SetColor(color_text_normal, text_normal);
		GetColor(color_text_hover, text_normal);
		p_data.listview->SetColor(color_text_hover, text_normal);
		GetColor(color_text_down, text_normal);
		p_data.listview->SetColor(color_text_down, text_normal);

		p_data.listview->Lock(0, 0, 0, 0);
		p_data.listview->s_data.isData = true;
		p_data.listview->s_data.pArray = p_data.pArray;
		p_data.listview->Update();
		p_data.listview->SetSelect(p_data.CurSelItem);
		p_data.listview->SetEnsureVisible(p_data.CurSelItem);
		LOGFONTW logfont;
		auto hFont = GetFont();
		hFont->GetLogFont(&logfont);
		p_data.listview->SetFontLogFont(&logfont);

		if (!m_data.radius.empty())
		{
			int rad = m_data.radius.left / UIEngine::GetDefaultScale();
			p_data.edit->SetRadius(rad, rad, rad, rad);
			p_data.listview->SetRadius(rad, rad, rad, rad);
			p_data.pWnd->SetRadius(rad);
		}
		p_data.pWnd->Show();

		p_data.fAngle = 180.f;
		Redraw();
		//UIAnimation::Start(this, 0, 180, 0, 0, AniEffect::Default, 15, 0, 1, 1);
	}
}
void HHBUI::UIComboBox::Close()
{
	if (p_data.pWnd)
	{
		delete p_data.pWnd;
		p_data.pWnd = nullptr;
	}
}
BOOL HHBUI::UIComboBox::IsPopup()
{
	return p_data.pWnd != nullptr;
}
void HHBUI::UIComboBox::SetColourArrow(UIColor dwColor)
{
	if (p_data.dstBrush)
		delete p_data.dstBrush;
	p_data.dstBrush = new UIBrush(dwColor);
}

void HHBUI::UIComboBox::SetDroppedWidth(INT width)
{
	p_data.droppedwidth = width;
}

void HHBUI::UIComboBox::SetItemHeight(INT height)
{
	p_data.itemheight = height;
}

void HHBUI::UIComboBox::SetItemWidth(INT nWidth)
{
	p_data.itemwidth = nWidth;
}

INT HHBUI::UIComboBox::AddItem(ListItem* item, INT index)
{
	if (item)
	{
		if (item->text != 0)
			item->text = StrDupW(item->text);
		if (index == -1)
		{
			p_data.pArray->insert((size_t)item);
			//UpdateIndex();
		}
		else if (IndexCheck(index))
		{
			p_data.pArray->insert((size_t)item, index);
			
		}
		else
		{
			return (INT)p_data.pArray->size();
		}
	}

	return (INT)p_data.pArray->size();
}

void HHBUI::UIComboBox::GetItem(int index, ListItem** item)
{
	if (index == -1)
	{
		if (p_data.pArray->size())
			*item = (ListItem*)p_data.pArray->end();
	}
	if (IndexCheck(index))
		*item = (ListItem*)p_data.pArray->get(index);
}

void HHBUI::UIComboBox::SetCurSelItem(int index)
{
	if (IndexCheck(index))
	{
		p_data.CurSelItem = index;
		auto item = (ListItem*)p_data.pArray->get(index);
		if (item)
			p_data.edit->SetText(item->text);
	}
}

int HHBUI::UIComboBox::GetCurSelItem()
{
	return p_data.CurSelItem;
}

INT HHBUI::UIComboBox::GetItemListCount()
{
	return (INT)p_data.pArray->size();
}

int HHBUI::UIComboBox::DeleteItem(int index)
{
	if (index == -1)
	{
		index = (INT)p_data.pArray->size() - 1;
		if (index < 0) index = 0;
	}
	if (IndexCheck(index))
	{
		auto item = (ListItem*)p_data.pArray->get(index);
		if (item) {
			if (item->nImage)
				delete item->nImage;
			if (item->text)
				LocalFree((HLOCAL)item->text);
		}
		p_data.pArray->erase(p_data.pArray->begin() + index);
	}
	return (INT)p_data.pArray->size();
}


void HHBUI::UIComboBox::DeleteAllItem(bool delItem)
{
	if (delItem)
	{
		for (size_t i = 1; i <= p_data.pArray->size(); ++i)
		{
			auto item = (ListItem*)p_data.pArray->get(i);
			if(item){
				if (item->nImage)
					delete item->nImage;
				if (item->text)
					LocalFree((HLOCAL)item->text);
			}
			//delete item;
		}
	}
	p_data.CurSelItem = 0;
	p_data.pArray->clear();
}
void HHBUI::UIComboBox::SetCrHot(UIColor hover, UIColor checked, UIColor down)
{
	if (!hover.empty())
		p_data.ItemCr[0] = hover;
	if (!checked.empty())
		p_data.ItemCr[1] = checked;
	if (!down.empty())
		p_data.ItemCr[2] = down;
}
LPCWSTR HHBUI::UIComboBox::GetText()
{
	return p_data.edit->GetText();
}

LPVOID HHBUI::UIComboBox::GetEdit()
{
	return p_data.edit;
}

LRESULT HHBUI::UIComboBox::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_CREATE)
	{
		p_data.edit = new UIEdit(this, 0, 0, m_data.Frame.right, 30, m_data.pstrTitle, m_data.dwStyle, eos_ex_tabstop | eos_ex_focusable, 0, m_data.dwTextFormat);
		p_data.edit->SetMsgProc(On_free_Proc);
		p_data.edit->SetlParam((size_t)this);
		p_data.edit->Lock(2, 2, 30, 2);
		UIColor dwColor;
		GetColor(color_text_normal, dwColor);
		p_data.dstBrush = new UIBrush(dwColor);

	}
	else if (uMsg == WM_DESTROY)
	{
		if (p_data.dstBrush)
			delete p_data.dstBrush;
		DeleteAllItem();
		delete p_data.pArray;
	}
	else if (uMsg == WM_EX_EASING)
	{
		auto easing = (HHBUI::info_Animation*)lParam;
		p_data.fAngle = easing->nCurrentX;
		Redraw();
	}
	else if (uMsg == WM_LBUTTONDOWN)
	{
		Popup();
	}
	else if (uMsg == WM_SETTEXT)
	{
		p_data.edit->SetText(m_data.pstrTitle);
	}
	else if (uMsg == WM_SETFONT)
	{
		LOGFONTW logfont;
		auto hFont = GetFont();
		hFont->GetLogFont(&logfont);
		p_data.edit->SetFontLogFont(&logfont);

	}
	return S_OK;
}

LRESULT HHBUI::UIComboBox::OnWndMsgCrProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIComboBox*)window->GetlParam();
	if (uMsg == WM_DESTROY)
	{
		INT CurSelItem = obj->p_data.listview->GetSelect();
		if (CurSelItem != 0 && CurSelItem != obj->p_data.CurSelItem)
		{
			ListItem* item = nullptr;
			obj->p_data.listview->GetItem(CurSelItem, &item);
			if (item)
				obj->p_data.edit->SetText(item->text);
			obj->p_data.CurSelItem = CurSelItem;
			obj->DispatchNotify(WMM_CMB_ITEMCHANGED, 0, CurSelItem);
		}
		obj->p_data.nProcessTime = GetTickCount64();
		obj->SetState(state_hover | state_down, TRUE);
		obj->DispatchNotify(MCM_GETCURSEL, 0, CurSelItem);
		obj->p_data.pWnd = nullptr;

		obj->p_data.fAngle = 0.f;
		obj->Redraw();
		//UIAnimation::Start(obj, 180, 0, 0, 0, AniEffect::Default, 10, 0, 1, 1);
	}
	return S_OK;
}

LRESULT HHBUI::UIComboBox::On_free_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_SETFOCUS)
	{
		auto obj = (UIEdit*)UIView;
		auto hobj = (UIComboBox*)obj->GetlParam();
		if (UIView == hobj->p_data.edit)
		{
			hobj->SetState(state_focus, FALSE);
			hobj->Redraw();
		}
	}
	else if (uMsg == WM_KILLFOCUS)
	{
		auto obj = (UIEdit*)UIView;
		auto hobj = (UIComboBox*)obj->GetlParam();
		if (UIView == hobj->p_data.edit)
		{
			hobj->SetState(state_focus, TRUE);
			hobj->Redraw();
		}
	}
	else if (uMsg == WM_LBUTTONUP)
	{
		auto obj = (UIList*)UIView;
		auto hobj = (UIComboBox*)obj->GetlParam();
		if (UIView == hobj->p_data.listview)
		{
			hobj->Popup();
			
		}
	}
	else if (uMsg == WM_LBUTTONDOWN)
	{
		auto obj = (UIEdit*)UIView;
		auto hobj = (UIComboBox*)obj->GetlParam();
		if (UIView == hobj->p_data.edit)
		{
			if (FLAGS_CHECK(obj->Style(), eos_edit_readonly))
			{
				hobj->Popup();
			}
		}
	}
	return S_OK;
}

void HHBUI::UIComboBox::OnPaintProc(ps_context ps)
{
	auto pDeviceContext = (ID2D1DeviceContext*)ps.hCanvas->GetContext(0);
	UIDrawContext::DrawRotatedLines(pDeviceContext, (ID2D1Brush*)p_data.dstBrush->GetContext(), ps.rcPaint.right - 35, (ps.uHeight - static_cast<FLOAT>(35)) / 2, 35, 35, p_data.fAngle);

}

void HHBUI::UIComboBox::combobox_ToScreen(float& x, float& y)
{
	ExRectF rect = m_data.Frame_c, swnd{};
	m_data.pWnd->GetRect(swnd);
	rect.left += m_data.Frame_w.left + swnd.left;
	rect.top += m_data.Frame_w.top + swnd.top;

	x += rect.left;
	y += rect.top;
}

bool HHBUI::UIComboBox::IndexCheck(INT index)
{
	if (index < (INT)p_data.pArray->size() && index >= 0)
		return true;
	return false;
}

