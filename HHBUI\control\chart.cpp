﻿#include "pch.h"
#include "chart.h"
#include <algorithm>
#include <numeric>
#include <iomanip>
HHBUI::UIChart::UIChart(UIBase* hParent, INT x, INT y, INT w, INT h, LPCWSTR title, chart_type type, INT nID)
{
	InitSubControl(hParent, x, y, w, h, L"form-chart", title, 0, 0, nID);

	p_data.type = type;
	p_data.w = UIEngine::fScale(w); p_data.h = UIEngine::fScale(h);

	p_data.brLine = new UIBrush(UIColor(149, 149, 151, 200));
	p_data.brItem = new UIBrush(m_data.Color.crHover);
	p_data.ftGen = new UIFont(NULL, 11, 0);
}

INT HHBUI::UIChart::AddItem(LPCWSTR name, float data, UIColor color, INT index)
{
	chart_item nItem;
	nItem.name = StrDupW(name);
	nItem.data = data;
	nItem.color = color;

	int ins = 0;
	if (index < 0 || index >= p_data.list.size()) {
		p_data.list.push_back(nItem);
		ins = (int)(p_data.list.size() - 1);
	}
	else {
		p_data.list.insert(p_data.list.begin() + index, nItem);
		ins = index;
	}

	return ins;
}

BOOL HHBUI::UIChart::SetItemName(INT index, LPCWSTR name)
{
	if (index >= 0 && index < p_data.list.size()) {
		auto pname = p_data.list.at(index).name;
		LocalFree((HLOCAL)pname);
		p_data.list.at(index).name = name;
		return true;
	}
	return false;
}

BOOL HHBUI::UIChart::SetItemData(INT index, float data)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.at(index).data = data;
		return true;
	}
	return false;
}

BOOL HHBUI::UIChart::SetItemColor(INT index, UIColor color)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.at(index).color = color;
		return true;
	}
	return false;
}

BOOL HHBUI::UIChart::DelItem(INT index)
{
	if (index >= 0 && index < p_data.list.size()) {
		auto pname = p_data.list.at(index).name;
		LocalFree((HLOCAL)pname);
		p_data.list.erase(p_data.list.begin() + index);
		return true;
	}
	return false;
}

void HHBUI::UIChart::ClearItem()
{
	if (int index = 0 && index < p_data.list.size()) {
		auto pname = p_data.list.at(index).name;
		LocalFree((HLOCAL)pname);
	}
	p_data.list.clear();
}


void HHBUI::UIChart::SetChartType(chart_type type)
{
	p_data.type = type;
}

void HHBUI::UIChart::SetDataDisplayMode(int mode)
{
	if (mode >= 0 && mode <= 2){
		p_data.dataMode = mode;
	}
}

void HHBUI::UIChart::SetLegend(bool isVL)
{
	p_data.isVL = isVL;
}

void HHBUI::UIChart::SetAutoMax(bool isAuto)
{
	p_data.maxAuto = isAuto;
	if (!p_data.maxAuto) {
        p_data.max = 100;
		p_data.min = 0;
	}
}

void HHBUI::UIChart::SetMaxData(float max, float min)
{
	if (!p_data.maxAuto) {
		p_data.max = max;
		p_data.min = min;
	}
}

void HHBUI::UIChart::SetBarPoint(bool isVert)
{
	p_data.isVert = isVert;
}


void HHBUI::UIChart::SetLineCurve(bool isCurve)
{
	p_data.isCurve = isCurve;
}
void HHBUI::UIChart::SetLineWidth(float width)
{
	p_data.lw = width;
}


void HHBUI::UIChart::Update()
{
	if (p_data.list.size() > 0) {
        ceil_data();
		Redraw();
	}
}


std::vector<HHBUI::ExPointF> GenerateBezierControls(
	const HHBUI::ExPointF& start,
	const HHBUI::ExPointF& end,
	float curvature = 0.3f)
{

	const float dx = end.x - start.x;
	const float perpen_dx = dx * curvature;

	std::vector<HHBUI::ExPointF> result;
    result.push_back(HHBUI::ExPointF::ExPointF(start.x + perpen_dx, start.y));
    result.push_back(HHBUI::ExPointF::ExPointF(end.x - perpen_dx, end.y));

	// 生成对称控制点
	return result;
}

void HHBUI::UIChart::CalculatePercentages(std::vector<chart_item>& items) {
	// 计算数据总和
	const float total = std::accumulate(items.begin(), items.end(), 0.0f,
		[](float sum, const chart_item& item) { return sum + item.data; });

	// 处理零值情况
	if (total == 0.0f) {
		for (auto& item : items) item.per = 0.0f;
		return;
	}

	// 临时数据结构
	struct TempData {
		float original;     // 原始精确百分比
		float rounded;       // 四舍五入后的值
		float remainder;     // 小数余量
		size_t index;        // 原始索引
	};
	std::vector<TempData> temp;

	// 预处理数据并计算初始百分比
	for (size_t i = 0; i < items.size(); ++i) {
		const float percent = (items[i].data / total) * 100.0f;
		const float rounded = std::roundf(percent * 100.0f) / 100.0f;  // 两位小数四舍五入
		temp.push_back({
			percent,
			rounded,
			percent - rounded,  // 计算余量
			i
			});
	}

	// 计算当前总和与差值
	float current_sum = std::accumulate(temp.begin(), temp.end(), 0.0f,
		[](float sum, const TempData& d) { return sum + d.rounded; });
	const float diff = 100.0f - current_sum;
	const int adjust_count = static_cast<int>(std::round(std::abs(diff) / 0.01f));

	// 需要调整的情况
	if (adjust_count > 0) {
		// 排序策略：根据差值方向选择排序方式
		auto comparator = [diff](const TempData& a, const TempData& b) {
			return (diff > 0) ? (a.remainder > b.remainder)
				: (a.remainder < b.remainder);
			};
		std::stable_sort(temp.begin(), temp.end(), comparator);

		// 进行差额调整
		const float adjust = (diff > 0) ? 0.01f : -0.01f;
		for (int i = 0; i < adjust_count && i < static_cast<int>(temp.size()); ++i) {
			temp[i].rounded += adjust;
			temp[i].rounded = std::roundf(temp[i].rounded * 100.0f) / 100.0f;  // 重新四舍五入
		}

		// 恢复原始顺序
		std::stable_sort(temp.begin(), temp.end(),
			[](const TempData& a, const TempData& b) { return a.index < b.index; });
	}

	// 写回结果
	for (const auto& td : temp) {
		items[td.index].per = td.rounded;
	}
}

const wchar_t* FloatToWideStr(float value) {
	// 四舍五入到两位小数
	float rounded = std::round(value * 100.0f) / 100.0f;

	// 使用C locale确保小数点格式
	std::wstringstream wss;
	wss.imbue(std::locale("C"));
	wss << std::fixed << std::setprecision(2) << rounded;

	// 获取格式化后的字符串
	std::wstring ws = wss.str();

	// 处理小数部分
	size_t dot_pos = ws.find(L'.');
	if (dot_pos != std::wstring::npos) {
		// 查找最后一个非零字符
		size_t last_non_zero = ws.find_last_not_of(L'0');

		if (last_non_zero != std::wstring::npos) {
			if (last_non_zero == dot_pos) {
				// 情况1：小数点后全是0
				ws = ws.substr(0, dot_pos);
			}
			else {
				// 情况2：保留到最后一个有效数字
				ws = ws.substr(0, last_non_zero + 1);
			}
		}
		else {
			// 异常情况处理（理论上不会出现）
			ws = ws.substr(0, dot_pos);
		}
	}

	// 使用静态变量存储结果（注意线程安全性）
	static std::wstring result;
	result = ws;
	return result.c_str();
}

void CalculateReferenceLines(float max_val, float min_val, float total_height, float font_height,
	int& num_reference_lines, float& lower_limit,
	float& upper_limit, float& step) {
	// 处理最大值等于最小值的情况
	if (max_val == min_val) {
		float center = max_val;
		lower_limit = center - 1.0f;
		upper_limit = center + 1.0f;
		max_val = upper_limit;
		min_val = lower_limit;
	}

	float original_range = max_val - min_val;

	// 计算最大允许的间隔数量
	int max_intervals = static_cast<int>(std::floor(total_height / (2.0f * font_height)));
	max_intervals = std::max(max_intervals, 1); // 保证至少一个间隔

	// 计算最小步长并处理零范围情况
	float min_step = original_range / max_intervals;
	if (min_step <= 0.0f) min_step = 1.0f;

	// 计算步长的数量级和归一化因子
	int exponent = static_cast<int>(std::floor(std::log10(min_step)));
	float factor = std::pow(10.0f, exponent);
	float normalized = min_step / factor;

	// 选择最接近的"美观"步长
	if (normalized <= 1.0f) {
		normalized = 1.0f;
	}
	else if (normalized <= 2.0f) {
		normalized = 2.0f;
	}
	else if (normalized <= 5.0f) {
		normalized = 5.0f;
	}
	else {
		normalized = 10.0f;
	}
	step = normalized * factor;

	// 计算扩展后的上下限
	lower_limit = std::floor(min_val / step) * step;
	upper_limit = std::ceil(max_val / step) * step;

	// 确保上下限包含原始范围
	if (lower_limit > min_val) lower_limit -= step;
	if (upper_limit < max_val) upper_limit += step;

	// 计算最终的参考线数量
	float range = upper_limit - lower_limit;
	int intervals = static_cast<int>(std::round(range / step));
	num_reference_lines = intervals + 1;

	// 处理浮点精度问题
	constexpr float epsilon = 1e-6f;
	while ((upper_limit - lower_limit) / step + epsilon < intervals) {
		upper_limit += step;
		range = upper_limit - lower_limit;
		intervals = static_cast<int>(std::round(range / step));
		num_reference_lines = intervals + 1;
	}
}
void HHBUI::UIChart::ceil_data()
{
	//=====计算标题高度=====
	if (wcslen(m_data.pstrTitle) > 0) {
		float tw = 0, th = 0;
		UICanvas::CalcTextSize(GetFont(), m_data.pstrTitle, m_data.dwTextFormat, p_data.w, p_data.h, &tw, &th);
		p_data.frc.top = th;
	}
	p_data.frc.top += UIEngine::fScale(8);

	p_data.frc.right = p_data.w;

	//=====计算百分比值=====
	CalculatePercentages(p_data.list);

	//=====计算数据绝对上下限值=====
	if (p_data.maxAuto && p_data.list.size() > 0) {
		p_data.max = p_data.list.at(0).data;
		p_data.min = p_data.list.at(0).data;
		for (auto& item : p_data.list) {
            if (item.data > p_data.max) p_data.max = item.data;
			if (item.data < p_data.min) p_data.min = item.data;
		}
	}

	//=====计算数据项名空间=====
	float tw = 0, th = 0;
	if (p_data.isVL) {
		int imax = 0;
		std::wstring dmax = L"0.0";
		for (int i = 0; i < p_data.list.size(); i++) {
			if (wcslen(p_data.list.at(i).name) > wcslen(p_data.list.at(imax).name)) imax = i;
			std::wstring ndm = FloatToWideStr(p_data.dataMode == 2 ? p_data.list.at(i).per : p_data.list.at(i).data);
			if (ndm.length() > dmax.length()) dmax = ndm;
		}

		if(p_data.type==bar && !p_data.isVert){
			UICanvas::CalcTextSize(p_data.ftGen, p_data.list.at(imax).name, SingleLine, p_data.w, p_data.h / 3, &tw, &th);

			tw = std::min(tw, p_data.w / 3.f);
			p_data.frc.left = tw + UIEngine::fScale(2);

			UICanvas::CalcTextSize(p_data.ftGen, dmax.c_str(), Left | SingleLine, p_data.w / 3, p_data.h, &tw, &th);

			tw = UIEngine::fScale(std::max(std::min(tw, p_data.w / 3.f), 8.f));
			p_data.frc.right = p_data.w - tw - UIEngine::fScale(2);
			if (p_data.dataMode == 2) p_data.frc.right -= UIEngine::fScale(8);
		}
		else {
			UICanvas::CalcTextSize(p_data.ftGen, p_data.list.at(imax).name, Vertical | SingleLine, p_data.w, p_data.h / 3.0, &tw, &th);

			th = std::min(th, UIEngine::fScale(p_data.h / 3.0));
			p_data.frc.bottom = p_data.h - th - UIEngine::fScale(4);
		}
	}
	else {
		if (p_data.type == bar && !p_data.isVert) {
			p_data.frc.left = UIEngine::fScale(4);
		}
		else {
			p_data.frc.bottom = p_data.h - UIEngine::fScale(4);
		}
	}

	//=====计算参考线数据=====
	if (p_data.type == bar && !p_data.isVert) {
		UICanvas::CalcTextSize(p_data.ftGen, FloatToWideStr(p_data.max), Vertical | SingleLine, p_data.w / 3, p_data.h, &tw, &th);
		
		p_data.gth = tw;
		p_data.frc.top += tw;
		p_data.frc.left += tw;

		CalculateReferenceLines(p_data.max, p_data.min, p_data.frc.right - p_data.frc.left, tw, p_data.lines, p_data.min, p_data.max, p_data.step);

        UICanvas::CalcTextSize(p_data.ftGen, FloatToWideStr(p_data.max), Vertical | SingleLine, p_data.w / 3, p_data.h, &tw, &th);
		p_data.frc.bottom = p_data.h - th - UIEngine::fScale(8);
	}
	else {
		UICanvas::CalcTextSize(p_data.ftGen, FloatToWideStr(p_data.max), SingleLine, p_data.w / 3, p_data.h, &tw, &th);

		p_data.gth = th;
		p_data.frc.top += th;
		p_data.frc.right = p_data.w;

		CalculateReferenceLines(p_data.max, p_data.min, p_data.frc.bottom - p_data.frc.top, th, p_data.lines, p_data.min, p_data.max, p_data.step);

		UICanvas::CalcTextSize(p_data.ftGen, FloatToWideStr(p_data.max), SingleLine, p_data.w / 3, p_data.h, &tw, &th);
		p_data.frc.left = tw + UIEngine::fScale(8);
	}


	//=====计算饼图绘制区域=====
	if (p_data.type == pie) {
		int toh = p_data.h - p_data.frc.top;
		if (toh <= p_data.w) {
			p_data.frc.bottom = p_data.h;
			int hh = p_data.frc.bottom - p_data.frc.top;
			p_data.frc.left = (float)(p_data.w - hh) / 2;
			p_data.frc.right = p_data.frc.left + hh;
		}
		else {
			p_data.frc.left = 0;
            p_data.frc.right = p_data.w;
			p_data.frc.top = p_data.frc.top + (toh - p_data.w) / 2.f;
            p_data.frc.bottom = p_data.frc.top + p_data.w;
		}
	}

	//=====计算顶点=====
	if (p_data.list.size() > 0 && p_data.type != pie) {
		if (p_data.type == bar && !p_data.isVert) {
			int dw = p_data.frc.right - p_data.frc.left;
			float bh = (float)((p_data.frc.bottom - p_data.frc.top) / p_data.list.size());
			p_data.barW = std::max(std::min(bh * 0.6f, UIEngine::fScale(4)), UIEngine::fScale(20));
			p_data.barS = (float)(bh - p_data.barW) / 2.f;
            for (int i = 0; i < p_data.list.size(); i++) {
                auto* item = &p_data.list.at(i);
                item->ex.x = p_data.frc.left + ((item->data - p_data.min) / (p_data.max - p_data.min) * dw);
				item->ex.y = p_data.frc.top + i * bh + bh / 2.f;
            }
		}
		else {
			int dh = p_data.frc.bottom - p_data.frc.top;
			float bw = (float)((p_data.frc.right - p_data.frc.left) / p_data.list.size());
			p_data.barW = std::max(std::min(bw * 0.6f, UIEngine::fScale(4)), UIEngine::fScale(20));
			p_data.barS = (float)(bw - p_data.barW) / 2.f;
			for (int i = 0; i < p_data.list.size(); i++) {
				auto* item = &p_data.list.at(i);
				item->ex.x = p_data.frc.left + i * bw + bw / 2.f;
				item->ex.y = p_data.frc.bottom - ((item->data - p_data.min) / (p_data.max - p_data.min) * dh);
			}
		}
	}
}

LRESULT HHBUI::UIChart::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_SIZE) {
		p_data.w = LOWORD(lParam);
        p_data.h = HIWORD(lParam);
	}
	else if (uMsg == WM_DESTROY) {
		if (p_data.brItem) delete p_data.brItem;
		if (p_data.brLine) delete p_data.brLine;
		if (p_data.ftGen) delete p_data.ftGen;
	}
	return S_OK;
}

void HHBUI::UIChart::OnPaintProc(ps_context ps)
{
	BeginPaint(ps);

	if (wcslen(m_data.pstrTitle) > 0) {
		ps.hCanvas->DrawTextByColor(ps.hFont, m_data.pstrTitle, Center | Middle | SingleLine,
			0, 0, ps.uWidth, p_data.frc.top / 2, m_data.Color.crNormal);
	}

	if (p_data.type != pie) {
		//=====绘制外框=====
		p_data.brLine->SetColor(UIColor(149, 149, 151, 200));
		ps.hCanvas->DrawLine(p_data.brLine, p_data.frc.left, p_data.frc.top, p_data.frc.left, p_data.frc.bottom, 1, 0);
		ps.hCanvas->DrawLine(p_data.brLine, p_data.frc.left, p_data.frc.bottom, p_data.frc.right, p_data.frc.bottom, 1, 0);
	}

    switch (p_data.type)
    {
    case bar:
        paint_bar(ps);
        break;
    case line:
        paint_line(ps);
        break;
    case pie:
        paint_pie(ps);
        break;
    }

	EndPaint();
}

//绘制柱状图
void HHBUI::UIChart::paint_bar(ps_context ps)
{
	DWORD tfor = Right | Middle | SingleLine;
	p_data.brLine->SetColor(UIColor(149, 149, 151, 90));
	float hgh = p_data.gth / 2;
	//=====绘制参考线=====
	if (p_data.isVert) {
		ps.hCanvas->DrawTextByColor(p_data.ftGen, FloatToWideStr(p_data.min), tfor,
			0, p_data.frc.bottom - hgh, p_data.frc.left - 2 * ps.dpi, p_data.frc.bottom + hgh, m_data.Color.crBan);

		float steps = (float)(p_data.frc.bottom - p_data.frc.top) / (p_data.lines - 1);
		for (int i = 1; i < p_data.lines; i++) {
			float ltop = p_data.frc.bottom - i * steps;
			ps.hCanvas->DrawTextByColor(p_data.ftGen, FloatToWideStr(p_data.min + i * p_data.step), tfor,
				0, ltop - hgh, p_data.frc.left - 2 * ps.dpi, ltop + hgh, m_data.Color.crBan);
			ps.hCanvas->DrawLine(p_data.brLine, p_data.frc.left + 1, ltop, p_data.frc.right, ltop, 1, 1);
		}
	}
	else {
		tfor = Left | Middle | Vertical | SingleLine;

		ps.hCanvas->DrawTextByColor(p_data.ftGen, FloatToWideStr(p_data.min), tfor,
			p_data.frc.left - hgh, p_data.frc.bottom + 2 * ps.dpi, p_data.frc.left + hgh, ps.uHeight, m_data.Color.crBan);
		
		float steps = (float)(p_data.frc.right - p_data.frc.left) / (p_data.lines - 1);
		for (int i = 1; i < p_data.lines; i++) {
			float lleft = p_data.frc.left + i * steps;
			ps.hCanvas->DrawTextByColor(p_data.ftGen, FloatToWideStr(p_data.min + i * p_data.step), tfor,
				lleft - hgh, p_data.frc.bottom + 2 * ps.dpi, lleft + hgh, ps.uHeight, m_data.Color.crBan);
			ps.hCanvas->DrawLine(p_data.brLine, lleft, p_data.frc.top, lleft, p_data.frc.bottom - 1, 1, 1);
		}
	}

	if (p_data.list.size() > 0) {
		if (p_data.isVert) {
			tfor = Center | Middle | SingleLine;
			
			for (int i = 0; i < p_data.list.size(); i++)
			{
				auto *item = &p_data.list.at(i);
				float bleft = item->ex.x - p_data.barW / 2.f;

				p_data.brItem->SetColor(item->color);
				ps.hCanvas->FillRect(p_data.brItem, bleft, item->ex.y, bleft + p_data.barW, p_data.frc.bottom);
			
				if (p_data.dataMode == 1) {
					ps.hCanvas->DrawTextByColor(p_data.ftGen, FloatToWideStr(item->data), tfor,
						bleft - p_data.barS, item->ex.y - p_data.gth, bleft + p_data.barW + p_data.barS, item->ex.y, item->color);
				}
				else if (p_data.dataMode == 2) {
					std::wstring wtr = FloatToWideStr(item->per);
					wtr.append(L"%");
					ps.hCanvas->DrawTextByColor(p_data.ftGen, wtr.c_str(), tfor,
						bleft - p_data.barS, item->ex.y - p_data.gth, bleft + p_data.barW + p_data.barS, item->ex.y, item->color);
				}
				
				if(p_data.isVL) ps.hCanvas->DrawTextByColor(p_data.ftGen, item->name, Left | Middle | Vertical,
					bleft - p_data.barS, p_data.frc.bottom + 2 * ps.dpi, bleft + p_data.barW + p_data.barS, ps.uHeight, m_data.Color.crBan);
			}
		}
		else {
			tfor = Left | Middle | SingleLine;

            for (int i = 0; i < p_data.list.size(); i++)
			{
                auto *item = &p_data.list.at(i);
                float btop = item->ex.y - p_data.barW / 2.f;

                p_data.brItem->SetColor(item->color);
                ps.hCanvas->FillRect(p_data.brItem, p_data.frc.left, btop, item->ex.x, btop + p_data.barW);

                if (p_data.dataMode == 1) {
					ps.hCanvas->DrawTextByColor(p_data.ftGen, FloatToWideStr(item->data), tfor,
						item->ex.x + 2 * ps.dpi, btop, ps.uWidth, btop + p_data.barW, item->color);
				}
				else if (p_data.dataMode == 2) {
					std::wstring wtr = FloatToWideStr(item->per);
                    wtr.append(L"%");
					ps.hCanvas->DrawTextByColor(p_data.ftGen, wtr.c_str(), tfor,
						item->ex.x + 2 * ps.dpi, btop, ps.uWidth, btop + p_data.barW, item->color);
				}

				if (p_data.isVL) ps.hCanvas->DrawTextByColor(p_data.ftGen, item->name, Right | Middle | SingleLine,
					0, btop, p_data.frc.left - 2 * ps.dpi, btop + p_data.barW, m_data.Color.crBan);
			}
		}
	}
}
//绘制折线图
void HHBUI::UIChart::paint_line(ps_context ps)
{
	DWORD tfor = Right | Middle | SingleLine;
	p_data.brLine->SetColor(UIColor(149, 149, 151, 90));
	float hgh = p_data.gth / 2;
	//=====绘制参考线=====
	ps.hCanvas->DrawTextByColor(p_data.ftGen, FloatToWideStr(p_data.min), tfor,
		0, p_data.frc.bottom - hgh, p_data.frc.left - 2 * ps.dpi, p_data.frc.bottom + hgh, m_data.Color.crBan);

	float steps = (float)(p_data.frc.bottom - p_data.frc.top) / (p_data.lines - 1);
	for (int i = 1; i < p_data.lines; i++) {
		float ltop = p_data.frc.bottom - i * steps;
		ps.hCanvas->DrawTextByColor(p_data.ftGen, FloatToWideStr(p_data.min + i * p_data.step), tfor,
			0, ltop - hgh, p_data.frc.left - 2 * ps.dpi, ltop + hgh, m_data.Color.crBan);
		ps.hCanvas->DrawLine(p_data.brLine, p_data.frc.left + 1, ltop, p_data.frc.right, ltop, 1, 1);
	}

	if (p_data.list.size() > 0) {
		auto count = (int)p_data.list.size();
		ExPointF* ptf = new ExPointF[count];

		for (int i = 0; i < count; i++) {
			auto *item = &p_data.list.at(i);
			ptf[i].x = item->ex.x;
            ptf[i].y = item->ex.y;
		}

		p_data.brItem->SetColor(m_data.Color.crHover);
		if (count >= 2) {
			if (!p_data.isCurve) {
				ps.hCanvas->DrawCurves(p_data.brItem, ptf, count, 1.f, p_data.lw, true, false);
			}
			else {
				for (int i = 0; i < (count - 1); i++) {
					std::vector<ExPointF> cpts = GenerateBezierControls(ptf[i], ptf[i + 1]);

					ps.hCanvas->DrawBezier(p_data.brItem, ptf[i].x, ptf[i].y, cpts[0].x, cpts[0].y, cpts[1].x, cpts[1].y, ptf[i + 1].x, ptf[i + 1].y, p_data.lw, false, false);
				}
			}
		}

		tfor = Center | Middle | SingleLine;
		for (int i = 0; i < count; i++) {
            auto *item = &p_data.list.at(i);
			float bleft = item->ex.x - p_data.barW / 2.f;

			p_data.brItem->SetColor(item->color);
			ps.hCanvas->DrawPoint(p_data.brItem, ptf[i].x, ptf[i].y, 8 * ps.dpi, true);

			if (p_data.dataMode == 1) {
				ps.hCanvas->DrawTextByColor(p_data.ftGen, FloatToWideStr(item->data), tfor,
					bleft - p_data.barS, item->ex.y - p_data.gth - 4 * ps.dpi, bleft + p_data.barW + p_data.barS, item->ex.y - 4 * ps.dpi, item->color);
			}
			else if (p_data.dataMode == 2) {
				std::wstring wtr = FloatToWideStr(item->per);
				wtr.append(L"%");
				ps.hCanvas->DrawTextByColor(p_data.ftGen, wtr.c_str(), tfor,
					bleft - p_data.barS, item->ex.y - p_data.gth - 4 * ps.dpi, bleft + p_data.barW + p_data.barS, item->ex.y - 4 * ps.dpi, item->color);
			}
		
			if (p_data.isVL)
			{
				ps.hCanvas->DrawTextByColor(p_data.ftGen, item->name, Left | Middle | Vertical, bleft - p_data.barS, p_data.frc.bottom + 4 * ps.dpi, 
					bleft + p_data.barW + p_data.barS, ps.uHeight, item->color);
				/*
				p_data.brItem->SetColor(item->color);
				ExMatrix3x2 matrix(
					1.0f, 0.52f,
					0.32f, 1.0f,
					0.0f, 0.0f
				);
				auto tranform = ExMatrix(matrix);
				ps.hCanvas->DrawTextEffect(p_data.brItem, p_data.ftGen, item->name, Left | SingleLine,
					bleft - p_data.barS + 15, p_data.frc.bottom - p_data.barS + 10 * ps.dpi,
					bleft + p_data.barW + p_data.barS + ps.uHeight, ps.uHeight, &tranform);
					*/
			}
		}


		delete[] ptf;
	}

}
//绘制饼图
void HHBUI::UIChart::paint_pie(ps_context ps)
{
	if (p_data.list.size() > 0) {

		//中心点
		POINTF ctf{
			p_data.frc.left + (p_data.frc.right - p_data.frc.left) / 2.f,
			p_data.frc.top + (p_data.frc.bottom - p_data.frc.top) / 2.f
		};
		float radius = (p_data.frc.right - p_data.frc.left) / 2.f;

		//累加起始角度
		float tAngle = -90.f;
		auto path = new UIPath;
		for (auto& item : p_data.list) {
			float sAngle = (item.per / 100) * 360;

			POINTF start{
				ctf.x + radius * cos(tAngle * 3.1415926f / 180),
				ctf.y + radius * sin(tAngle * 3.1415926f / 180)
			};
			tAngle += sAngle;
			POINTF end{
				ctf.x + radius * cos(tAngle * 3.1415926f / 180),
				ctf.y + radius * sin(tAngle * 3.1415926f / 180)
			};
			path->Reset();
			path->BeginPath();
			path->StartFigure(ctf.x, ctf.y);
			path->MoveTo(start.x, start.y);
			path->ArcTo(radius, radius, sAngle, sAngle > 180.f, true, end.x, end.y);
			path->FinishFigure(true);
			path->EndPath();

			p_data.brItem->SetColor(item.color);
			ps.hCanvas->FillPath(p_data.brItem, path);

			output(tAngle, sAngle);
		}
		delete path;
	}
}
