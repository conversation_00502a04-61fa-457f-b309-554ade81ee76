﻿/**
 * @file mem_dc.h
 * @brief 内存设备上下文
 */
#pragma once
namespace HHBUI
{
	/// 内存设备上下文
	struct ExMemDC
	{
		///宽度
		uint32_t width;

		///高度
		uint32_t height;
		
		///设备上下文句柄
		HDC dc;
		
		///位图句柄
		HBITMAP bitmap;

		///位图像素数据
		LPVOID bits;
	};

	/////////////////////////////////

	HRESULT ExMemDCCreate(uint32_t width, uint32_t height, ExMemDC* r_dc);
	BOOL ExMemDCCreate(HDC& offsetDc, LPVOID& offsetBmp, LPVOID& offsetBits, INT width, INT height);
	HRESULT ExMemDCDestroy(ExMemDC* dc);
	BOOL ExMemDCDestroy(HDC OffsetDc, LPVOID OffsetBmp, LPVOID OffsetBits);
	HRESULT ExMemDCResize(ExMemDC* dc, uint32_t new_width, uint32_t new_height);

}
