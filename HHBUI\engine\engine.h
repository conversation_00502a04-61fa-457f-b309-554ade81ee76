﻿#pragma once
namespace HHBUI
{
	// 引擎初始化信息
	struct info_Init
	{
		int device;
		HINSTANCE hInstance;
		FLOAT dwScaledpi;
		BOOL dwDebug;
		LPCWSTR default_font_Face;
		INT default_font_Size;
		DWORD default_font_Style;//参考 FontStyle
	};
	class TOAPI UIEngine
	{
	public:
		/**
		 * @brief 引擎初始化
		 * @param init_info 初始化信息
		 * @return 返回执行状态
		 */
		static HRESULT Init(info_Init* info = nullptr);

		/**
		 * @brief 引擎反初始化
		 * @return 返回执行状态
		 */
		static HRESULT UnInit();

		//引擎查询是否debug模式
		static BOOL QueryDebug();
		//引擎查询是否已经初始化
		static BOOL QueryInit();
		//计算DPI缩放值
		static FLOAT fScale(FLOAT n);
		//取DPI缩放系数
		static FLOAT GetDefaultScale();
		static FLOAT GetTime();
		static LPCWSTR GetVersion();
	};
}
