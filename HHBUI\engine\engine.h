﻿#pragma once
#include <memory>
#include <string>
#include <vector>
#include <chrono>
#include <atomic>
#include <mutex>
#include <string_view>
#include <functional>
#include <cmath>

namespace HHBUI
{
	// 现代化枚举类型
	enum class RenderDevice : int {
		Auto = -1,
		Default = 0,
		Hardware = 1,
		Software = 2
	};

	enum class FontStyle : uint32_t {
		Normal = 0,
		Bold = 1,
		Italic = 2,
		Underline = 4,
		StrikeOut = 8
	};

	// 现代化配置结构
	struct EngineConfig {
		RenderDevice device = RenderDevice::Auto;
		HINSTANCE instance = nullptr;
		float dpi_scale = 0.0f;
		bool debug_mode = false;

		struct FontConfig {
			std::wstring face_name = L"";
			int size = 14;
			FontStyle style = FontStyle::Normal;

			FontConfig() = default;
			FontConfig(const std::wstring& face, int sz, FontStyle st = FontStyle::Normal)
				: face_name(face), size(sz), style(st) {}
		} font;

		EngineConfig() = default;
		EngineConfig& with_device(RenderDevice dev) { device = dev; return *this; }
		EngineConfig& with_instance(HINSTANCE inst) { instance = inst; return *this; }
		EngineConfig& with_dpi_scale(float scale) { dpi_scale = scale; return *this; }
		EngineConfig& with_debug(bool debug) { debug_mode = debug; return *this; }
		EngineConfig& with_font(const FontConfig& font_cfg) { font = font_cfg; return *this; }
	};

	// 错误详情结构
	struct ErrorDetail {
		std::wstring component;
		std::wstring operation;
		std::wstring description;
		HRESULT hr_code = S_OK;
		std::chrono::system_clock::time_point timestamp;

		ErrorDetail(std::wstring comp, std::wstring op, std::wstring desc, HRESULT hr = S_OK)
			: component(std::move(comp)), operation(std::move(op)), description(std::move(desc))
			, hr_code(hr), timestamp(std::chrono::system_clock::now()) {}
	};

	// 向后兼容的旧结构体
	struct info_Init
	{
		int device;
		HINSTANCE hInstance;
		FLOAT dwScaledpi;
		BOOL dwDebug;
		LPCWSTR default_font_Face;
		INT default_font_Size;
		DWORD default_font_Style;//参考 FontStyle
	};
	class TOAPI UIEngine
	{
	public:
		// 删除构造函数，确保单例模式
		UIEngine() = delete;
		UIEngine(const UIEngine&) = delete;
		UIEngine& operator=(const UIEngine&) = delete;
		UIEngine(UIEngine&&) = delete;
		UIEngine& operator=(UIEngine&&) = delete;

		// 现代化结果类型
		enum class InitResult {
			Success,
			AlreadyInitialized,
			ComInitFailed,
			WinApiInitFailed,
			DrawContextInitFailed,
			FontInitFailed,
			DpiSetupFailed,
			ResourceSetupFailed,
			WindowClassRegFailed,
			UnknownError
		};

		struct InitStatus {
			InitResult result;
			std::wstring error_message;
			HRESULT hr_code = S_OK;
			std::vector<ErrorDetail> error_details;

			bool is_success() const noexcept { return result == InitResult::Success; }
			explicit operator bool() const noexcept { return is_success(); }

			void AddError(const ErrorDetail& detail) {
				error_details.push_back(detail);
			}

			std::wstring GetDetailedErrorMessage() const {
				if (error_details.empty()) {
					return error_message;
				}

				std::wstring detailed = error_message + L"\n详细错误信息:\n";
				for (const auto& detail : error_details) {
					detailed += L"- " + detail.component + L"::" + detail.operation +
							   L": " + detail.description + L"\n";
				}
				return detailed;
			}
		};

		// 现代化API
		[[nodiscard]] static InitStatus Initialize(const EngineConfig* config = nullptr) noexcept;
		[[nodiscard]] static InitStatus Shutdown() noexcept;
		[[nodiscard]] static bool IsDebugMode() noexcept;
		[[nodiscard]] static bool IsInitialized() noexcept;
		[[nodiscard]] static float ScaleValue(float value) noexcept;
		[[nodiscard]] static float GetDpiScale() noexcept;
		[[nodiscard]] static float GetElapsedTime() noexcept;
		[[nodiscard]] static std::wstring_view GetVersion() noexcept;

		// 批量缩放优化
		static void ScaleValues(float* values, size_t count) noexcept;

		// 引擎状态信息
		struct EngineInfo {
			bool initialized;
			bool debug_mode;
			float dpi_scale;
			RenderDevice current_device;
			std::wstring version;
			float elapsed_time;
		};
		[[nodiscard]] static EngineInfo GetEngineInfo() noexcept;

		// 错误历史记录
		[[nodiscard]] static std::vector<ErrorDetail> GetErrorHistory() noexcept;
		static void ClearErrorHistory() noexcept;

		// 向后兼容的旧API
		/**
		 * @brief 引擎初始化 (向后兼容)
		 * @param init_info 初始化信息
		 * @return 返回执行状态
		 */
		static HRESULT Init(info_Init* info = nullptr);

		/**
		 * @brief 引擎反初始化 (向后兼容)
		 * @return 返回执行状态
		 */
		static HRESULT UnInit();

		//引擎查询是否debug模式
		static BOOL QueryDebug();
		//引擎查询是否已经初始化
		static BOOL QueryInit();
		//计算DPI缩放值
		static FLOAT fScale(FLOAT n);
		//取DPI缩放系数
		static FLOAT GetDefaultScale();
		static FLOAT GetTime();
		static LPCWSTR GetVersion_Legacy();

	private:
		// 内部实现
		static InitStatus InitializeInternal(const EngineConfig& config) noexcept;
		static void CleanupResources() noexcept;
		static EngineConfig ConvertLegacyConfig(const info_Init* info) noexcept;
	};

	// DPI管理类
	class DpiManager {
	public:
		struct DpiInfo {
			float scale_x = 1.0f;
			float scale_y = 1.0f;
			float raw_dpi_x = USER_DEFAULT_SCREEN_DPI;
			float raw_dpi_y = USER_DEFAULT_SCREEN_DPI;
			bool is_high_dpi = false;

			float GetUniformScale() const noexcept {
				return std::max(scale_x, scale_y);
			}

			bool IsScaled() const noexcept {
				return scale_x != 1.0f || scale_y != 1.0f;
			}
		};

		[[nodiscard]] static DpiInfo GetSystemDpiInfo() noexcept;
		[[nodiscard]] static float CalculateOptimalScale(float user_scale = 0.0f) noexcept;
		[[nodiscard]] static float ScaleValue(float value, float scale) noexcept;
		[[nodiscard]] static float UnscaleValue(float scaled_value, float scale) noexcept;
	};

	// 引擎日志系统
	class EngineLogger {
	public:
		enum class LogLevel {
			Debug, Info, Warning, Error, Critical
		};

		static void SetDebugMode(bool enabled) noexcept;
		static void LogError(const ErrorDetail& detail) noexcept;
		static void Log(LogLevel level, const std::wstring& message) noexcept;
		static std::vector<ErrorDetail> GetErrorHistory() noexcept;
		static void ClearErrorHistory() noexcept;

	private:
		static inline std::vector<ErrorDetail> error_history_;
		static inline bool debug_enabled_ = false;
		static inline std::mutex log_mutex_;

		static void OutputErrorToDebug(const ErrorDetail& detail) noexcept;
		static std::wstring FormatLogMessage(LogLevel level, const std::wstring& message) noexcept;
	};

	// 性能优化的缩放计算
	class OptimizedScaling {
	public:
		[[nodiscard]] static inline float ScaleValue(float value) noexcept {
			if (!needs_scaling_.load(std::memory_order_relaxed)) {
				return value;
			}

			const float scale = GetCachedScale();
			return (scale > 1.0f) ? std::round(value * scale) : value;
		}

		static void ScaleValues(float* values, size_t count) noexcept;
		static void UpdateScale(float new_scale) noexcept;

	private:
		static inline float cached_scale_ = 1.0f;
		static inline bool scale_cached_ = false;
		static inline std::atomic<bool> needs_scaling_{false};

		[[nodiscard]] static inline float GetCachedScale() noexcept;
		static void ScaleValuesScalar(float* values, size_t count, float scale) noexcept;
	};

	// 高精度计时器
	class HighResolutionTimer {
	public:
		[[nodiscard]] static float GetElapsedTime() noexcept;
		static void Reset() noexcept;

	private:
		static inline std::chrono::steady_clock::time_point start_time_ =
			std::chrono::steady_clock::now();
		static inline std::atomic<bool> initialized_{false};
	};

	// 引擎状态管理
	class EngineState {
	public:
		static void SetInitialized(bool state) noexcept;
		static void SetDebugMode(bool state) noexcept;
		static void SetDpiScale(float scale) noexcept;
		static void SetCurrentDevice(RenderDevice device) noexcept;

		[[nodiscard]] static bool IsInitialized() noexcept;
		[[nodiscard]] static bool IsDebugMode() noexcept;
		[[nodiscard]] static float GetDpiScale() noexcept;
		[[nodiscard]] static RenderDevice GetCurrentDevice() noexcept;

	private:
		static inline std::atomic<bool> initialized_{false};
		static inline std::atomic<bool> debug_mode_{false};
		static inline std::atomic<float> dpi_scale_{1.0f};
		static inline std::atomic<RenderDevice> current_device_{RenderDevice::Auto};
	};

	// 初始化步骤枚举
	enum class InitStep {
		CheckState, ComInitialize, SystemDetection, DpiSetup, FontSetup,
		WinApiInit, DrawContextInit, ResourceSetup, WindowClassReg, Complete
	};

	// 辅助函数
	bool IsWin10OrLater() noexcept;
	void SetupDpiAwareness() noexcept;
	UIEngine::InitStatus SetupDpiScaling(float user_scale) noexcept;
}
