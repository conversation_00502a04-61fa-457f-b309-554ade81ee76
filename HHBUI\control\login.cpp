﻿#include "pch.h"
#include "hhbui.h"
#include "login.h"
#include <algorithm>

namespace HHBUI
{
    UILogin::UILogin(UIBase* hParent, INT x, INT y, INT width, INT height, INT dwStyle, INT dwStyleEx, INT nID)
    {
        InitSubControl(hParent, x, y, width, height, L"UILogin", NULL, dwStyle, dwStyleEx, nID, -1);

        // 默认设置为透明背景，避免遮挡父窗口的圆角
        SetColor(color_background, UIColor(0, 0, 0, 0));

        InitControls();
    }

    void UILogin::InitControls()
{
    // 计算合适的布局位置
    ExRectF rect;
    GetRect(rect);
    INT width = rect.right - rect.left;
    INT height = rect.bottom - rect.top;
    
    // 根据内容区宽度比例计算左右区域的分界线
    INT contentWidth = width * p_data.contentWidthRatio;
    INT leftWidth = width - contentWidth;

    // 创建左侧背景图像控件
    p_data.backgroundImage = new UIImageBox(this, 0, 0, leftWidth, height, NULL, 1000);
    ApplyImageFitMode(p_data.backgroundImage, p_data.bgFitMode);
    // 设置左侧背景图左上和左下圆角
    p_data.backgroundImage->SetRadius(p_data.windowRadius, 0, 0, p_data.windowRadius);
    
    // 创建右侧背景图像控件
    p_data.rightBgImage = new UIImageBox(this, leftWidth, 0, contentWidth, height, NULL, 1001);
    ApplyImageFitMode(p_data.rightBgImage, p_data.rightBgFitMode);
    // 设置右侧背景图右上和右下圆角
    p_data.rightBgImage->SetRadius(0, p_data.windowRadius, p_data.windowRadius, 0);

        // 创建LOGO图像控件
        p_data.logoImage = new UIImageBox(this, 0, 0, p_data.logoSize, p_data.logoSize, NULL, 1002);
        ApplyImageFitMode(p_data.logoImage, p_data.logoFitMode);

        // 创建标题
        p_data.titleLabel = new UIStatic(this, 0, 0, 280, 50, p_data.title.c_str(), 0, 0, 1003, Center | Middle);
        p_data.titleLabel->SetFontFromFamily(L"微软雅黑", 28, FontStyle::Bold);
        p_data.titleLabel->SetColor(color_text_normal, p_data.themeColor);

        // 创建副标题
        p_data.subTitleLabel = new UIStatic(this, 0, 0, 280, 24, L"", 0, 0, 1004, Center | Middle);
        p_data.subTitleLabel->SetFontFromFamily(L"微软雅黑", 14, FontStyle::Normal);
        p_data.subTitleLabel->SetColor(color_text_normal, UIColor(130, 130, 130, 255));
        p_data.subTitleLabel->Show(FALSE); // 默认隐藏

        // 设置输入框样式和尺寸
        INT editWidth = 280;
        INT editHeight = 45;

        // 创建用户名输入框 - 现代风格
        p_data.usernameEdit = new UIEdit(this, 0, 0, editWidth, editHeight, NULL, 0, 0, 1005, SingleLine | Center | Middle);
        p_data.usernameEdit->SetCueBanner(p_data.usernamePlaceholder.c_str(), UIColor(170, 170, 170, 255));
        p_data.usernameEdit->SetColor(color_border, p_data.inputBorderColor); // 使用定义的边框颜色
        p_data.usernameEdit->SetColor(color_background, UIColor(250, 250, 250, 255));
        p_data.usernameEdit->SetRadius(p_data.inputBorderRadius, p_data.inputBorderRadius, p_data.inputBorderRadius, p_data.inputBorderRadius);
        p_data.usernameEdit->SetFontFromFamily(L"微软雅黑", 14, FontStyle::Normal);
        
        // 为用户名输入框设置焦点事件
        p_data.usernameEdit->SetEvent(WMM_SETFOCUS, [](LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam) -> LRESULT {
            auto edit = (UIEdit*)UIView;
            auto login = (UILogin*)edit->GetParent();
            edit->SetColor(color_border, login->p_data.inputFocusBorderColor);
            return 0;
        });
        p_data.usernameEdit->SetEvent(WMM_KILLFOCUS, [](LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam) -> LRESULT {
            auto edit = (UIEdit*)UIView;
            auto login = (UILogin*)edit->GetParent();
            edit->SetColor(color_border, login->p_data.inputBorderColor);
            return 0;
        });

        // 创建密码输入框 - 现代风格
        p_data.passwordEdit = new UIEdit(this, 0, 0, editWidth, editHeight, NULL, eos_edit_usepassword, 0, 1006, SingleLine | Center | Middle);
        p_data.passwordEdit->SetCueBanner(p_data.passwordPlaceholder.c_str(), UIColor(170, 170, 170, 255));
        p_data.passwordEdit->SetColor(color_border, p_data.inputBorderColor); // 使用定义的边框颜色
        p_data.passwordEdit->SetColor(color_background, UIColor(250, 250, 250, 255));
        p_data.passwordEdit->SetRadius(p_data.inputBorderRadius, p_data.inputBorderRadius, p_data.inputBorderRadius, p_data.inputBorderRadius);
        p_data.passwordEdit->SetFontFromFamily(L"微软雅黑", 14, FontStyle::Normal);
        
        // 为密码输入框设置焦点事件
        p_data.passwordEdit->SetEvent(WMM_SETFOCUS, [](LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam) -> LRESULT {
            auto edit = (UIEdit*)UIView;
            auto login = (UILogin*)edit->GetParent();
            edit->SetColor(color_border, login->p_data.inputFocusBorderColor);
            return 0;
        });
        p_data.passwordEdit->SetEvent(WMM_KILLFOCUS, [](LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam) -> LRESULT {
            auto edit = (UIEdit*)UIView;
            auto login = (UILogin*)edit->GetParent();
            edit->SetColor(color_border, login->p_data.inputBorderColor);
            return 0;
        });

        // 创建记住密码复选框 - 现代风格
        p_data.rememberCheckbox = new UICheck(this, 0, 0, 95, 20, p_data.rememberMeText.c_str(), 0, 0, 1007);
        p_data.rememberCheckbox->SetColor(color_text_normal, UIColor(100, 100, 100, 255));
        p_data.rememberCheckbox->SetBoxColor(UIColor(220, 220, 220, 255), p_data.themeColor, UIColor(255, 255, 255, 255));
        p_data.rememberCheckbox->SetFontFromFamily(L"微软雅黑", 12, FontStyle::Normal);
        
        // 设置复选框大小和圆角
        p_data.rememberCheckbox->SetBoxSize(16);
        p_data.rememberCheckbox->SetRadius(4);

        // 创建忘记密码按钮 - 现代风格
        p_data.forgetPasswordButton = new UIButton(this, 0, 0, 80, 20, p_data.forgetPasswordText.c_str(), 0, 0, 1008);
        p_data.forgetPasswordButton->SetStyle(plain, primary);
        p_data.forgetPasswordButton->SetEvent(WMM_CLICK, OnForgetPasswordEvent);
        p_data.forgetPasswordButton->Show(FALSE); // 默认隐藏
        p_data.forgetPasswordButton->SetFontFromFamily(L"微软雅黑", 12, FontStyle::Normal);
        p_data.forgetPasswordButton->SetCrText(p_data.themeColor, p_data.themeColor, p_data.themeColor);

        // 设置按钮样式和尺寸
        INT buttonWidth = 280;
        INT buttonHeight = 48;

        // 创建登录按钮 - 现代风格
        p_data.loginButton = new UIButton(this, 0, 0, buttonWidth, buttonHeight, p_data.loginButtonText.c_str(), 0, 0, 1009);
        p_data.loginButton->SetStyle(fill, primary);
        p_data.loginButton->SetRadius(24); // 圆润按钮
        p_data.loginButton->SetEvent(WMM_CLICK, OnLoginButtonEvent);
        p_data.loginButton->SetFontFromFamily(L"微软雅黑", 16, FontStyle::Bold);

        // 创建注册按钮 - 现代风格
        p_data.registerButton = new UIButton(this, 0, 0, buttonWidth, buttonHeight, p_data.registerButtonText.c_str(), 0, 0, 1010);
        p_data.registerButton->SetStyle(plain, primary);
        p_data.registerButton->SetRadius(24); // 圆润按钮
        p_data.registerButton->SetEvent(WMM_CLICK, OnLoginButtonEvent);
        p_data.registerButton->SetFontFromFamily(L"微软雅黑", 14, FontStyle::Normal);

        // 更新控件布局
        UpdateLayout();
    }

    void UILogin::UpdateLayout()
    {
        // 添加空指针检查
        if (!p_data.titleLabel || !p_data.subTitleLabel || !p_data.usernameEdit ||
            !p_data.passwordEdit || !p_data.loginButton || !p_data.registerButton ||
            !p_data.rememberCheckbox || !p_data.forgetPasswordButton)
            return;

        // 计算合适的布局位置
        ExRectF rect;
        GetRect(rect);
        INT width = rect.right - rect.left;
        INT height = rect.bottom - rect.top;

        // 根据设置的内容区比例计算内容区宽度
        INT contentWidth = width * p_data.contentWidthRatio;
        INT contentLeft = width - contentWidth;

        // 设置左侧背景图像位置
        if (p_data.backgroundImage) {
            p_data.backgroundImage->SetPos(0, 0, contentLeft, height);
            // 确保圆角设置正确
            p_data.backgroundImage->SetRadius(p_data.windowRadius, 0, 0, p_data.windowRadius);
        }
        
        // 设置右侧背景图像位置
        if (p_data.rightBgImage) {
            p_data.rightBgImage->SetPos(contentLeft, 0, contentWidth, height);
            // 确保圆角设置正确
            p_data.rightBgImage->SetRadius(0, p_data.windowRadius, p_data.windowRadius, 0);
        }

        // 如果是手动布局模式，直接返回，让用户自己设置位置
        if (p_data.layoutMode == MANUAL_LAYOUT)
            return;

        // 自动布局模式 - 右侧内容区域
        // 内容区垂直居中
        INT contentHeight = 480; // 估算内容高度
        INT contentTop = (height - contentHeight) / 2;
        if (contentTop < 20) contentTop = 20; // 最小边距

        // 内容区水平居中
        INT contentCenterX = contentLeft + contentWidth / 2;
        INT contentPadding = 30; // 内容区边距

        // Logo位置 - 内容区顶部居中
        INT logoY = contentTop;
        INT logoX = contentCenterX;
        
        // 如果有Logo，设置它的位置
        if (p_data.logoImage) {
            p_data.logoImage->SetPos(logoX - p_data.logoSize/2, logoY, p_data.logoSize, p_data.logoSize);
            contentTop += p_data.logoSize + 20; // Logo下方留出空间
        }

        // 标题位置 - Logo下方
        INT titleWidth = 280;
        p_data.titleLabel->SetPos(contentCenterX - titleWidth/2, contentTop, titleWidth, 50);
        contentTop += 50;

        // 副标题位置 - 标题下方
        if (!p_data.subTitle.empty()) {
            p_data.subTitleLabel->Show(TRUE);
            p_data.subTitleLabel->SetPos(contentCenterX - titleWidth/2, contentTop, titleWidth, 24);
            contentTop += 34;
        }
        else {
            p_data.subTitleLabel->Show(FALSE);
        }

        contentTop += 20; // 输入框上方留出空间

        // 输入框区域
        INT editWidth = 280;
        INT editHeight = 45;

        // 用户名输入框位置
        p_data.usernameEdit->SetPos(contentCenterX - editWidth/2, contentTop, editWidth, editHeight);
        contentTop += editHeight + 20;

        // 密码输入框位置
        p_data.passwordEdit->SetPos(contentCenterX - editWidth/2, contentTop, editWidth, editHeight);
        contentTop += editHeight + 15;

        // 记住密码和忘记密码位置
        if (p_data.showRememberMe) {
            p_data.rememberCheckbox->Show(TRUE);
            p_data.rememberCheckbox->SetPos(contentCenterX - editWidth/2, contentTop, 95, 20);
        }
        else {
            p_data.rememberCheckbox->Show(FALSE);
        }

        if (p_data.showForgetPassword) {
            p_data.forgetPasswordButton->Show(TRUE);
            p_data.forgetPasswordButton->SetPos(contentCenterX + editWidth/2 - 80, contentTop, 80, 20);
        }
        else {
            p_data.forgetPasswordButton->Show(FALSE);
        }

        contentTop += 40; // 按钮上方留出空间

        // 登录按钮位置
        INT buttonWidth = 280;
        INT buttonHeight = 48;
        p_data.loginButton->SetPos(contentCenterX - buttonWidth/2, contentTop, buttonWidth, buttonHeight);
        contentTop += buttonHeight + 20;

        // 注册按钮位置
        if (p_data.showRegister) {
            p_data.registerButton->Show(TRUE);
            p_data.registerButton->SetPos(contentCenterX - buttonWidth/2, contentTop, buttonWidth, buttonHeight);
        }
        else {
            p_data.registerButton->Show(FALSE);
        }
    }

    LRESULT CALLBACK UILogin::OnLoginButtonEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
    {
        if (!UIView) return S_FALSE;  // 添加空指针检查

        auto loginControl = (UILogin*)((UIControl*)UIView)->GetParent();
        if (!loginControl) return S_FALSE;  // 添加空指针检查

        if (nCode == WMM_CLICK)
        {
            if (nID == 1009) // 登录按钮
            {
                if (loginControl->p_data.loginCallback)
                {
                    return loginControl->p_data.loginCallback(
                        loginControl->GetUsername(),
                        loginControl->GetPassword()
                    );
                }
            }
            else if (nID == 1010) // 注册按钮
            {
                if (loginControl->p_data.registerCallback)
                {
                    return loginControl->p_data.registerCallback();
                }
            }
        }

        return S_OK;
    }

    LRESULT CALLBACK UILogin::OnForgetPasswordEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
    {
        if (!UIView) return S_FALSE;  // 添加空指针检查

        auto loginControl = (UILogin*)((UIControl*)UIView)->GetParent();
        if (!loginControl) return S_FALSE;  // 添加空指针检查

        if (nCode == WMM_CLICK && nID == 1008) // 忘记密码按钮
        {
            if (loginControl->p_data.forgetPasswordCallback)
            {
                return loginControl->p_data.forgetPasswordCallback();
            }
        }

        return S_OK;
    }

    void UILogin::SetBackgroundFitMode(ImageFitMode mode)
    {
        p_data.bgFitMode = mode;
        if (p_data.backgroundImage) {
            ApplyImageFitMode(p_data.backgroundImage, mode);
            // 重新应用圆角设置，防止适配模式切换后丢失圆角
            p_data.backgroundImage->SetRadius(p_data.windowRadius, 0, 0, p_data.windowRadius);
        }
    }

    void UILogin::SetRightBackgroundFitMode(ImageFitMode mode)
    {
        p_data.rightBgFitMode = mode;
        if (p_data.rightBgImage) {
            ApplyImageFitMode(p_data.rightBgImage, mode);
            // 重新应用圆角设置，防止适配模式切换后丢失圆角
            p_data.rightBgImage->SetRadius(0, p_data.windowRadius, p_data.windowRadius, 0);
        }
    }

    void UILogin::SetLogoFitMode(ImageFitMode mode)
    {
        p_data.logoFitMode = mode;
        if (p_data.logoImage) {
            ApplyImageFitMode(p_data.logoImage, mode);
        }
    }
    
    void UILogin::ApplyImageFitMode(UIImageBox* imageBox, ImageFitMode mode)
    {
        if (!imageBox) return;
        
        // 根据模式设置图片盒子的绘制模式
        switch (mode)
        {
        case FILL:
            imageBox->SetFit(UIImageBox::image_fit::fill);
            break;
        case CONTAIN:
            imageBox->SetFit(UIImageBox::image_fit::contain);
            break;
        case COVER:
            imageBox->SetFit(UIImageBox::image_fit::cover);
            break;
        case CENTER:
            imageBox->SetFit(UIImageBox::image_fit::none);
            break;
        case TILE:
            // 没有对应的平铺模式，使用填充模式作为替代
            imageBox->SetFit(UIImageBox::image_fit::fill);
            break;
        default:
            imageBox->SetFit(UIImageBox::image_fit::cover);
        }
    }

    // 修改设置背景图像的方法，应用适配模式
    void UILogin::SetBackgroundImage(LPCWSTR lpszImagePath)
    {
        if (lpszImagePath && p_data.backgroundImage)
        {
            p_data.backgroundImage->SetImg(lpszImagePath);
            p_data.backgroundImage->Show(TRUE);
            ApplyImageFitMode(p_data.backgroundImage, p_data.bgFitMode);
        }
    }

    void UILogin::SetBackgroundImage(LPVOID pImgData, size_t size)
    {
        if (pImgData && size > 0 && p_data.backgroundImage)
        {
            p_data.backgroundImage->SetImg(pImgData, size);
            p_data.backgroundImage->Show(TRUE);
            ApplyImageFitMode(p_data.backgroundImage, p_data.bgFitMode);
        }
    }

    // 修改设置右侧背景图像的方法，应用适配模式
    void UILogin::SetRightBackgroundImage(LPCWSTR lpszImagePath)
    {
        if (lpszImagePath && p_data.rightBgImage)
        {
            p_data.rightBgImage->SetImg(lpszImagePath);
            p_data.rightBgImage->Show(TRUE);
            ApplyImageFitMode(p_data.rightBgImage, p_data.rightBgFitMode);
        }
    }

    void UILogin::SetRightBackgroundImage(LPVOID pImgData, size_t size)
    {
        if (pImgData && size > 0 && p_data.rightBgImage)
        {
            p_data.rightBgImage->SetImg(pImgData, size);
            p_data.rightBgImage->Show(TRUE);
            ApplyImageFitMode(p_data.rightBgImage, p_data.rightBgFitMode);
        }
    }

    void UILogin::SetRightBackgroundBlur(FLOAT fBlurRadius)
    {
        p_data.rightBgBlur = fBlurRadius;
        // 在OnPaintProc中应用模糊效果
    }

    void UILogin::SetRightBackgroundDark(FLOAT fDarkness)
    {
        p_data.rightBgDarkness = fDarkness;
        // 在OnPaintProc中应用暗化效果
    }

    void UILogin::SetLayoutMode(LayoutMode mode)
    {
        p_data.layoutMode = mode;
        UpdateLayout();
    }

    void UILogin::SetContentWidthRatio(FLOAT ratio)
    {
        // 确保比例在有效范围内
        ratio = std::max(0.2f, std::min(0.8f, ratio));
        p_data.contentWidthRatio = ratio;
        UpdateLayout();
    }
    
    void UILogin::SetForgetPasswordText(LPCWSTR lpszText)
    {
        if (lpszText)
        {
            p_data.forgetPasswordText = lpszText;
            if (p_data.forgetPasswordButton)
            {
                p_data.forgetPasswordButton->SetText(lpszText);
            }
        }
    }

    void UILogin::SetTitle(LPCWSTR lpszTitle)
    {
        if (lpszTitle)
        {
            p_data.title = lpszTitle;
            if (p_data.titleLabel)
            {
                p_data.titleLabel->SetText(lpszTitle);
            }
        }
    }

    void UILogin::SetSubTitle(LPCWSTR lpszSubTitle)
    {
        if (lpszSubTitle)
        {
            p_data.subTitle = lpszSubTitle;
            if (p_data.subTitleLabel)
            {
                p_data.subTitleLabel->SetText(lpszSubTitle);
                p_data.subTitleLabel->Show(wcslen(lpszSubTitle) > 0);
            }
            UpdateLayout();
        }
    }

    void UILogin::SetBackgroundBlur(FLOAT fBlurRadius)
    {
        p_data.backgroundBlur = fBlurRadius;
        // 在OnPaintProc中应用模糊效果
    }

    void UILogin::SetBackgroundDark(FLOAT fDarkness)
    {
        p_data.backgroundDarkness = fDarkness;
        // 在OnPaintProc中应用暗化效果
    }

    void UILogin::SetLogo(LPCWSTR lpszImagePath)
    {
        if (lpszImagePath && p_data.logoImage)
        {
            p_data.logoImage->SetImg(lpszImagePath);
            p_data.logoImage->Show(TRUE);
            ApplyImageFitMode(p_data.logoImage, p_data.logoFitMode);
            UpdateLayout();
        }
    }

    void UILogin::SetLogo(LPVOID pImgData, size_t size)
    {
        if (pImgData && size > 0 && p_data.logoImage)
        {
            p_data.logoImage->SetImg(pImgData, size);
            p_data.logoImage->Show(TRUE);
            ApplyImageFitMode(p_data.logoImage, p_data.logoFitMode);
            UpdateLayout();
        }
    }

    void UILogin::SetLogoSize(INT size)
    {
        p_data.logoSize = size;
        UpdateLayout();
    }

    void UILogin::SetUsernamePlaceholder(LPCWSTR lpszText)
    {
        if (lpszText)
        {
            p_data.usernamePlaceholder = lpszText;
            if (p_data.usernameEdit)
            {
                p_data.usernameEdit->SetCueBanner(lpszText);
            }
        }
    }

    void UILogin::SetPasswordPlaceholder(LPCWSTR lpszText)
    {
        if (lpszText)
        {
            p_data.passwordPlaceholder = lpszText;
            if (p_data.passwordEdit)
            {
                p_data.passwordEdit->SetCueBanner(lpszText);
            }
        }
    }

    void UILogin::SetLoginButtonText(LPCWSTR lpszText)
    {
        if (lpszText)
        {
            p_data.loginButtonText = lpszText;
            if (p_data.loginButton)
            {
                p_data.loginButton->SetText(lpszText);
            }
        }
    }

    void UILogin::SetRegisterButtonText(LPCWSTR lpszText)
    {
        if (lpszText)
        {
            p_data.registerButtonText = lpszText;
            if (p_data.registerButton)
            {
                p_data.registerButton->SetText(lpszText);
            }
        }
    }

    void UILogin::SetRememberMeText(LPCWSTR lpszText)
    {
        if (lpszText)
        {
            p_data.rememberMeText = lpszText;
            if (p_data.rememberCheckbox)
            {
                p_data.rememberCheckbox->SetText(lpszText);
            }
        }
    }

    void UILogin::ShowRegisterButton(BOOL bShow)
    {
        p_data.showRegister = bShow;
        UpdateLayout();
    }

    void UILogin::ShowRememberMe(BOOL bShow)
    {
        p_data.showRememberMe = bShow;
        UpdateLayout();
    }

    void UILogin::ShowForgetPassword(BOOL bShow)
    {
        p_data.showForgetPassword = bShow;
        UpdateLayout();
    }

    LPCWSTR UILogin::GetUsername()
    {
        return p_data.usernameEdit ? p_data.usernameEdit->GetText() : L"";
    }

    LPCWSTR UILogin::GetPassword()
    {
        return p_data.passwordEdit ? p_data.passwordEdit->GetText() : L"";
    }

    BOOL UILogin::GetRememberMe()
    {
        return p_data.rememberCheckbox && (p_data.rememberCheckbox->GetState() & state_checked);
    }

    void UILogin::SetLoginCallback(std::function<LRESULT(LPCWSTR, LPCWSTR)> callback)
    {
        p_data.loginCallback = callback;
    }

    void UILogin::SetRegisterCallback(std::function<LRESULT()> callback)
    {
        p_data.registerCallback = callback;
    }

    void UILogin::SetForgetPasswordCallback(std::function<LRESULT()> callback)
    {
        p_data.forgetPasswordCallback = callback;
    }

    void UILogin::SetThemeColor(UIColor primary)
    {
        p_data.themeColor = primary;
        
        // 更新标题颜色
        if (p_data.titleLabel)
            p_data.titleLabel->SetColor(color_text_normal, primary);

        // 更新登录按钮颜色
        if (p_data.loginButton) {
            UIColor darker1(primary);
            UIColor darker2(primary);
            darker1.SetColorLights(0.9f); // 暗化10%
            darker2.SetColorLights(0.8f); // 暗化20%
            
            p_data.loginButton->SetCrBkg(primary, darker1, darker2);
            p_data.loginButton->SetCrText(UIColor(255, 255, 255, 255), UIColor(255, 255, 255, 255), UIColor(255, 255, 255, 255));
        }

        // 更新注册按钮颜色
        if (p_data.registerButton) {
            UIColor darker1(primary);
            UIColor darker2(primary);
            darker1.SetColorLights(0.9f);
            darker2.SetColorLights(0.8f);
            
            p_data.registerButton->SetCrText(primary, darker1, darker2);
        }

        // 更新忘记密码按钮颜色
        if (p_data.forgetPasswordButton) {
            UIColor darker1(primary);
            UIColor darker2(primary);
            darker1.SetColorLights(0.9f);
            darker2.SetColorLights(0.8f);
            
            p_data.forgetPasswordButton->SetCrText(primary, darker1, darker2);
        }

        // 更新记住密码复选框颜色
        if (p_data.rememberCheckbox) {
            // 设置复选框颜色
            p_data.rememberCheckbox->SetBoxColor(UIColor(220, 220, 220, 255), primary, UIColor(255, 255, 255, 255));
        }
    }

    void UILogin::EnableLightEffect(BOOL bEnable)
    {
        p_data.enableLightEffect = bEnable;
    }

    void UILogin::SetInputBorderRadius(INT radius)
    {
        p_data.inputBorderRadius = radius;
        
        // 更新输入框圆角
        if (p_data.usernameEdit) {
            p_data.usernameEdit->SetRadius(radius, radius, radius, radius);
        }
        
        if (p_data.passwordEdit) {
            p_data.passwordEdit->SetRadius(radius, radius, radius, radius);
        }
    }

    void UILogin::SetWindowRadius(INT radius)
    {
        p_data.windowRadius = radius;
        
        // 更新背景图像圆角
        if (p_data.backgroundImage) {
            p_data.backgroundImage->SetRadius(radius, 0, 0, radius);
        }
        
        if (p_data.rightBgImage) {
            p_data.rightBgImage->SetRadius(0, radius, radius, 0);
        }
        
        // 重绘控件
        this->Redraw();
    }

    void UILogin::ShowSeparator(BOOL bShow)
{
    p_data.showSeparator = bShow;
    // 确保背景图的圆角设置正确
    if (p_data.backgroundImage) {
        p_data.backgroundImage->SetRadius(p_data.windowRadius, 0, 0, p_data.windowRadius);
    }
    if (p_data.rightBgImage) {
        p_data.rightBgImage->SetRadius(0, p_data.windowRadius, p_data.windowRadius, 0);
    }
    // 通过UIControl基类的Redraw方法重绘控件
    this->Redraw();
}

void UILogin::SetSeparatorStyle(SeparatorStyle style)
{
    p_data.separatorStyle = style;
    // 确保背景图的圆角设置正确
    if (p_data.backgroundImage) {
        p_data.backgroundImage->SetRadius(p_data.windowRadius, 0, 0, p_data.windowRadius);
    }
    if (p_data.rightBgImage) {
        p_data.rightBgImage->SetRadius(0, p_data.windowRadius, p_data.windowRadius, 0);
    }
    this->Redraw();
}

void UILogin::SetSeparatorColor(UIColor color)
{
    p_data.separatorColor = color;
    // 确保背景图的圆角设置正确
    if (p_data.backgroundImage) {
        p_data.backgroundImage->SetRadius(p_data.windowRadius, 0, 0, p_data.windowRadius);
    }
    if (p_data.rightBgImage) {
        p_data.rightBgImage->SetRadius(0, p_data.windowRadius, p_data.windowRadius, 0);
    }
    this->Redraw();
}

void UILogin::SetSeparatorWidth(INT width)
{
    p_data.separatorWidth = std::max(1, width); // 确保宽度至少为1像素
    // 确保背景图的圆角设置正确
    if (p_data.backgroundImage) {
        p_data.backgroundImage->SetRadius(p_data.windowRadius, 0, 0, p_data.windowRadius);
    }
    if (p_data.rightBgImage) {
        p_data.rightBgImage->SetRadius(0, p_data.windowRadius, p_data.windowRadius, 0);
    }
    this->Redraw();
}

    // 新增方法：设置输入框边框颜色
    void UILogin::SetInputBorderColor(UIColor color)
    {
        p_data.inputBorderColor = color;
        
        // 应用到用户名和密码输入框
        if (p_data.usernameEdit) {
            p_data.usernameEdit->SetColor(color_border, color);
        }
        
        if (p_data.passwordEdit) {
            p_data.passwordEdit->SetColor(color_border, color);
        }
    }

    // 新增方法：设置输入框聚焦时边框颜色
    void UILogin::SetInputFocusBorderColor(UIColor color)
    {
        p_data.inputFocusBorderColor = color;
        
        // 对于聚焦颜色，需要通过事件监听来实现，这里只是存储颜色值
    }

    LRESULT UILogin::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
    {
        if (uMsg == WM_SIZE)
        {
            UpdateLayout();
        }
        
        return UIControl::OnMsgProc(hWnd, uMsg, wParam, lParam);
    }

    void UILogin::OnPaintProc(ps_context ps)
    {
        // 在这里可以绘制特殊效果，比如左右两侧背景图的边界效果
        ExRectF rect;
        GetRect(rect);
        INT width = rect.right - rect.left;
        INT height = rect.bottom - rect.top;
        
        // 内容区宽度
        INT contentWidth = width * p_data.contentWidthRatio;
        INT contentLeft = width - contentWidth;
        
            // 创建圆角裁剪路径以确保背景不溢出圆角
    if (p_data.windowRadius > 0) {
        // 注意：由于HHBUI库没有直接的圆角裁剪路径API，我们将圆角效果依赖于UIImageBox自身的圆角设置
        // 在UpdateLayout方法中已经为背景图设置了正确的圆角属性
    }
        
        // 绘制分割线 - 现在分割线显示只依赖于showSeparator标志，不再依赖于光效
        if (p_data.showSeparator && p_data.separatorStyle != SeparatorStyle::NONE) {
            switch (p_data.separatorStyle) {
            case SeparatorStyle::SOLID: {
                // 实线分割线
                UIBrush separatorBrush(p_data.separatorColor);
                ps.hCanvas->FillRect(&separatorBrush, contentLeft, 0, contentLeft + p_data.separatorWidth, height);
                break;
            }
            case SeparatorStyle::DASHED: {
                // 虚线分割线
                UIBrush separatorBrush(p_data.separatorColor);
                const INT dashLength = 6;  // 虚线长度
                const INT gapLength = 4;   // 虚线间隔
                
                for (INT y = 0; y < height; y += (dashLength + gapLength)) {
                    INT dashHeight = std::min(dashLength, height - y);
                    ps.hCanvas->FillRect(&separatorBrush, contentLeft, y, 
                                       contentLeft + p_data.separatorWidth, y + dashHeight);
                }
                break;
            }
            case SeparatorStyle::GRADIENT: {
                // 渐变分割线 - 由于没有直接的渐变画刷API，我们使用多个矩形模拟渐变效果
                UIColor baseColor = p_data.separatorColor;
                
                // 渐变分为10个步骤
                const int gradientSteps = 10;
                const float widthPerStep = p_data.separatorWidth * 3.0f / gradientSteps;
                
                for (int i = 0; i < gradientSteps; i++) {
                    // 从左到右，透明度逐渐降低
                    float alpha = 1.0f - (float)i / gradientSteps;
                    UIColor stepColor = baseColor;
                    stepColor.SetA(stepColor.GetA() * alpha);
                    
                    UIBrush stepBrush(stepColor);
                    float x = contentLeft + (i * widthPerStep);
                    ps.hCanvas->FillRect(&stepBrush, x, 0, x + widthPerStep, height);
                }
                break;
            }
            default:
                break;
            }
        }

            // 由于没有使用绘图状态保存/恢复，此处不需要恢复操作

        UIControl::OnPaintProc(ps);
    }
}
