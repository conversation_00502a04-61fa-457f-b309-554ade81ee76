﻿#pragma once
/*徽章 badge 通常作为修饰用途而存在，它们本身细小而并不显眼，但掺杂在其它元素中就显得尤为突出了。
* by:来自群管理员[E.globulus]贡献
*/
namespace HHBUI
{
	class TOAPI UIBadge : public UIControl
	{
	public:
		enum badge_type {
			number,		//数字模式
			content,	//文本模式
			dot			//点模式（不显示文字）
		};
		UIBadge() = default;
		//显示内容通过title设置（绑定组件后可使用SetPadding设置偏移）
		UIBadge(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT nID = 0);
		/*
		 * @brief 设置背景和文本颜色
		 * @param  dwCrBkg  背景颜色
		 * @param  dwCrText 文本颜色
		 */
		void SetColor(UIColor dwCrBkg, UIColor dwCrText);
		//设置类型
		void SetBadgeType(badge_type type);

		//设置开启无内容时显示（开启后无内容时显示0，关闭则无内容时隐藏Badge，type=dot无效）
		void EnableShowZero(BOOL enable = FALSE);

		//置最大显示数字（超过该值时显示值+，如99+，0表示不限制）
		void SetMaxNumber(INT max = 0);
		//置显示数字（仅type=number时有效）
		void SetNumber(INT number = 0);

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		struct badge_s
		{
			badge_type type = badge_type::number;
			BOOL bszero = FALSE;
			INT number = 0, max = 99;
			ExRectF bind{};
			LPVOID BindUIView = nullptr;
			UIColor Color[2] = { UIColor(245, 88, 88, 255),UIColor(230,231,232, 255)};
		}p_data;
	};
}
