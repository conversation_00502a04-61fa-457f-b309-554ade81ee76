﻿#include "pch.h"
#include "font_pool.h"
#include "common/winapi.h"
#include <common/Exception.h>
namespace HHBUI
{
	// 字体文件流对象
	class ExFontFileStream : public ExUnknownImpl<IDWriteFontFileStream>
	{
	public:
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE(IDWriteFontFileStream);
		EX_DECLEAR_INTERFACE_END();

		ExFontFileStream(ExData* data) { m_data = *data; }

		virtual HRESULT __stdcall ReadFileFragment(void const** fragmentStart, UINT64 fileOffset,
			UINT64 fragmentSize, void** fragmentContext) override
		{
			*fragmentContext = nullptr;

			//在数据区内
			if (fileOffset < m_data.size && fragmentSize <= m_data.size - fileOffset)
			{
				*fragmentStart = m_data.data + fileOffset;
				return S_OK;
			}
			else
			{
				*fragmentStart = nullptr;
				return E_FAIL;
			}
		}

		virtual void __stdcall ReleaseFileFragment(void* fragmentContext) override
		{
		}
		virtual HRESULT __stdcall GetFileSize(UINT64* fileSize) override
		{
			*fileSize = m_data.size;
			return S_OK;
		}
		virtual HRESULT __stdcall GetLastWriteTime(UINT64* lastWriteTime) override
		{
			*lastWriteTime = 0;
			return E_NOTIMPL;
		}
	private:
		ExData m_data;
	};

	// 字体文件加载器对象
	class ExFontFileLoader : public ExUnknownImpl<IDWriteFontFileLoader>
	{
		friend ExLazySingleton<ExFontFileLoader>;

	public:
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE(IDWriteFontFileLoader);
		EX_DECLEAR_INTERFACE_END();

		virtual HRESULT __stdcall CreateStreamFromKey(void const* fontFileReferenceKey, UINT32 fontFileReferenceKeySize,
			IDWriteFontFileStream** fontFileStream) override
		{
			if (fontFileReferenceKey != nullptr && fontFileReferenceKeySize == sizeof(ExData))
			{
				ExData* data = (ExData*)fontFileReferenceKey;
				if (data->data != nullptr && data->size > 0)
				{
					*fontFileStream = new ExFontFileStream(data);
					return (*fontFileStream) ? S_OK : E_FAIL;
				}
			}
			return E_FAIL;
		}

	};

	// 字体文件枚举器对象
	class ExFontFileEnumerator : public ExUnknownImpl<IDWriteFontFileEnumerator>
	{
	public:
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE(IDWriteFontFileEnumerator);
		EX_DECLEAR_INTERFACE_END();


		ExFontFileEnumerator(IDWriteFactory* pFactory, ExData* aDatas, UINT cDatas)
			: m_factory(pFactory), m_data_list(aDatas), m_data_count(cDatas), m_current_file(nullptr), m_next_index(0)
		{
		}

		virtual HRESULT __stdcall MoveNext(BOOL* hasCurrentFile) override
		{
			HRESULT hr = S_OK;

			//释放当前字体文件
			SAFE_RELEASE(m_current_file);
			*hasCurrentFile = FALSE;

			//有下一个字体文件
			if (m_next_index < m_data_count)
			{
				ExData* data = m_data_list + m_next_index;
				if (data->data != nullptr && data->size > 0)
				{
					//创建字体文件流
					hr = m_factory->CreateCustomFontFileReference(data, sizeof(ExData),
						ExLazySingleton<ExFontFileLoader>::Instance(), &m_current_file);
					if (SUCCEEDED(hr))
					{
						*hasCurrentFile = TRUE;
						m_next_index++;
					}
				}
				else hr = E_FAIL;
			}

			return hr;
		}

		virtual HRESULT __stdcall GetCurrentFontFile(IDWriteFontFile** fontFile) override
		{
			*fontFile = m_current_file;
			if (m_current_file == nullptr) return E_FAIL;
			m_current_file->AddRef();
			return S_OK;
		}

	private:
		IDWriteFactory* m_factory;
		ExData* m_data_list;
		UINT m_data_count;
		IDWriteFontFile* m_current_file;
		UINT m_next_index;

	};

	// 字体集加载器对象
	class ExFontCollectionLoader : public ExUnknownImpl<IDWriteFontCollectionLoader>
	{
		friend ExLazySingleton<ExFontCollectionLoader>;

	public:
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE(IDWriteFontCollectionLoader);
		EX_DECLEAR_INTERFACE_END();

		virtual HRESULT __stdcall CreateEnumeratorFromKey(IDWriteFactory* factory, void const* collectionKey,
			UINT32 collectionKeySize, IDWriteFontFileEnumerator** fontFileEnumerator) override
		{
			if (!collectionKey) return E_FAIL;
			if (collectionKeySize % sizeof(ExData) != 0) return E_FAIL;

			*fontFileEnumerator = new ExFontFileEnumerator(factory, (ExData*)collectionKey, collectionKeySize / sizeof(ExData));

			return (*fontFileEnumerator) ? S_OK : E_FAIL;
		}

	};

	/////////////////////////////

	ExFontPoolD2D::ExFontPoolD2D()
	{
		//创建资源池
		throw_if_failed(
			ExResPoolCreate(sizeof(ExFontContextD2D),
				OnInitFontContext, OnFreeFontContext,
				&m_font_pool
			), L"创建字体池失败"
		);
		throw_if_failed(
			ExResPoolCreate(sizeof(ExFontFileContextD2D),
				OnInitFontFileContext, OnFreeFontFileContext,
				&m_file_pool
			), L"创建字体文件池失败"
		);

		//创建并注册字体文件加载器和字体集加载器
		throw_if_failed(
			UIDrawContext::ToList.dwrite_factory->RegisterFontFileLoader(
				ExLazySingleton<ExFontFileLoader>::GetInstance()
			), L"注册字体文件加载器失败"
		);

		throw_if_failed(
			UIDrawContext::ToList.dwrite_factory->RegisterFontCollectionLoader(
				ExLazySingleton<ExFontCollectionLoader>::GetInstance()
			), L"注册字体集加载器失败"
		);

		::GetUserDefaultLocaleName(m_local_name, _countof(m_local_name));
	}
	ExFontPoolD2D::~ExFontPoolD2D()
	{
		ZeroMemory(m_local_name, sizeof(m_local_name));

		//取消注册字体集加载器和字体文件加载器
		UIDrawContext::ToList.dwrite_factory->UnregisterFontCollectionLoader(
			ExLazySingleton<ExFontCollectionLoader>::Instance()
		);
		UIDrawContext::ToList.dwrite_factory->UnregisterFontFileLoader(
			ExLazySingleton<ExFontFileLoader>::Instance()
		);

		//释放字体集加载器和字体文件加载器
		ExLazySingleton<ExFontCollectionLoader>::ClearInstance(true);
		ExLazySingleton<ExFontFileLoader>::ClearInstance(true);
	}

	ExFontFileContextD2D* ExFontPoolD2D::LoadFontFile(LPVOID data, size_t size, LPCTSTR lpwzFontFace)
	{
		EXATOM key = ExAtom(lpwzFontFace);
		throw_if_failed(key != EXATOM_UNKNOWN, L"计算字体文件原子号失败");
		ExFontFileContextD2D* context = nullptr;
		throw_if_failed(
			m_file_pool->UseOrCreateItem(key, data, size, (LPARAM)this, 0, (void**)&context),
			L"获取字体文件上下文失败"
		);
		return context;
	}
	void ExFontPoolD2D::UnLoadFontFile(EXATOM atom)
	{
		throw_if_failed(m_file_pool->UnUseItem(atom), L"释放字体文件失败");
	}

	ExFontContextD2D* ExFontPoolD2D::CreateFontInfo(LOGFONTW* info)
	{
		throw_if_false(info != nullptr, E_INVALIDARG, L"字体信息为空");

		EXATOM key = ExAtomData(info, sizeof(LOGFONTW));
		throw_if_failed(key != EXATOM_UNKNOWN, L"计算字体原子号失败");

		ExFontContextD2D* context = nullptr;
		throw_if_failed(
			m_font_pool->UseOrCreateItem(key, info, 0, (LPARAM)this, 0, (void**)&context),
			L"获取字体上下文失败"
		);
		return context;
	}
	bool ExFontPoolD2D::DestroyFont(EXATOM atom)
	{
		HRESULT hr = m_font_pool->UnUseItem(atom);
		throw_if_failed(hr, L"释放字体失败");
		return hr == S_OK;
	}

	HRESULT ExFontPoolD2D::OnInitFontFileContext(IExResPool* pool, EXATOM key, const void* data, WPARAM wparam, LPARAM lparam, DWORD flags, void* r_res)
	{
		ExFontFileContextD2D* context = (ExFontFileContextD2D*)r_res;
		context->data = {};

		try
		{
			//复制数据块
			throw_if_failed(
				ExDataCopy(&context->data, (byte_t*)data, wparam),
				L"复制字体文件数据失败"
			);

			//创建字体集
			throw_if_failed(
				UIDrawContext::ToList.dwrite_factory->CreateCustomFontCollection(
					ExLazySingleton<ExFontCollectionLoader>::Instance(),
					&context->data, sizeof(ExData),
					&context->collection
				), L"创建字体集失败"
			);

			//返回
			context->atom = key;
			return S_OK;
		}
		catch_throw({ if (context->data.data) { ExDataFree(&context->data); } });
	}
	HRESULT ExFontPoolD2D::OnFreeFontFileContext(IExResPool* pool, EXATOM key, DWORD flags, void* res)
	{
		ExFontFileContextD2D* context = (ExFontFileContextD2D*)res;
		SAFE_RELEASE(context->collection);
		ExDataFree(&context->data);
		return S_OK;
	}
	HRESULT ExFontPoolD2D::OnInitFontContext(IExResPool* pool, EXATOM key, const void* data, WPARAM wparam, LPARAM lparam, DWORD flags, void* r_res)
	{
		ExFontContextD2D* context = (ExFontContextD2D*)r_res;
		LOGFONTW* info = (LOGFONTW*)data;

		ExFontPoolD2D* that = (ExFontPoolD2D*)lparam;
		throw_if_false(that, E_INVALIDARG, L"字体池指针为空");
		ExFontFileContextD2D* file_context = nullptr;
		EXATOM fkey = ExAtom((LPCWSTR)info->lfFaceName);
		auto factory = UIDrawContext::ToList.dwrite_factory;
		try
		{
			//如果是自定义字体则引用，拿到字体文件上下文
			that->m_file_pool->UseItem(fkey, (void**)&file_context);
		
			DWRITE_FONT_WEIGHT weight = (DWRITE_FONT_WEIGHT)info->lfWeight;
			DWRITE_FONT_STYLE style = info->lfItalic ? DWRITE_FONT_STYLE_ITALIC : DWRITE_FONT_STYLE_NORMAL;
			DWRITE_FONT_STRETCH stretch = DWRITE_FONT_STRETCH_NORMAL;
			//创建字体
			throw_if_failed(
				factory->CreateTextFormat(
					info->lfFaceName,
					file_context ? file_context->collection : nullptr,
					weight, style, stretch,
					(FLOAT)(-info->lfHeight),
					that->m_local_name,
					&context->font
				), L"创建字体失败"
			);
			context->LogFont = info;
			context->atom = key;
			return S_OK;
		}
		catch_throw({
			if (context->font) { context->font->Release(); }
			});
	}
	HRESULT ExFontPoolD2D::OnFreeFontContext(IExResPool* pool, EXATOM key, DWORD flags, void* res)
	{
		ExFontContextD2D* context = (ExFontContextD2D*)res;
		context->font->Release();
		EXATOM fkey = ExAtom((LPCWSTR)context->LogFont->lfFaceName);
		delete context->LogFont;
		ExFontPoolD2D::Instance()->m_file_pool->UnUseItem(fkey);
		return S_OK;
	}

}
