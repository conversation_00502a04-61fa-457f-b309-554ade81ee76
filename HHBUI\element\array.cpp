﻿#include "pch.h"
#include "array.h"
HHBUI::UIarray::UIarray()
{
}
HHBUI::UIarray::~UIarray()
{
    m_data.clear();
}

BOOL HHBUI::UIarray::empty(size_t nIndex)
{
    return nIndex >= m_data.size();
}

size_t HHBUI::UIarray::insert(size_t value, size_t index)
{
    if (index == 0 || index > m_data.size())
    {
        m_data.push_back(value); 
    }
    else
    {
        m_data.insert(m_data.begin() + index - 1, value);
    }
    return m_data.size(); 
}

BOOL HHBUI::UIarray::erase(size_t index)
{
    if (index == 0 || index > m_data.size())
        return FALSE;

    m_data.erase(m_data.begin() + index - 1);
    return TRUE;
}

BOOL HHBUI::UIarray::set(size_t index, size_t value)
{
    if (index == 0 || index > m_data.size())
        return FALSE;

    m_data[index - 1] = value;
    return TRUE;
}

size_t HHBUI::UIarray::get(size_t index)
{
    if (index == 0 || index > m_data.size())
        return 0;

    return m_data[index - 1];
}

void HHBUI::UIarray::setextra(size_t extra)
{
    m_extra = extra;
}

size_t HHBUI::UIarray::getextra()
{
    return m_extra;
}

BOOL HHBUI::UIarray::redefine(size_t size)
{
    if (m_data.size() != 0)
        return FALSE;

    m_data.resize(size); 
    return TRUE;
}

void HHBUI::UIarray::clear()
{
    m_data.clear();
}

size_t HHBUI::UIarray::size()
{
    return m_data.size();
}

size_t TOAPI HHBUI::UIarray::begin()
{
    return m_data.empty() ? 0 : m_data[0];
}

size_t TOAPI HHBUI::UIarray::end()
{
    return m_data.empty() ? 0 : m_data[m_data.size() - 1];
}

size_t HHBUI::UIarray::emum(LPVOID fun, size_t pvParam)
{
    if (m_data.empty() || fun == nullptr)
        return 0;
    int index = 1;
    for (auto it = m_data.begin(); it != m_data.end(); ++it) {
        if (((ArrayEnumPROC)fun)(this, index, *it, pvParam))
            return index;
        ++index;
    }
    return 0;
}

BOOL HHBUI::UIarray::sort(BOOL fDesc, ArrayComparePROC fun, size_t extra1, size_t extra2)
{
    if (m_data.empty())
        return FALSE;
    if (fun == 0)
    {
        //默认的迭代器来进行升降排序
        if (fDesc)
            std::sort(m_data.rbegin(), m_data.rend());
        else
            std::sort(m_data.begin(), m_data.end());
    }
    else
    {
        fun(this, fDesc, extra1, extra2);
    }
    return TRUE;
}

