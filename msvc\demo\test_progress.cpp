﻿#include "hhbui.h"
using namespace HHBUI;

void testprogress(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 870, 550, L"hello Progress", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);
	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto progress1 = new UIProgress(window, 30, 70, 20, 300, L"{{value}}%", 1401);
	progress1->SetPoint(UIProgress::progress_point::top);
	progress1->SetValue(40);
	auto progress2 = new UIProgress(window, 60, 70, 20, 300, L"{{value}}%", 1402);
	progress2->SetPoint(UIProgress::progress_point::bottom);
	progress2->SetValue(40);
	progress2->IsShwoText(FALSE);//可不显示进度文本
	auto progress3 = new UIProgress(window, 90, 70, 300, 20, L"{{value}}%", 1403);
	progress3->SetPoint(UIProgress::progress_point::left);
	progress3->SetValue(40);
	auto progress4 = new UIProgress(window, 90, 338, 300, 20, L"{{value}}%", 1404);
	progress4->SetPoint(UIProgress::progress_point::right);
	progress4->SetValue(40);
	//纵向
	auto progress5 = new UIProgress(window, 700, 70, 20, 300, L"{{value}}%", 1405);
	progress5->SetType(UIProgress::progress_type::line);
	progress5->SetBarRadius(0);
	progress5->SetPoint(UIProgress::progress_point::top);
	progress5->SetValue(40);
	auto progress6 = new UIProgress(window, 730, 70, 20, 300, L"{{value}}%", 1406);
	progress6->SetType(UIProgress::progress_type::line);
	progress6->SetBarRadius(0);
	progress6->SetPoint(UIProgress::progress_point::bottom);
	progress6->SetValue(40);
	progress6->IsShwoText(FALSE);

	auto progress7 = new UIProgress(window, 420, 70, 200, 20, L"{{value}}%", 1407);
	progress7->SetType(UIProgress::progress_type::line);
	progress7->SetBarRadius(0);
	progress7->SetPoint(UIProgress::progress_point::left);
	progress7->SetValue(40);
	progress7->IsShwoText(FALSE);
	auto progress8 = new UIProgress(window, 420, 338, 200, 20, L"{{value}}%", 1408);
	progress8->SetType(UIProgress::progress_type::line);
	progress8->SetBarRadius(0);
	progress8->SetPoint(UIProgress::progress_point::right);
	progress8->SetValue(40);

	auto progress9 = new UIProgress(window, 90, 428, 100, 100, L"环{{value}}%", 1409);
	progress9->SetType(UIProgress::progress_type::ring);
	progress9->SetLineWidth(16);
	progress9->SetValue(50);

	auto progress10 = new UIProgress(window, 230, 428, 100, 100, L"环{{value}}%", 1410);
	progress10->SetType(UIProgress::progress_type::ring_round);
	progress10->SetLineWidth(16);
	progress10->SetValue(60);
	progress10->SetBarColor(UIColor(208, 211, 217, 150), UIColor(255, 87, 34, 255));

	auto progress11 = new UIProgress(window, 370, 428, 100, 100, L"水波{{value}}%", 1411);
	progress11->SetType(UIProgress::progress_type::wave);
	progress11->SetValue(60);
	progress11->SetWaveWidth(150);
	progress11->SetWaveHeight(15);
	progress11->SetBarColor({}, UIColor(22, 183, 119, 200));
	progress11->SetColor(color_border, UIColor(89, 89, 91, 60));
	progress11->SetTimer(1000, 100);

	auto progress12 = new UIProgress(window, 510, 428, 100, 100, L"水波{{value}}%", 1412);
	progress12->SetType(UIProgress::progress_type::wave);
	progress12->SetValue(30);
	progress12->SetWaveWidth(130);
	progress12->SetWaveHeight(15);
	progress12->SetBarColor({}, UIColor(255, 184, 0, 200));
	progress12->SetColor(color_border, UIColor(89, 89, 91, 60));
	progress12->SetRadius(50, 50, 50, 50);
	progress12->SetWaveBubbleSpeed(0);
	progress12->SetTimer(1000, 100);

	auto progress13 = new UIProgress(window, 100, 100, 560, 220, L"水波{{value}}%", 1413);
	progress13->SetType(UIProgress::progress_type::wave);
	progress13->SetValue(40);
	progress13->SetWaveWidth(400);
	progress13->SetWaveBubbleSpeed(5);
	progress13->SetBarColor(UIColor(230, 231, 232, 255), UIColor(30, 159, 255, 200));
	progress13->SetColor(color_border, UIColor(89, 89, 91, 60));
	progress13->SetRadius(10, 10, 10, 10);
	progress13->SetTimer(1000, 50);

	window->Show();
	//window->MessageLoop();
}