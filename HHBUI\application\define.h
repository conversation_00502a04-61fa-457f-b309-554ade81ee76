﻿#pragma once
#include <functional>
using UIimage = LPVOID;
using UIbrush = LPVOID;
using UIzip = LPVOID;
using WndView = INT;
using ClsPROC = LRESULT(CALLBACK*)(HWND, INT, WPARAM, LPARAM);
using MsgPROC = LRESULT(CALLBACK*)(HWND, LPVOID, LPVOID, INT, INT, WPARAM, LPARAM);
using EventHandlerPROC = LRESULT(CALLBACK*)(LPVOID, LPVOID, INT, INT, WPARAM, LPARAM);
using ArrayComparePROC = void(CALLBACK*)(LPVOID, BOOL, size_t, size_t);
using AnimationPROC = void(CALLBACK*)(LPVOID, INT, BOOL, BOOL, DOUBLE, DOUBLE, DOUBLE, LONG_PTR, LONG_PTR, LONG_PTR, LONG_PTR);
using LayoutPROC = std::function<LRESULT(INT, WPARAM, LPARAM)>;
using EnumFileCallback = BOOL(CALLBACK*)(LPCTSTR, LPARAM);

struct HK_THUNK_DATA
{
	HWND hWnd;
	WNDPROC Proc;
	LPVOID dwData;
};
using ThunkPROC = LRESULT(CALLBACK*)(HK_THUNK_DATA*, UINT, WPARAM, LPARAM);
#ifdef HHBUIDLL_EXPORTS
#define TOAPI __declspec(dllexport)
#else
#define TOAPI
#endif // HHBUIDLL_EXPORTS
#define NOTHROW noexcept
#define MAYTHROW 
#define EXINTERFACE(iid)	interface __declspec(uuid(iid)) __declspec(novtable)
#define EXMETHOD virtual __declspec(nothrow)
#define __CALLINFO__	__FILEW__, __LINE__

#pragma region 枚举类型声明
#define EXENUM(name) struct _##name##_Enum_ {								\
	_##name##_Enum_() = delete;												\
	_##name##_Enum_(const _##name##_Enum_&) = delete;						\
	_##name##_Enum_(_##name##_Enum_&&) = delete;							\
	_##name##_Enum_& operator=(const _##name##_Enum_&) = delete;			\
	enum Values; };															\
using name = _##name##_Enum_::Values;										\
enum _##name##_Enum_::Values 												

#pragma region DebugString
template <class Ty>
static void pt(std::wstring& str, Ty v)
{
	str.append(std::to_wstring(v) + L" ");
}
static void pt(std::wstring& str, std::wstring s)
{
	str.append(s + L" ");
}
static void pt(std::wstring& str, const WCHAR* s)
{
	str.append(s);
	str.append(L" ");
}
static void pt(std::wstring& str, WCHAR* s)
{
	str.append(s);
	str.append(L" ");
}
static void pt(std::wstring& str, const char* s)
{
	std::wstring wstr(s, s + strlen(s));
	str.append(wstr);
	str.append(L" ");
}
template <class... T>
static void output(T... args)
{
	std::wstring str = L"";
	(pt(str, std::forward<T>(args)), ...);
	str.append(L"\r\n");
	OutputDebugStringW(str.c_str());
}
#pragma endregion
template<class Interface>
inline void SafeRelease(Interface*& pInterfaceToRelease) {
	if (pInterfaceToRelease != nullptr) {
		pInterfaceToRelease->Release();
		pInterfaceToRelease = nullptr;
	}
}