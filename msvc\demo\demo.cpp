﻿#include "demo.h"

using namespace HHBUI;
UIMenu* m_bMenu = nullptr;

// 控件名称数据
const std::vector<std::wstring> btnData = {
	L"Button",L"Static",<PERSON>"Layout",<PERSON>"Ani",<PERSON>"List",<PERSON>"Check",<PERSON>"Free", <PERSON>"Edit", <PERSON>"Menu",<PERSON>"Tabs",L"Progress",L"Slider",L"Groupbox",L"Hotkey",L"Page",L"Imagebox",L"ColorPicker",L"ComboBox",
	L"Treeview",L"Table", L"Loading",L"Knobs",L"Datebox",L"Toast",L"Emoji",L"Tour",L"MiniBlink",L"Chart",L"TimeLine",L"Segmented",L"Modal",L"SplashScreen",L"Login",L"WindowSnap"
};

INT APIENTRY wWinMain(_In_ HINSTANCE hInstance, _In_opt_ HINSTANCE hInstancePrev, _In_ LPWSTR wzCmd, _In_ INT nCmdShow)
{
	const int MAX_DEVICES = 10;
	UIDevice devices[MAX_DEVICES];
	int numDevices = 0, deviceIndex = 0;
	UIDrawContext::EnumRenderDevice(devices, numDevices);
	for (int i = 0; i < numDevices; i++)
	{
		deviceIndex = devices[i].deviceIndex;
		output(devices[i].deviceName, devices[i].deviceIndex);
	}
	info_Init lpConfigInfo{};
	lpConfigInfo.hInstance = hInstance;
	/*配置显卡设备：没有显卡？-1为默认自动选择*/
	lpConfigInfo.device = (numDevices == 0 ? -1 : devices[0].deviceIndex);
	//lpConfigInfo.device = deviceIndex;
	//lpConfigInfo.dwDebug = 1;
	//lpConfigInfo.default_font_Size = 15;
	//lpConfigInfo.default_font_Face = L"Maple Mono";
	//lpConfigInfo.dwScaledpi = 1.25;
	//lpConfigInfo.default_font_Face = L"AROW";
	if (UIEngine::Init(&lpConfigInfo) != S_OK)
		return 0;
	//testsegmented(0);
	//return 0;


	/*Let's get*/
	Demo_Code();

	/*You can also use XML format*/
	//Demo_xml();

	UIEngine::UnInit();
	return 0;
}
void Demo_Code()
{
	// 尝试加载资源DLL
	if (!ResDLL::LoadResourceDLL())
	{
		MessageBox(NULL, L"无法加载资源DLL，将使用本地图片资源", L"提示", MB_ICONINFORMATION);
	}

	// 创建启动窗口特效
	UISplashScreen splash;
	// 设置为0表示不自动关闭，需要手动调用Hide()
	splash.Show(L"HHBUI演示程序正在启动...",
		UIColor(30, 30, 30, 10),  // 背景颜色（更加透明）
		UIColor(255, 255, 255, 255), // 文字颜色（不透明）
		28,  // 字体大小
		0);  // 显示时间设为0，由程序自行控制关闭时机

	// 更新进度条
	splash.SetProgress(10);

	// 添加延时，确保启动窗口显示足够长的时间
	Sleep(500);

	std::wstring wstr(L"HhbuiSharp demo🙂 Version：");   INT dwWidth = 860, dwHeight = 550;
	wstr.append(UIEngine::GetVersion());
	auto window = new UIWnd(0, 0, dwWidth, dwHeight, wstr.c_str(), 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX | UISTYLE_BTN_MENU |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_BTN_SETTING | UISTYLE_BTN_SKIN | UISTYLE_BTN_HELP, 0, NULL, 0, OnWndMsgProc);

	// 更新进度条
	splash.SetProgress(30);
	splash.UpdateText(L"正在初始化界面元素...");

	// 添加延时
	Sleep(300);

	window->SetBackgColor(UIColor(253, 253, 255, 255));
	//window->SetBorderColor(UIColor(121, 125, 122, 255));
	//window->SetShadowColor(UIColor(72, 76, 79, 155));
	//window->SetMinTrackSize(860, 500);
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);
	//window->SetAlpha(100);

	window->SetBlur(3.5f, TRUE);
	LPVOID imgdata;
	size_t retSize = 0;
	UIreadFile(LR"(icons\\IMG_1236.JPG)", imgdata, retSize);
	window->SetBackgImage(imgdata, retSize, 0, 0, bir_default, 0, bif_disablescale);

	// 更新进度条
	splash.SetProgress(50);

	// 添加延时
	Sleep(300);

	info_objcaption Info{};
	Info.crBkg = UIColor(16, 124, 16, 155);
	Info.crTitle = UIColor(255, 255, 255, 255);
	Info.crbutton_normal = UIColor(255, 255, 255, 255);
	Info.crbutton_hover = UIColor(255, 255, 255, 255);
	Info.crbutton_down = UIColor(200, 200, 200, 255);
	Info.dwTextFormat = Center | Middle | EndEllipsis;
	window->SetCaptionInfo(&Info);

	// 更新进度条
	splash.SetProgress(70);
	splash.UpdateText(L"正在加载控件...");

	// 添加延时
	Sleep(300);

	auto FlexBox = new UIWrapPanel(window, 0, 0, 100, 60, eos_scroll_controlbutton | eos_scroll_v);
	FlexBox->Lock(10, 70, 10, 70);
	//FlexBox->SetColor(color_background, UIColor(L"#DBEBB0"));
	FlexBox->SetLayoutType(elt_flow);
	FlexBox->SetMargin(-1, -1, 20, 20);
	FlexBox->SetSize(100, 60);
	for (int i = 0; i < (int)btnData.size(); ++i) {

		auto btn = new UIButton(window, 20, 70, 100, 60, btnData[i].c_str(), 0, 0, 1000 + i, SingleLine | Center | Middle | EndEllipsis);
		std::wstring dstImg = L"icons\\mian\\" + std::to_wstring(i) + L".png";
		btn->SetIcon(new UIImage(dstImg.c_str()), 1);
		btn->SetEvent(WMM_CLICK, OnButtonEvent);
		FlexBox->AddChild(btn);
	}

	// 更新进度条
	splash.SetProgress(90);
	splash.UpdateText(L"准备完毕，即将显示主窗口...");

	// 添加延时
	Sleep(300);

	/*测试自定义字体*/

	/*
	LPVOID fontdata;
	size_t fontSize = 0;
	UIreadFile(LR"(D:\Code\Font\汉仪游园体W.ttf)", fontdata, fontSize);
	output(UIFont::LoadFromMem(fontdata, fontSize, L"汉仪游园体"));
	btn8->SetFontFromFamily(L"汉仪游园体", 30, HHBUI::FontStyle::Bold);
	*/

	//UIImage* dstImg = nullptr;
	//window->ToImage(&dstImg);
	//output((int)dstImg);
	auto bstatic = new UIStatic(window, 20, 470, 400, 40, NULL, 0, 0, 100200, Middle);
	bstatic->SetEvent(WMM_TIMER, OnButtonEvent);
	bstatic->SetPadding(10, 0);
	bstatic->Lock(20, -1, -1, 20);
	bstatic->SetColor(color_background, UIColor(230, 231, 232, 155));
	bstatic->SetColor(color_border, UIColor(194, 195, 201, 255));
	bstatic->SetTimer(1001, 100);
	auto bCheck = new UICheck(bstatic, 280, 10, 130, 20, L"即时渲染模式");
	bCheck->SetEvent(WMM_CHECK, OnButtonEvent);

	// 更新进度条到100%
	splash.SetProgress(100);

	// 添加延时，确保进度条显示到100%
	Sleep(500);

	//window->SetRenderMode(TRUE);

	// 先显示主窗口
	window->Show();

	// 确保主窗口已经显示后再隐藏启动窗口
	// 处理窗口消息，让主窗口有时间显示
	MSG msg;
	PeekMessage(&msg, NULL, 0, 0, PM_NOREMOVE);

	// 再隐藏启动窗口
	splash.Hide();

	/*
	* 菜单逻辑：菜单是HOOK系统的菜单 系统支持什么特性基本都支持
	* 支持快捷键、用\t启用快捷键(包括组合) 后面是命令 自动识别
	* 支持制表符
	* 支持转义前缀符 (&转义为下划线)
	* 支持图标、可创建任何格式图标 尺寸建议16x16
	* 支持背景、文本颜色自定义 在弹出时设置
	* 支持独立事件回调 可同时接送消息，包括处理自定义背景等绘制
	* 支持添加文本uFlags标志;参考：https://learn.microsoft.com/zh-cn/windows/win32/api/winuser/nf-winuser-appendmenuw
	*/
	//创建子菜单
	auto bMenu = new UIMenu();
	bMenu->Append(MF_STRING, 2000, L"名称");
	std::wstring xml = LR"(<svg t="1717416270923" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21186"><path d="M896 128a64 64 0 0 1 64 64v384a64 64 0 0 1-64 64h-256a64 64 0 0 1-64-64V192a64 64 0 0 1 64-64h256z m0 64h-256v384h256V192z" fill="#d81e06" p-id="21187"></path><path d="M64 704h64v192H64zM896 704h64v192h-64z" fill="#d81e06" p-id="21188"></path><path d="M64 768h832v64H64z" fill="#d81e06" p-id="21189"></path><path d="M256 128a192 192 0 0 1 192 192v128a192 192 0 1 1-384 0V320a192 192 0 0 1 192-192z m0 64a128 128 0 0 0-127.68 118.4L128 320v128a128 128 0 0 0 255.68 9.6L384 448V320a128 128 0 0 0-128-128z" fill="#d81e06" p-id="21190"></path></svg>)";
	auto hpng1 = new UIImage(xml.c_str(), TRUE, 16, 16);
	bMenu->Append(MF_STRING, 2001, L"大小", hpng1);
	bMenu->Append(MF_STRING, 2002, L"项目类型");
	bMenu->Append(MF_STRING, 2003, L"修改日期");
	//创建主菜单
	m_bMenu = new UIMenu();
	xml = LR"(<svg t="1717415967502" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12984"><path d="M387.925333 128A81.493333 81.493333 0 0 1 469.333333 209.408v178.517333A81.493333 81.493333 0 0 1 387.925333 469.333333H209.365333A81.493333 81.493333 0 0 1 128 387.925333V209.408A81.493333 81.493333 0 0 1 209.365333 128h178.56z m426.666667 0A81.493333 81.493333 0 0 1 896 209.408v178.517333A81.493333 81.493333 0 0 1 814.592 469.333333h-178.56A81.493333 81.493333 0 0 1 554.666667 387.925333V209.408A81.493333 81.493333 0 0 1 636.032 128h178.56z m-426.666667 426.666667A81.493333 81.493333 0 0 1 469.333333 636.032v178.602667A81.493333 81.493333 0 0 1 387.925333 896H209.365333A81.493333 81.493333 0 0 1 128 814.634667v-178.602667A81.493333 81.493333 0 0 1 209.365333 554.666667h178.56zM725.333333 855.808A130.645333 130.645333 0 0 0 855.808 725.333333 130.602667 130.602667 0 0 0 725.333333 594.858667 130.602667 130.602667 0 0 0 594.858667 725.333333 130.645333 130.645333 0 0 0 725.333333 855.808zM725.333333 554.666667c94.122667 0 170.666667 76.544 170.666667 170.666666 0 94.08-76.544 170.666667-170.666667 170.666667s-170.666667-76.586667-170.666666-170.666667c0-94.122667 76.544-170.666667 170.666666-170.666666z" fill="#387AC9" p-id="12985"></path></svg>)";

	auto hpng2 = new UIImage(xml.c_str(), TRUE, 16, 16);
	m_bMenu->Append(MF_STRING, 1000, L"查看", hpng2);
	m_bMenu->Append(MF_POPUP, (size_t)bMenu, L"排序方式");//绑定子菜单
	m_bMenu->Append(MF_DISABLED, 1001, L"刷新");
	m_bMenu->Append(MF_SEPARATOR, 1002);
	m_bMenu->Append(MF_STRING, 1003, L"撤销 移动\tCtrl+&Z");

	xml = LR"(<svg t="1717416116125" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15858"><path d="M622.6944 0h-409.6c-56.32 0-101.888 46.08-101.888 102.4l-0.512 819.2c0 56.32 45.568 102.4 101.888 102.4h614.912c56.32 0 102.4-46.08 102.4-102.4V307.2l-307.2-307.2z m102.4 819.2h-409.6v-102.4h409.6v102.4z m0-204.8h-409.6v-102.4h409.6v102.4z m-153.6-256V76.8l281.6 281.6h-281.6z" fill="#30BA78" p-id="15859"></path></svg>)";
	auto hpng3 = new UIImage(xml.c_str(), TRUE, 16, 16);
	m_bMenu->Append(MF_STRING, 1004, L"新建", hpng3);
	m_bMenu->Append(MF_SEPARATOR, 1005);
	m_bMenu->Append(MF_STRING, 1006, L"显示设置");
	m_bMenu->Append(MF_STRING, 1007, L"个性化\tCtrl+&T");
	m_bMenu->Append(MF_SEPARATOR, 1008);
	m_bMenu->Append(MF_STRING, 1009, L"在终端中打开");
	m_bMenu->Append(MF_SEPARATOR, 1010);
	m_bMenu->Append(MF_STRING, 1011, L"Hello, HHBUI Menu test1");

	window->MessageLoop();
}
//控件事件
LRESULT CALLBACK OnButtonEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	if (nCode == WMM_CLICK)
	{
		// 创建控件测试函数表
		typedef void (*TestFunc)(HWND);
		const std::map<std::wstring, TestFunc> testFuncs = {
			{L"Button", testbutton},
			{L"Static", teststatic},
			{L"Layout", testlayout},
			{L"Ani", testanimation},
			{L"List", testlist},
			{L"Check", testcheck},
			{L"Free", testfree},
			{L"Edit", testedit},
			{L"Menu", testmenu},
			{L"Tabs", testtabs},
			{L"Progress", testprogress},
			{L"Slider", testslider},
			{L"Groupbox", testgroupbox},
			{L"Hotkey", testhotkey},
			{L"Page", testpage},
			{L"Imagebox", testimagebox},
			{L"ColorPicker", testcolorpicker},
			{L"ComboBox", testcombobox},
			{L"Treeview", testtreeview},
			{L"Table", testtable},
			{L"Loading", testloading},
			{L"Knobs", testknobs},
			{L"Datebox", testdatebox},
			{L"Toast", testtoast},
			{L"Emoji", testemoji},
			{L"Tour", testtour},
			{L"MiniBlink", testminiblink},
			{L"Chart", testchart},
			{L"TimeLine", testtimeline},
			{L"Segmented", testsegmented},
			{L"Modal", testmodal},
			{L"SplashScreen", testsplashscreen},
			{L"Login", testlogin},
			{L"WindowSnap", testwindowsnap}
		};
		auto it = testFuncs.find(btnData[nID - 1000]);
		if (it != testFuncs.end())
		{
			it->second(window->GethWnd());
		}
	}
	else if (nCode == WMM_EASING)
	{
		if (nID == 1002)
		{
			auto but = (UIButton*)UIView;
			auto easing = (info_Animation*)lParam;
			if (easing->nIsEnd)
				but->SetText(L"测试缓动");
			else
			{
				std::wstring tmp = L"任务[" + std::to_wstring(easing->nIndex) + L"]进度:" + std::to_wstring(easing->nProgress);
				but->SetText(tmp.c_str());
			}
			but->Move(easing->nCurrentX, easing->nCurrentY, CW_USEDEFAULT, CW_USEDEFAULT, TRUE);
		}
	}
	else if (nCode == WMM_LUP)
	{
		if (nID == 1001)
		{
			auto but = (UIButton*)UIView;
			delete but;
			/*注意：销毁控件必须用放开事件、不能用单击事件、由于单击后内部还会执行放开相关处理造成内存已经失效崩溃
			* 事件回调用【WMM_LUP】
			* 消息回调用【WM_LBUTTONUP】
			*/
		}
	}
	else if (nCode == WMM_TIMER)
	{
		if (nID == 100200 && wParam == 1001)
		{
			auto but = (UIStatic*)UIView;
			UINT curfps = window->GetLastFPS();
			auto fps = vstring::format(L"App: %.2f ms/frame (%.f FPS)", 1000.0f / curfps, 1.f * curfps);
			but->SetText(fps.c_str());
		}
	}
	else if (nCode == WMM_CHECK)
	{
		//auto but = (UICheck*)UIView;

		output(wParam);
		window->SetRenderMode(wParam);

	}
	return S_OK;
}
//窗口消息回调
LRESULT CALLBACK OnWndMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;//注意这里是取基类 不属于某个控件
	if (obj)
	{
		if (uMsg == WM_CLOSE)
		{
			if (obj->GetID() == UISTYLE_BTN_CLOSE)
			{
				//if (window->PopupMsg(L"确定要关闭吗？", L"提示：", MB_YESNO | MB_ICONQUESTION) != IDYES)
				//	return S_FALSE;
			}
		}
		else if (uMsg == WM_LBUTTONUP)
		{
			if (obj)
			{
				if (obj->GetID() == UISTYLE_BTN_MENU)
				{
					m_bMenu->Popup(window);
				}
			}
		}
		else if (uMsg == WM_COMMAND)
		{
			output(L"菜单：", wParam);
		}
	}
	return S_OK;
}

//控件消息回调
LRESULT CALLBACK OnViewMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;//注意这里是取基类 不属于某个控件
	if (uMsg == WM_LBUTTONDOWN)
	{

	}

	return S_OK;
}