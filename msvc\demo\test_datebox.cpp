﻿#include "hhbui.h"

using namespace HHBUI;
LRESULT CALLBACK OnDatebox_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (nCode == WMM_DBN_DATETIME)
	{
		output(wParam, L"年", LOWORD(lParam), L"月", HIWORD(lParam), L"日");
	}
	return S_OK;
}
void testdatebox(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 700, 500, L"hello Datebox", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_BTN_HELP | UISTYLE_MOVEABLE, hWnd);

	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto bDate = new UIDatebox(window, 20, 70, 160, 30, eos_date_defaultline);
	bDate->SetEvent(WMM_DBN_DATETIME, OnDatebox_Event);
	bDate->SetColor(color_border, UIColor(L"#cccedb"));

	auto bDaterd = new UIDatebox(window, 220, 70, 160, 30, eos_date_allcomma);
	bDaterd->SetEvent(WMM_DBN_DATETIME, OnDatebox_Event);
	bDaterd->SetColor(color_border, UIColor(L"#cccedb"));
	bDaterd->SetRadius(15, 15, 15, 15);
	bDaterd->SetDate(2009, 10, 1);


	window->Show();
	//window->MessageLoop();
}