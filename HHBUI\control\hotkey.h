﻿#pragma once
//Copyright from SOUI
namespace HHBUI
{
	enum HotkeyMod
	{
		HotkeyMod_None,
		HotkeyMod_Alt,
		HotkeyMod_Ctrl,
		HotkeyMod_Shift = 4,
	};
	enum HotkeyS
	{
		Mod_SC = HotkeyMod_Shift | HotkeyMod_Ctrl,
		Mod_SA = HotkeyMod_Shift | HotkeyMod_Alt,
		Mod_CA = HotkeyMod_Ctrl | HotkeyMod_Alt,
		Mod_SCA = HotkeyMod_Shift | HotkeyMod_Ctrl | HotkeyMod_Alt,
	};
	class TOAPI UIHotkey : public UIControl
	{
	public:
		UIHotkey() = default;
		UIHotkey(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
		//设置组合热键文本 组合键尽量统一小写 如“ctrl+f”
		void SetkeyW(LPCWSTR pszAccelKey);
		//设置组合热键
		void SetkeyW(INT vKey, INT wModifier);
		//获取Key值
		INT Getkey();
		//获取组合键值
		INT GetModifier();

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		void UpdateModifier();
		DWORD TranslateAccelKey(LPCWSTR pszAccelKey);
		WORD VkFromString(LPCWSTR pszKey);

		struct hotkey_s
		{
			WORD wModifier = 0, wVK = 0, wInvalidComb = 0, wInvalidModifier = 0; /*< 对无效组合键的替换方案,默认方案 */
			BOOL bInSetting = FALSE; /*< 正在设置中 */
		}p_data;
	};
}
