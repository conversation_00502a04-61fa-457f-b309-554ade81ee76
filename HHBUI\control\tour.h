﻿#pragma once
namespace HHBUI
{
	class TOAPI UITour : public UIControl
	{
	protected:
		struct FRAME {
			LPCWSTR tip;
			BOOL bUnion;
			ExRectF rc[4];
		};
	public:
		UITour() = default;
		UITour(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = NULL, INT nID = 0, INT dwStyle = 0, INT dwStyleEx = 0,
			INT textFormat = DT_LEFT | DT_VCENTER | DT_SINGLELINE);
		/*
		* @brief 添加提示
		* @param  tip      提示内容
		* @param  bUnion   是否联合
		*/
		void AddFrame(LPCWSTR tip, BOOL bUnion = FALSE, ExRectF rc1 = {}, ExRectF rc2 = {}, ExRectF rc3 = {}, ExRectF rc4 = {});
		void Step(int n);

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;

		struct tour_s
		{
			UIarray* pArray = nullptr;
			int nStep = 0, cx = 0, cy = 0;
		}p_data;
	};
}
