﻿/**
** =====================================================================================
**
**       文件名称: render_api.h
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】高级渲染引擎API接口定义 - 现代化DirectX11渲染框架 （声明文件）
**
**       主要功能:
**       - 高性能DirectX11渲染管线接口系统
**       - 现代化着色器管理与编译框架
**       - 智能缓冲区与纹理资源管理
**       - 高级渲染状态与管线配置
**       - 跨平台渲染抽象层设计
**       - COM接口规范与引用计数管理
**       - 渲染性能监控与调试支持
**
**       技术特性:
**       - 采用现代C++17标准与COM接口规范
**       - DirectX11硬件加速渲染管线
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能着色器编译与缓存系统
**       - 多线程安全的渲染资源管理
**       - 实时渲染性能监控与统计
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 创建现代化DirectX11渲染API接口
**                             2. 实现COM接口规范与引用计数
**                             3. 添加高级着色器管理系统
**                             4. 支持智能缓冲区与纹理管理
**                             5. 集成渲染状态与管线配置
**                             6. 添加性能监控与调试接口
**                             7. 确保跨平台兼容性设计
**
** =====================================================================================
**/

#pragma once
#include "engine/object_api.h"
#include <d3d11.h>
#include <d2d1_1.h>
#include <dwrite.h>
#include <guiddef.h>
#include <wrl/client.h>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <atomic>
#include <mutex>
#include <optional>
#include <string_view>
#include <algorithm>
#include <execution>

// 确保PURE宏定义
#ifndef PURE
#define PURE = 0
#endif

namespace HHBUI
{
	// 常量定义
	#ifndef E_TIMEOUT
	#define E_TIMEOUT _HRESULT_TYPEDEF_(0x80070102L)
	#endif

	// 接口ID定义
	DEFINE_GUID(IID_IRenderBatchManager, 0xA1B2C3D4, 0xE5F6, 0x7890, 0xAB, 0xCD, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC);
	DEFINE_GUID(IID_IRenderStateCache, 0xB2C3D4E5, 0xF6A7, 0x8901, 0xBC, 0xDE, 0x23, 0x45, 0x67, 0x89, 0x0D, 0xEF);
	DEFINE_GUID(IID_IMemoryPool, 0xC3D4E5F6, 0xA7B8, 0x9012, 0xCD, 0xEF, 0x34, 0x56, 0x78, 0x90, 0x12, 0x34);
	DEFINE_GUID(IID_IGPUResourcePool, 0xD4E5F6A7, 0xB8C9, 0x0123, 0xDE, 0xF4, 0x45, 0x67, 0x89, 0x01, 0x23, 0x45);
	DEFINE_GUID(IID_IMemoryPoolManager, 0xE5F6A7B8, 0xC9D0, 0x1234, 0xEF, 0x56, 0x56, 0x78, 0x90, 0x12, 0x34, 0x56);
	DEFINE_GUID(IID_IAsyncResourceLoader, 0xF6A7B8C9, 0xD0E1, 0x2345, 0x67, 0x89, 0xAB, 0xCD, 0xEF, 0x01, 0x23, 0x45);
	DEFINE_GUID(IID_IResourceCache, 0xA7B8C9D0, 0xE1F2, 0x3456, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0x12, 0x34, 0x56);
	DEFINE_GUID(IID_IStreamingLoader, 0xB8C9D0E1, 0xF2A3, 0x4567, 0x89, 0xAB, 0xCD, 0xEF, 0x01, 0x23, 0x45, 0x67);
	DEFINE_GUID(IID_IResourceLifecycleManager, 0xC9D0E1F2, 0xA3B4, 0x5678, 0x9A, 0xBC, 0xDE, 0xF0, 0x12, 0x34, 0x56, 0x78);
	DEFINE_GUID(IID_IRenderCommandQueue, 0xD0E1F2A3, 0xB4C5, 0x6789, 0xAB, 0xCD, 0xEF, 0x01, 0x23, 0x45, 0x67, 0x89);
	DEFINE_GUID(IID_IParallelRenderPipeline, 0xE1F2A3B4, 0xC5D6, 0x789A, 0xBC, 0xDE, 0xF0, 0x12, 0x34, 0x56, 0x78, 0x9A);
	DEFINE_GUID(IID_ITaskScheduler, 0xF2A3B4C5, 0xD6E7, 0x89AB, 0xCD, 0xEF, 0x01, 0x23, 0x45, 0x67, 0x89, 0xAB);
	DEFINE_GUID(IID_IWorkerThreadPool, 0xA3B4C5D6, 0xE7F8, 0x9ABC, 0xDE, 0xF0, 0x12, 0x34, 0x56, 0x78, 0x9C, 0xDE);

	// 前向声明
	class IRenderManager;
	class IBuffer;
	class ITexture;
	class IShader;
	class IRenderBatchManager;
	class IRenderStateCache;
	class IMemoryPool;
	class IGPUResourcePool;
	class IMemoryPoolManager;
	class IAsyncResourceLoader;
	class IResourceCache;
	class IStreamingLoader;
	class IResourceLifecycleManager;
	class IRenderCommandQueue;
	class IParallelRenderPipeline;
	class ITaskScheduler;
	class IWorkerThreadPool;
	// 前向声明
	class UIShader;
	class UIBuffer;
	class UITexture;
	class UIRenderState;

	// 接口前向声明
	struct IBuffer;
	struct IShader;
	struct ITexture;
	struct IRenderState;
	struct IRenderManager;
	struct IRenderFactory;

	// 接口IID定义
	DEFINE_GUID(IID_IRenderObject, 0x5C75E0DD, 0x96FD, 0x4902, 0xA3, 0xE0, 0x98, 0x65, 0x4C, 0xAB, 0x12, 0xCE);
	DEFINE_GUID(IID_IShader, 0xA1B2C3D4, 0xE5F6, 0x7890, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x90);
	DEFINE_GUID(IID_IBuffer, 0xB2C3D4E5, 0xF617, 0x8901, 0xBC, 0xDE, 0xF2, 0x34, 0x56, 0x78, 0x90, 0x12);
	DEFINE_GUID(IID_ITexture, 0xC3D4E5F6, 0xA7B8, 0x9012, 0xCD, 0xEF, 0x34, 0x56, 0x78, 0x90, 0x12, 0x34);
	DEFINE_GUID(IID_IRenderState, 0xD4E5F6A7, 0xB8C9, 0x0123, 0xDE, 0xFA, 0x45, 0x67, 0x89, 0x01, 0x23, 0x45);
	DEFINE_GUID(IID_IRenderManager, 0xE5F6A7B8, 0xC9D0, 0x1234, 0xEF, 0xAB, 0x56, 0x78, 0x90, 0x12, 0x34, 0x56);
	DEFINE_GUID(IID_IRenderFactory, 0xF6A7B8C9, 0xD0E1, 0x2345, 0xFA, 0xBC, 0x67, 0x89, 0x01, 0x23, 0x45, 0x67);

	/// 渲染器类型枚举
	enum class RenderType : uint32_t
	{
		D2D_ONLY = 0,      // 仅使用D2D渲染
		D3D_ONLY = 1,      // 仅使用D3D渲染
		HYBRID = 2,        // 混合渲染模式
		GDI_COMPAT = 3     // GDI兼容模式
	};

	/// 着色器类型枚举
	enum class ShaderType : uint32_t
	{
		VERTEX = 0,
		PIXEL = 1,
		GEOMETRY = 2,
		HULL = 3,
		DOMAIN_SHADER = 4,
		COMPUTE = 5
	};

	/// 缓冲区类型枚举
	enum class BufferType : uint32_t
	{
		VERTEX = 0,
		INDEX = 1,
		CONSTANT = 2,
		STRUCTURED = 3,
		STAGING = 4
	};

	/// 渲染统计信息
	struct RenderStats
	{
		uint32_t draw_calls;           // 绘制调用次数
		uint32_t triangles;            // 三角形数量
		uint32_t vertices;             // 顶点数量
		uint32_t instances;            // 实例数量
		uint64_t gpu_memory_used;      // GPU内存使用量
		float frame_time_ms;           // 帧时间(毫秒)
		float gpu_time_ms;             // GPU时间(毫秒)

		// 资源绑定统计
		uint32_t vertex_buffer_binds;  // 顶点缓冲区绑定次数
		uint32_t index_buffer_binds;   // 索引缓冲区绑定次数
		uint32_t shader_binds;         // 着色器绑定次数
		uint32_t texture_binds;        // 纹理绑定次数
		uint32_t render_state_binds;   // 渲染状态绑定次数

		// 渲染操作统计
		uint32_t clear_calls;          // 清除调用次数
		uint32_t present_calls;        // 呈现调用次数
		uint32_t state_changes;        // 状态切换次数
		uint32_t resource_uploads;     // 资源上传次数
		uint32_t resource_downloads;   // 资源下载次数

		// 构造函数，初始化所有成员为0
		RenderStats()
			: draw_calls(0), triangles(0), vertices(0), instances(0)
			, gpu_memory_used(0), frame_time_ms(0.0f), gpu_time_ms(0.0f)
			, vertex_buffer_binds(0), index_buffer_binds(0), shader_binds(0)
			, texture_binds(0), render_state_binds(0), clear_calls(0)
			, present_calls(0), state_changes(0), resource_uploads(0)
			, resource_downloads(0)
		{}
	};

	/// 渲染器对象基础接口
	EXINTERFACE("5C75E0DD-96FD-4902-A3E0-98654CAB12CE") IRenderObject : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 获取渲染器类型
		EXMETHOD RenderType GetRenderType() const PURE;

		/// 获取渲染统计信息
		EXMETHOD const RenderStats& GetRenderStats() const PURE;

		/// 重置渲染统计
		EXMETHOD void ResetRenderStats() PURE;
	};

	/// 着色器接口
	EXINTERFACE("A1B2C3D4-E5F6-7890-ABCD-EF1234567890") IShader : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 编译着色器
		EXMETHOD HRESULT Compile(LPCWSTR source_code, LPCSTR entry_point, LPCSTR target) PURE;

		/// 从文件加载着色器
		EXMETHOD HRESULT LoadFromFile(LPCWSTR file_path, LPCSTR entry_point, LPCSTR target) PURE;

		/// 绑定着色器到渲染管线
		EXMETHOD HRESULT Bind() PURE;

		/// 解绑着色器
		EXMETHOD void Unbind() PURE;

		/// 获取着色器类型
		EXMETHOD ShaderType GetShaderType() const PURE;

		/// 设置常量缓冲区
		EXMETHOD HRESULT SetConstantBuffer(uint32_t slot, IBuffer* buffer) PURE;
	};

	/// 缓冲区接口
	EXINTERFACE("B2C3D4E5-F617-8901-BCDE-F23456789012") IBuffer : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 创建缓冲区
		EXMETHOD HRESULT Create(BufferType type, uint32_t size, const void* initial_data = nullptr,
			bool dynamic = false, bool cpu_access = false) PURE;

		/// 更新缓冲区数据
		EXMETHOD HRESULT UpdateData(const void* data, uint32_t size, uint32_t offset = 0) PURE;

		/// 映射缓冲区内存
		EXMETHOD HRESULT Map(void** mapped_data, bool read_only = false) PURE;

		/// 解除映射
		EXMETHOD void Unmap() PURE;

		/// 绑定到渲染管线
		EXMETHOD HRESULT Bind(uint32_t slot) PURE;

		/// 获取缓冲区大小
		EXMETHOD uint32_t GetSize() const PURE;

		/// 获取缓冲区类型
		EXMETHOD BufferType GetBufferType() const PURE;
	};

	/// 纹理接口
	EXINTERFACE("C3D4E5F6-A7B8-9012-CDEF-345678901234") ITexture : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 创建2D纹理
		EXMETHOD HRESULT Create2D(uint32_t width, uint32_t height, DXGI_FORMAT format,
			const void* initial_data = nullptr, bool render_target = false, bool shader_resource = true) PURE;

		/// 从文件加载纹理
		EXMETHOD HRESULT LoadFromFile(LPCWSTR file_path) PURE;

		/// 绑定为着色器资源
		EXMETHOD HRESULT BindAsShaderResource(uint32_t slot) PURE;

		/// 绑定为渲染目标
		EXMETHOD HRESULT BindAsRenderTarget() PURE;

		/// 获取纹理尺寸
		EXMETHOD void GetSize(uint32_t* width, uint32_t* height) const PURE;

		/// 获取纹理格式
		EXMETHOD DXGI_FORMAT GetFormat() const PURE;
	};

	/// 渲染状态接口
	EXINTERFACE("D4E5F6A7-B8C9-0123-DEFA-456789012345") IRenderState : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 设置混合状态
		EXMETHOD HRESULT SetBlendState(bool enable, D3D11_BLEND src_blend = D3D11_BLEND_SRC_ALPHA,
			D3D11_BLEND dest_blend = D3D11_BLEND_INV_SRC_ALPHA) PURE;

		/// 设置深度模板状态
		EXMETHOD HRESULT SetDepthStencilState(bool depth_enable, bool depth_write_enable = true,
			D3D11_COMPARISON_FUNC depth_func = D3D11_COMPARISON_LESS) PURE;

		/// 设置光栅化状态
		EXMETHOD HRESULT SetRasterizerState(D3D11_CULL_MODE cull_mode = D3D11_CULL_BACK,
			D3D11_FILL_MODE fill_mode = D3D11_FILL_SOLID, bool scissor_enable = false) PURE;

		/// 应用所有状态
		EXMETHOD HRESULT Apply() PURE;
	};

	/// 高级渲染管理器接口
	EXINTERFACE("E5F6A7B8-C9D0-1234-EFAB-567890123456") IRenderManager : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化渲染管理器
		EXMETHOD HRESULT Initialize(RenderType render_type, HWND hwnd = nullptr) PURE;

		/// 关闭渲染管理器
		EXMETHOD void Shutdown() PURE;

		/// 开始帧渲染
		EXMETHOD HRESULT BeginFrame() PURE;

		/// 结束帧渲染
		EXMETHOD HRESULT EndFrame() PURE;

		/// 呈现到屏幕
		EXMETHOD HRESULT Present(bool vsync = true) PURE;

		/// 创建着色器
		EXMETHOD HRESULT CreateShader(ShaderType type, IShader** shader) PURE;

		/// 创建缓冲区
		EXMETHOD HRESULT CreateBuffer(BufferType type, IBuffer** buffer) PURE;

		/// 创建纹理
		EXMETHOD HRESULT CreateTexture(ITexture** texture) PURE;

		/// 创建渲染状态
		EXMETHOD HRESULT CreateRenderState(IRenderState** render_state) PURE;

		/// 设置视口
		EXMETHOD HRESULT SetViewport(float x, float y, float width, float height,
			float min_depth = 0.0f, float max_depth = 1.0f) PURE;

		/// 清除渲染目标
		EXMETHOD HRESULT ClearRenderTarget(float r = 0.0f, float g = 0.0f, float b = 0.0f, float a = 1.0f) PURE;

		/// 清除深度缓冲区
		EXMETHOD HRESULT ClearDepthStencil(float depth = 1.0f, uint8_t stencil = 0) PURE;

		/// 绘制图元
		EXMETHOD HRESULT Draw(uint32_t vertex_count, uint32_t start_vertex = 0) PURE;

		/// 绘制索引图元
		EXMETHOD HRESULT DrawIndexed(uint32_t index_count, uint32_t start_index = 0, uint32_t base_vertex = 0) PURE;

		/// 绘制实例化索引图元
		EXMETHOD HRESULT DrawIndexedInstanced(uint32_t index_count, uint32_t instance_count,
											 uint32_t start_index = 0, uint32_t base_vertex = 0,
											 uint32_t start_instance = 0) PURE;

		/// 设置顶点缓冲区
		EXMETHOD HRESULT SetVertexBuffer(uint32_t slot, void* buffer, uint32_t stride, uint32_t offset = 0) PURE;

		/// 设置索引缓冲区
		EXMETHOD HRESULT SetIndexBuffer(void* buffer, DXGI_FORMAT format, uint32_t offset = 0) PURE;

		/// 设置着色器
		EXMETHOD HRESULT SetShader(void* shader) PURE;

		/// 设置纹理
		EXMETHOD HRESULT SetTexture(uint32_t slot, void* texture) PURE;

		/// 清除渲染目标（重载版本，接受纹理参数）
		EXMETHOD HRESULT ClearRenderTarget(void* render_target, const float color[4]) PURE;

		/// 创建2D纹理
		EXMETHOD HRESULT CreateTexture2D(uint32_t width, uint32_t height, DXGI_FORMAT format,
										 ITexture** texture) PURE;

		/// 从文件加载纹理
		EXMETHOD HRESULT LoadTextureFromFile(const wchar_t* file_path, ITexture** texture) PURE;

		/// 获取D3D11设备
		EXMETHOD ID3D11Device* GetD3D11Device() const PURE;

		/// 获取D3D11设备上下文
		EXMETHOD ID3D11DeviceContext* GetD3D11DeviceContext() const PURE;

		/// 获取D2D设备上下文
		EXMETHOD ID2D1DeviceContext* GetD2D1DeviceContext() const PURE;

		/// 启用/禁用调试模式
		EXMETHOD void SetDebugMode(bool enable) PURE;

		/// 获取GPU内存使用情况
		EXMETHOD uint64_t GetGPUMemoryUsage() const PURE;
	};

	/// 渲染工厂接口
	EXINTERFACE("F6A7B8C9-D0E1-2345-FABC-678901234567") IRenderFactory : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 创建渲染管理器
		EXMETHOD HRESULT CreateRenderManager(IRenderManager** render_manager) PURE;

		/// 获取支持的渲染类型
		EXMETHOD uint32_t GetSupportedRenderTypes() const PURE;

		/// 检查功能支持
		EXMETHOD bool IsFeatureSupported(LPCWSTR feature_name) const PURE;
	};

	/// 全局函数声明

	/// 创建渲染工厂
	HRESULT CreateRenderFactory(IRenderFactory** factory);

	/// 获取默认渲染管理器
	IRenderManager* GetDefaultRenderManager();

	/// 设置默认渲染管理器
	void SetDefaultRenderManager(IRenderManager* manager);

	// =====================================================================================
	// 智能批处理系统 - C++17实现
	// =====================================================================================

	/// 渲染批次类型
	enum class BatchType : uint32_t
	{
		GEOMETRY = 0,       // 几何体批次
		SPRITE = 1,         // 精灵批次
		TEXT = 2,           // 文本批次
		UI_ELEMENT = 3,     // UI元素批次
		EFFECT = 4,         // 特效批次
		COUNT
	};

	/// 渲染状态哈希 - 用于快速状态比较
	struct RenderStateHash
	{
		uint64_t shader_hash{0};
		uint64_t texture_hash{0};
		uint64_t blend_state_hash{0};
		uint64_t depth_state_hash{0};
		uint64_t rasterizer_state_hash{0};

		constexpr bool operator==(const RenderStateHash& other) const noexcept
		{
			return shader_hash == other.shader_hash &&
				   texture_hash == other.texture_hash &&
				   blend_state_hash == other.blend_state_hash &&
				   depth_state_hash == other.depth_state_hash &&
				   rasterizer_state_hash == other.rasterizer_state_hash;
		}

		constexpr bool operator!=(const RenderStateHash& other) const noexcept
		{
			return !(*this == other);
		}
	};

	/// 顶点数据结构 - 支持不同类型的顶点
	struct UIVertex
	{
		float position[3];
		float normal[3];
		float texcoord[2];
		uint32_t color;

		constexpr UIVertex() noexcept : position{0,0,0}, normal{0,0,1}, texcoord{0,0}, color{0xFFFFFFFF} {}
		constexpr UIVertex(float x, float y, float z, float u, float v, uint32_t c = 0xFFFFFFFF) noexcept
			: position{x,y,z}, normal{0,0,1}, texcoord{u,v}, color{c} {}
	};

	/// 精灵顶点结构
	struct SpriteVertex
	{
		float position[2];
		float texcoord[2];
		uint32_t color;

		constexpr SpriteVertex() noexcept : position{0,0}, texcoord{0,0}, color{0xFFFFFFFF} {}
		constexpr SpriteVertex(float x, float y, float u, float v, uint32_t c = 0xFFFFFFFF) noexcept
			: position{x,y}, texcoord{u,v}, color{c} {}
	};

	/// 实例化数据结构
	struct InstanceData
	{
		float world_matrix[16];  // 4x4 世界变换矩阵
		uint32_t color_tint;     // 颜色调制
		float custom_data[4];    // 自定义数据

		constexpr InstanceData() noexcept
			: world_matrix{1,0,0,0, 0,1,0,0, 0,0,1,0, 0,0,0,1}
			, color_tint{0xFFFFFFFF}
			, custom_data{0,0,0,0} {}
	};

	/// 渲染批次数据
	struct RenderBatch
	{
		BatchType type{BatchType::GEOMETRY};
		RenderStateHash state_hash{};
		uint32_t vertex_count{0};
		uint32_t index_count{0};
		uint32_t instance_count{1};

		// 资源引用 - 使用原始指针（接口定义后可改为智能指针）
		IShader* shader{nullptr};
		IBuffer* vertex_buffer{nullptr};
		IBuffer* index_buffer{nullptr};
		IBuffer* instance_buffer{nullptr};
		ITexture* texture{nullptr};

		// 渲染参数
		D3D11_PRIMITIVE_TOPOLOGY topology{D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST};
		uint32_t start_vertex{0};
		uint32_t start_index{0};
		uint32_t start_instance{0};

		// 深度排序键 - 用于透明物体排序
		float depth_key{0.0f};

		// 批次优先级 - 用于渲染顺序控制
		uint32_t priority{0};

		// 性能统计 - 使用普通类型以支持拷贝操作
		mutable uint32_t draw_calls{0};
		mutable uint32_t triangles{0};

		// 批次合并标记
		bool can_merge{true};
		bool is_instanced{false};
		bool is_transparent{false};

		// 构造函数
		RenderBatch() = default;
		RenderBatch(BatchType t, const RenderStateHash& hash)
			: type(t), state_hash(hash) {}

		// 拷贝构造函数
		RenderBatch(const RenderBatch& other)
			: type(other.type)
			, state_hash(other.state_hash)
			, vertex_count(other.vertex_count)
			, index_count(other.index_count)
			, instance_count(other.instance_count)
			, shader(other.shader)
			, vertex_buffer(other.vertex_buffer)
			, index_buffer(other.index_buffer)
			, instance_buffer(other.instance_buffer)
			, texture(other.texture)
			, topology(other.topology)
			, start_vertex(other.start_vertex)
			, start_index(other.start_index)
			, start_instance(other.start_instance)
			, depth_key(other.depth_key)
			, priority(other.priority)
			, draw_calls(other.draw_calls)
			, triangles(other.triangles)
			, can_merge(other.can_merge)
			, is_instanced(other.is_instanced)
			, is_transparent(other.is_transparent)
		{}

		// 拷贝赋值操作符
		RenderBatch& operator=(const RenderBatch& other)
		{
			if (this != &other) {
				type = other.type;
				state_hash = other.state_hash;
				vertex_count = other.vertex_count;
				index_count = other.index_count;
				instance_count = other.instance_count;
				shader = other.shader;
				vertex_buffer = other.vertex_buffer;
				index_buffer = other.index_buffer;
				instance_buffer = other.instance_buffer;
				texture = other.texture;
				topology = other.topology;
				start_vertex = other.start_vertex;
				start_index = other.start_index;
				start_instance = other.start_instance;
				depth_key = other.depth_key;
				priority = other.priority;
				draw_calls = other.draw_calls;
				triangles = other.triangles;
				can_merge = other.can_merge;
				is_instanced = other.is_instanced;
				is_transparent = other.is_transparent;
			}
			return *this;
		}

		// 移动构造函数
		RenderBatch(RenderBatch&& other) noexcept
			: type(other.type)
			, state_hash(other.state_hash)
			, vertex_count(other.vertex_count)
			, index_count(other.index_count)
			, instance_count(other.instance_count)
			, shader(other.shader)
			, vertex_buffer(other.vertex_buffer)
			, index_buffer(other.index_buffer)
			, instance_buffer(other.instance_buffer)
			, texture(other.texture)
			, topology(other.topology)
			, start_vertex(other.start_vertex)
			, start_index(other.start_index)
			, start_instance(other.start_instance)
			, depth_key(other.depth_key)
			, priority(other.priority)
			, draw_calls(other.draw_calls)
			, triangles(other.triangles)
			, can_merge(other.can_merge)
			, is_instanced(other.is_instanced)
			, is_transparent(other.is_transparent)
		{
			// 清空源对象的指针
			other.shader = nullptr;
			other.vertex_buffer = nullptr;
			other.index_buffer = nullptr;
			other.instance_buffer = nullptr;
			other.texture = nullptr;
		}

		// 移动赋值操作符
		RenderBatch& operator=(RenderBatch&& other) noexcept
		{
			if (this != &other) {
				type = other.type;
				state_hash = other.state_hash;
				vertex_count = other.vertex_count;
				index_count = other.index_count;
				instance_count = other.instance_count;
				shader = other.shader;
				vertex_buffer = other.vertex_buffer;
				index_buffer = other.index_buffer;
				instance_buffer = other.instance_buffer;
				texture = other.texture;
				topology = other.topology;
				start_vertex = other.start_vertex;
				start_index = other.start_index;
				start_instance = other.start_instance;
				depth_key = other.depth_key;
				priority = other.priority;
				draw_calls = other.draw_calls;
				triangles = other.triangles;
				can_merge = other.can_merge;
				is_instanced = other.is_instanced;
				is_transparent = other.is_transparent;

				// 清空源对象的指针
				other.shader = nullptr;
				other.vertex_buffer = nullptr;
				other.index_buffer = nullptr;
				other.instance_buffer = nullptr;
				other.texture = nullptr;
			}
			return *this;
		}
	};

	/// 批处理统计信息
	struct BatchStats
	{
		uint32_t total_batches{0};
		uint32_t merged_batches{0};
		uint32_t draw_calls_saved{0};
		uint32_t state_changes{0};
		uint32_t instanced_draws{0};
		float batch_efficiency{0.0f};
		float gpu_utilization{0.0f};
		float merge_ratio{0.0f};

		// 重置统计
		void Reset() noexcept {
			*this = BatchStats{};
		}

		// 计算效率
		void CalculateEfficiency() noexcept {
			if (total_batches > 0) {
				merge_ratio = static_cast<float>(merged_batches) / total_batches;
				batch_efficiency = 1.0f - (static_cast<float>(state_changes) / total_batches);
			}
		}
	};

	/// 智能批处理管理器接口
	EXINTERFACE("A1B2C3D4-E5F6-7890-ABCD-123456789ABC") IRenderBatchManager : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化批处理管理器
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager) PURE;

		/// 关闭批处理管理器
		EXMETHOD void Shutdown() PURE;

		/// 开始批处理帧
		EXMETHOD HRESULT BeginBatch() PURE;

		/// 结束批处理帧并提交
		EXMETHOD HRESULT EndBatch() PURE;

		/// 添加渲染批次
		EXMETHOD HRESULT AddBatch(const RenderBatch& batch) PURE;

		/// 添加几何体批次
		EXMETHOD HRESULT AddGeometryBatch(IShader* shader, IBuffer* vertex_buffer,
										IBuffer* index_buffer, uint32_t index_count,
										D3D11_PRIMITIVE_TOPOLOGY topology = D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST) PURE;

		/// 添加精灵批次
		EXMETHOD HRESULT AddSpriteBatch(ITexture* texture, const SpriteVertex* vertices,
									   uint32_t vertex_count) PURE;

		/// 添加文本批次
		EXMETHOD HRESULT AddTextBatch(const wchar_t* text, float x, float y,
									 uint32_t color, IShader* text_shader) PURE;

		/// 添加实例化批次
		EXMETHOD HRESULT AddInstancedBatch(IShader* shader, IBuffer* vertex_buffer,
										  IBuffer* index_buffer, IBuffer* instance_buffer,
										  uint32_t index_count, uint32_t instance_count) PURE;

		/// 设置批处理策略
		EXMETHOD void SetBatchStrategy(bool enable_auto_merge, bool enable_state_sorting,
									  bool enable_instancing) PURE;

		/// 获取批处理统计
		EXMETHOD const BatchStats& GetBatchStats() const PURE;

		/// 重置统计信息
		EXMETHOD void ResetStats() PURE;

		/// 设置最大批次数量
		EXMETHOD void SetMaxBatchCount(uint32_t max_batches) PURE;

		/// 获取当前批次数量
		EXMETHOD uint32_t GetCurrentBatchCount() const PURE;

		/// 强制提交当前批次
		EXMETHOD HRESULT FlushBatches() PURE;

		/// 设置深度排序模式
		EXMETHOD void SetDepthSortMode(bool enable_depth_sort) PURE;
	};

	/// 渲染状态缓存管理器接口
	EXINTERFACE("B2C3D4E5-F6A7-8901-BCDE-234567890DEF") IRenderStateCache : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化状态缓存
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager) PURE;

		/// 关闭状态缓存
		EXMETHOD void Shutdown() PURE;

		/// 设置渲染状态（智能缓存）
		EXMETHOD HRESULT SetRenderState(const RenderStateHash& state_hash) PURE;

		/// 强制应用状态（跳过缓存）
		EXMETHOD HRESULT ForceApplyState(const RenderStateHash& state_hash) PURE;

		/// 获取当前状态哈希
		EXMETHOD const RenderStateHash& GetCurrentState() const PURE;

		/// 清除状态缓存
		EXMETHOD void ClearCache() PURE;

		/// 获取缓存命中率
		EXMETHOD float GetCacheHitRate() const PURE;

		/// 获取状态切换次数
		EXMETHOD uint32_t GetStateChangeCount() const PURE;

		/// 重置统计信息
		EXMETHOD void ResetStats() PURE;
	};

	/// 批处理工厂类 - 用于创建各种类型的批次
	class UIBatchFactory
	{
	public:
		/// 创建几何体批次
		static RenderBatch CreateGeometryBatch(IShader* shader, IBuffer* vertex_buffer,
											  IBuffer* index_buffer, uint32_t index_count,
											  D3D11_PRIMITIVE_TOPOLOGY topology = D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST)
		{
			RenderBatch batch;
			batch.type = BatchType::GEOMETRY;
			batch.shader = shader;
			batch.vertex_buffer = vertex_buffer;
			batch.index_buffer = index_buffer;
			batch.index_count = index_count;
			batch.topology = topology;
			batch.can_merge = true;
			return batch;
		}

		/// 创建精灵批次
		static RenderBatch CreateSpriteBatch(ITexture* texture, uint32_t sprite_count)
		{
			RenderBatch batch;
			batch.type = BatchType::SPRITE;
			batch.texture = texture;
			batch.vertex_count = sprite_count * 4; // 每个精灵4个顶点
			batch.index_count = sprite_count * 6;  // 每个精灵6个索引
			batch.topology = D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST;
			batch.can_merge = true;
			return batch;
		}

		/// 创建文本批次
		static RenderBatch CreateTextBatch(IShader* text_shader, uint32_t character_count)
		{
			RenderBatch batch;
			batch.type = BatchType::TEXT;
			batch.shader = text_shader;
			batch.vertex_count = character_count * 4; // 每个字符4个顶点
			batch.index_count = character_count * 6;  // 每个字符6个索引
			batch.topology = D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST;
			batch.can_merge = true;
			return batch;
		}

		/// 创建UI元素批次
		static RenderBatch CreateUIElementBatch(BatchType element_type, uint32_t element_count)
		{
			RenderBatch batch;
			batch.type = element_type;
			batch.vertex_count = element_count * 4;
			batch.index_count = element_count * 6;
			batch.topology = D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST;
			batch.can_merge = true;
			return batch;
		}

		/// 创建实例化批次
		static RenderBatch CreateInstancedBatch(IShader* shader, IBuffer* vertex_buffer,
											   IBuffer* index_buffer, IBuffer* instance_buffer,
											   uint32_t index_count, uint32_t instance_count)
		{
			RenderBatch batch;
			batch.type = BatchType::GEOMETRY;
			batch.shader = shader;
			batch.vertex_buffer = vertex_buffer;
			batch.index_buffer = index_buffer;
			batch.instance_buffer = instance_buffer;
			batch.index_count = index_count;
			batch.instance_count = instance_count;
			batch.is_instanced = true;
			batch.topology = D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST;
			batch.can_merge = false; // 实例化批次通常不合并
			return batch;
		}
	};

	/// 批处理优化器 - 提供性能分析和优化建议
	class UIBatchOptimizer
	{
	public:
		/// 分析批处理性能
		static void AnalyzeBatchPerformance(const std::vector<RenderBatch>& batches,
										   BatchStats& stats)
		{
			if (batches.empty()) {
				stats.Reset();
				return;
			}

			stats.total_batches = static_cast<uint32_t>(batches.size());

			// 统计不同类型的批次
			std::unordered_map<BatchType, uint32_t> type_counts;
			uint32_t mergeable_batches = 0;
			uint32_t instanced_batches = 0;

			for (const auto& batch : batches) {
				type_counts[batch.type]++;
				if (batch.can_merge) mergeable_batches++;
				if (batch.is_instanced) instanced_batches++;
			}

			stats.instanced_draws = instanced_batches;

			// 估算可能的合并效果
			float potential_merge_ratio = static_cast<float>(mergeable_batches) / stats.total_batches;
			stats.merge_ratio = potential_merge_ratio;

			// 计算效率指标
			stats.CalculateEfficiency();
		}

		/// 建议最优批处理策略
		static void RecommendBatchStrategy(const BatchStats& current_stats,
										 bool& enable_merge, bool& enable_sorting,
										 bool& enable_instancing)
		{
			// 基于当前统计数据给出建议
			enable_merge = current_stats.merge_ratio > 0.3f; // 如果30%以上的批次可合并
			enable_sorting = current_stats.state_changes > current_stats.total_batches * 0.5f; // 状态切换频繁
			enable_instancing = current_stats.instanced_draws < current_stats.total_batches * 0.2f; // 实例化使用率低
		}

		/// 预测批处理性能
		static float PredictBatchPerformance(const std::vector<RenderBatch>& batches)
		{
			if (batches.empty()) return 0.0f;

			float score = 1.0f;

			// 批次数量惩罚
			float batch_penalty = std::min(1.0f, static_cast<float>(batches.size()) / 100.0f);
			score *= (1.0f - batch_penalty * 0.5f);

			// 状态切换惩罚
			std::unordered_set<uint64_t> unique_states;
			for (const auto& batch : batches) {
				unique_states.insert(batch.state_hash.shader_hash);
			}

			float state_penalty = static_cast<float>(unique_states.size()) / batches.size();
			score *= (1.0f - state_penalty * 0.3f);

			// 实例化奖励
			uint32_t instanced_count = 0;
			for (const auto& batch : batches) {
				if (batch.is_instanced) instanced_count++;
			}

			float instancing_bonus = static_cast<float>(instanced_count) / batches.size();
			score *= (1.0f + instancing_bonus * 0.2f);

			return std::clamp(score, 0.0f, 1.0f);
		}
	};

	/// 全局函数声明

	/// 创建批处理管理器
	HRESULT CreateBatchManager(IRenderBatchManager** batch_manager);

	/// 创建状态缓存管理器
	HRESULT CreateStateCache(IRenderStateCache** state_cache);

	/// 获取全局批处理管理器
	IRenderBatchManager* GetGlobalBatchManager();

	/// 设置全局批处理管理器
	void SetGlobalBatchManager(IRenderBatchManager* manager);

	// =====================================================================================
	// 阶段二：GPU资源管理优化系统 - C++17
	// =====================================================================================

	/// 内存池类型枚举
	enum class PoolType : uint32_t
	{
		SMALL_OBJECTS = 0,      // 小对象池 (< 1KB)
		MEDIUM_OBJECTS = 1,     // 中等对象池 (1KB - 64KB)
		LARGE_OBJECTS = 2,      // 大对象池 (> 64KB)
		VERTEX_BUFFERS = 3,     // 顶点缓冲区池
		INDEX_BUFFERS = 4,      // 索引缓冲区池
		TEXTURES = 5,           // 纹理池
		CONSTANT_BUFFERS = 6,   // 常量缓冲区池
		COUNT
	};

	/// 内存分配策略
	enum class AllocationStrategy : uint32_t
	{
		FIRST_FIT = 0,          // 首次适应
		BEST_FIT = 1,           // 最佳适应
		WORST_FIT = 2,          // 最坏适应
		BUDDY_SYSTEM = 3        // 伙伴系统
	};

	/// 内存块信息
	struct MemoryBlock
	{
		void* ptr{nullptr};             // 内存指针
		size_t size{0};                 // 块大小
		size_t alignment{0};            // 对齐要求
		bool is_free{true};             // 是否空闲
		uint32_t pool_id{0};            // 所属池ID
		std::chrono::steady_clock::time_point last_used; // 最后使用时间
		uint32_t ref_count{0};          // 引用计数

		MemoryBlock() = default;
		MemoryBlock(void* p, size_t s, size_t align = 16)
			: ptr(p), size(s), alignment(align), last_used(std::chrono::steady_clock::now()) {}
	};

	/// GPU资源描述符
	struct GPUResourceDesc
	{
		PoolType pool_type{PoolType::SMALL_OBJECTS};
		size_t size{0};
		size_t alignment{16};
		uint32_t usage_flags{0};        // D3D11_USAGE标志
		uint32_t bind_flags{0};         // D3D11_BIND_FLAG标志
		uint32_t cpu_access_flags{0};   // D3D11_CPU_ACCESS_FLAG标志
		DXGI_FORMAT format{DXGI_FORMAT_UNKNOWN};
		uint32_t width{0};
		uint32_t height{0};
		uint32_t depth{1};
		uint32_t mip_levels{1};
		uint32_t array_size{1};

		GPUResourceDesc() = default;
		GPUResourceDesc(PoolType type, size_t sz) : pool_type(type), size(sz) {}
	};

	/// 内存池统计信息
	struct PoolStats
	{
		size_t total_size{0};           // 总大小
		size_t used_size{0};            // 已使用大小
		size_t free_size{0};            // 空闲大小
		uint32_t total_blocks{0};       // 总块数
		uint32_t used_blocks{0};        // 已使用块数
		uint32_t free_blocks{0};        // 空闲块数
		uint32_t allocation_count{0};   // 分配次数
		uint32_t deallocation_count{0}; // 释放次数
		uint32_t fragmentation_count{0}; // 碎片数量
		float fragmentation_ratio{0.0f}; // 碎片率
		float utilization_ratio{0.0f};   // 利用率

		void CalculateRatios() {
			if (total_size > 0) {
				utilization_ratio = static_cast<float>(used_size) / total_size;
			}
			if (total_blocks > 0) {
				fragmentation_ratio = static_cast<float>(fragmentation_count) / total_blocks;
			}
		}
	};

	/// 内存池接口
	EXINTERFACE("C3D4E5F6-A7B8-9012-CDEF-345678901234") IMemoryPool : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化内存池
		EXMETHOD HRESULT Initialize(PoolType pool_type, size_t initial_size,
								   AllocationStrategy strategy = AllocationStrategy::BEST_FIT) PURE;

		/// 关闭内存池
		EXMETHOD void Shutdown() PURE;

		/// 分配内存块
		EXMETHOD void* Allocate(size_t size, size_t alignment = 16) PURE;

		/// 释放内存块
		EXMETHOD void Deallocate(void* ptr) PURE;

		/// 重新分配内存块
		EXMETHOD void* Reallocate(void* ptr, size_t new_size, size_t alignment = 16) PURE;

		/// 获取内存块信息
		EXMETHOD bool GetBlockInfo(void* ptr, MemoryBlock& block_info) const PURE;

		/// 整理内存碎片
		EXMETHOD HRESULT DefragmentMemory() PURE;

		/// 收缩内存池
		EXMETHOD HRESULT ShrinkToFit() PURE;

		/// 预分配内存
		EXMETHOD HRESULT Reserve(size_t size) PURE;

		/// 获取池统计信息
		EXMETHOD const PoolStats& GetStats() const PURE;

		/// 重置统计信息
		EXMETHOD void ResetStats() PURE;

		/// 设置分配策略
		EXMETHOD void SetAllocationStrategy(AllocationStrategy strategy) PURE;

		/// 获取池类型
		EXMETHOD PoolType GetPoolType() const PURE;

		/// 检查内存池健康状态
		EXMETHOD bool IsHealthy() const PURE;
	};

	/// GPU资源池接口
	EXINTERFACE("D4E5F6A7-B8C9-0123-DEF4-456789012345") IGPUResourcePool : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化GPU资源池
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager, PoolType pool_type,
								   uint32_t initial_count = 100) PURE;

		/// 关闭GPU资源池
		EXMETHOD void Shutdown() PURE;

		/// 获取缓冲区资源
		EXMETHOD HRESULT AcquireBuffer(const GPUResourceDesc& desc, IBuffer** buffer) PURE;

		/// 释放缓冲区资源
		EXMETHOD void ReleaseBuffer(IBuffer* buffer) PURE;

		/// 获取纹理资源
		EXMETHOD HRESULT AcquireTexture(const GPUResourceDesc& desc, ITexture** texture) PURE;

		/// 释放纹理资源
		EXMETHOD void ReleaseTexture(ITexture* texture) PURE;

		/// 获取着色器资源
		EXMETHOD HRESULT AcquireShader(const GPUResourceDesc& desc, IShader** shader) PURE;

		/// 释放着色器资源
		EXMETHOD void ReleaseShader(IShader* shader) PURE;

		/// 预热资源池
		EXMETHOD HRESULT WarmupPool(uint32_t count) PURE;

		/// 清理未使用资源
		EXMETHOD HRESULT CleanupUnusedResources(uint32_t max_age_seconds = 300) PURE;

		/// 获取资源池统计
		EXMETHOD const PoolStats& GetPoolStats() const PURE;

		/// 设置资源池大小限制
		EXMETHOD void SetPoolSizeLimit(uint32_t max_count, size_t max_memory) PURE;

		/// 获取池类型
		EXMETHOD PoolType GetPoolType() const PURE;
	};

	/// 内存池管理器接口
	EXINTERFACE("E5F6A7B8-C9D0-1234-EF56-567890123456") IMemoryPoolManager : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化内存池管理器
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager) PURE;

		/// 关闭内存池管理器
		EXMETHOD void Shutdown() PURE;

		/// 创建内存池
		EXMETHOD HRESULT CreateMemoryPool(PoolType pool_type, size_t initial_size,
										 AllocationStrategy strategy, IMemoryPool** pool) PURE;

		/// 创建GPU资源池
		EXMETHOD HRESULT CreateGPUResourcePool(PoolType pool_type, uint32_t initial_count,
											  IGPUResourcePool** pool) PURE;

		/// 获取内存池
		EXMETHOD IMemoryPool* GetMemoryPool(PoolType pool_type) PURE;

		/// 获取GPU资源池
		EXMETHOD IGPUResourcePool* GetGPUResourcePool(PoolType pool_type) PURE;

		/// 全局内存分配
		EXMETHOD void* GlobalAllocate(size_t size, size_t alignment = 16) PURE;

		/// 全局内存释放
		EXMETHOD void GlobalDeallocate(void* ptr) PURE;

		/// 智能分配（自动选择最佳池）
		EXMETHOD void* SmartAllocate(size_t size, size_t alignment = 16) PURE;

		/// 获取全局统计信息
		EXMETHOD void GetGlobalStats(PoolStats& total_stats) const PURE;

		/// 执行全局内存整理
		EXMETHOD HRESULT GlobalDefragmentation() PURE;

		/// 执行全局清理
		EXMETHOD HRESULT GlobalCleanup(uint32_t max_age_seconds = 300) PURE;

		/// 设置全局内存限制
		EXMETHOD void SetGlobalMemoryLimit(size_t max_memory_bytes) PURE;

		/// 获取内存使用报告
		EXMETHOD void GenerateMemoryReport(std::string& report) const PURE;

		/// 启用/禁用自动整理
		EXMETHOD void SetAutoDefragmentation(bool enabled, uint32_t interval_seconds = 60) PURE;

		/// 注册内存事件回调
		EXMETHOD void RegisterMemoryEventCallback(void* callback_func, void* user_data) PURE;
	};

	/// 内存池工厂类
	class UIMemoryPoolFactory
	{
	public:
		/// 创建最优内存池配置
		static HRESULT CreateOptimalPools(IRenderManager* render_manager,
										  IMemoryPoolManager** pool_manager);

		/// 获取推荐的池大小
		static size_t GetRecommendedPoolSize(PoolType pool_type);

		/// 获取推荐的分配策略
		static AllocationStrategy GetRecommendedStrategy(PoolType pool_type);

		/// 创建预配置的内存池
		static HRESULT CreatePreconfiguredPool(PoolType pool_type, IMemoryPool** pool);
	};

	/// 全局函数声明

	/// 创建内存池管理器
	HRESULT CreateMemoryPoolManager(IMemoryPoolManager** pool_manager);

	/// 获取全局内存池管理器
	IMemoryPoolManager* GetGlobalMemoryPoolManager();

	/// 设置全局内存池管理器
	void SetGlobalMemoryPoolManager(IMemoryPoolManager* manager);

	// =====================================================================================
	// 异步资源加载系统 - C++17
	// =====================================================================================

	/// 资源加载优先级
	enum class LoadPriority : uint32_t
	{
		IMMEDIATE = 0,      // 立即加载
		HIGH = 1,           // 高优先级
		NORMAL = 2,         // 普通优先级
		LOW = 3,            // 低优先级
		BACKGROUND = 4      // 后台加载
	};

	/// 资源加载状态
	enum class LoadStatus : uint32_t
	{
		PENDING = 0,        // 等待加载
		LOADING = 1,        // 正在加载
		LOADED = 2,         // 加载完成
		FAILED = 3,         // 加载失败
		CANCELLED = 4       // 已取消
	};

	/// 资源类型
	enum class ResourceType : uint32_t
	{
		TEXTURE = 0,        // 纹理
		MESH = 1,           // 网格
		SHADER = 2,         // 着色器
		AUDIO = 3,          // 音频
		FONT = 4,           // 字体
		ANIMATION = 5,      // 动画
		MATERIAL = 6,       // 材质
		SCENE = 7           // 场景
	};

	/// 资源加载请求
	struct ResourceLoadRequest
	{
		std::wstring file_path;         // 文件路径
		ResourceType resource_type;     // 资源类型
		LoadPriority priority;          // 加载优先级
		uint32_t request_id;            // 请求ID
		void* user_data;                // 用户数据
		std::function<void(HRESULT, void*)> completion_callback; // 完成回调
		std::chrono::steady_clock::time_point request_time;      // 请求时间

		ResourceLoadRequest()
			: resource_type(ResourceType::TEXTURE)
			, priority(LoadPriority::NORMAL)
			, request_id(0)
			, user_data(nullptr)
			, request_time(std::chrono::steady_clock::now())
		{}
	};

	/// 资源加载结果
	struct ResourceLoadResult
	{
		uint32_t request_id;            // 请求ID
		HRESULT result;                 // 加载结果
		void* resource_ptr;             // 资源指针
		size_t resource_size;           // 资源大小
		std::chrono::milliseconds load_time; // 加载时间
		std::wstring error_message;     // 错误信息

		ResourceLoadResult()
			: request_id(0)
			, result(S_OK)
			, resource_ptr(nullptr)
			, resource_size(0)
			, load_time(0)
		{}
	};

	/// 资源加载统计
	struct LoaderStats
	{
		uint32_t total_requests;        // 总请求数
		uint32_t completed_requests;    // 完成请求数
		uint32_t failed_requests;       // 失败请求数
		uint32_t cancelled_requests;    // 取消请求数
		uint32_t pending_requests;      // 等待请求数
		uint32_t active_threads;        // 活跃线程数
		size_t total_bytes_loaded;      // 总加载字节数
		std::chrono::milliseconds avg_load_time; // 平均加载时间
		float cache_hit_rate;           // 缓存命中率

		LoaderStats()
			: total_requests(0), completed_requests(0), failed_requests(0)
			, cancelled_requests(0), pending_requests(0), active_threads(0)
			, total_bytes_loaded(0), avg_load_time(0), cache_hit_rate(0.0f)
		{}
	};

	/// 异步资源加载器接口
	EXINTERFACE("F6A7B8C9-D0E1-2345-6789-ABCDEF012345") IAsyncResourceLoader : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化异步加载器
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager, uint32_t thread_count = 4) PURE;

		/// 关闭异步加载器
		EXMETHOD void Shutdown() PURE;

		/// 提交加载请求
		EXMETHOD HRESULT SubmitLoadRequest(const ResourceLoadRequest& request, uint32_t* request_id) PURE;

		/// 取消加载请求
		EXMETHOD HRESULT CancelLoadRequest(uint32_t request_id) PURE;

		/// 检查加载状态
		EXMETHOD LoadStatus GetLoadStatus(uint32_t request_id) const PURE;

		/// 获取加载结果
		EXMETHOD HRESULT GetLoadResult(uint32_t request_id, ResourceLoadResult& result) PURE;

		/// 等待加载完成
		EXMETHOD HRESULT WaitForCompletion(uint32_t request_id, uint32_t timeout_ms = INFINITE) PURE;

		/// 等待所有请求完成
		EXMETHOD HRESULT WaitForAllCompletion(uint32_t timeout_ms = INFINITE) PURE;

		/// 设置线程数量
		EXMETHOD void SetThreadCount(uint32_t thread_count) PURE;

		/// 获取加载统计
		EXMETHOD const LoaderStats& GetLoaderStats() const PURE;

		/// 重置统计信息
		EXMETHOD void ResetStats() PURE;

		/// 暂停加载
		EXMETHOD void PauseLoading() PURE;

		/// 恢复加载
		EXMETHOD void ResumeLoading() PURE;

		/// 清空队列
		EXMETHOD void ClearQueue() PURE;

		/// 设置优先级权重
		EXMETHOD void SetPriorityWeights(const float weights[5]) PURE;
	};

	/// 资源缓存接口
	EXINTERFACE("A7B8C9D0-E1F2-3456-789A-BCDEF0123456") IResourceCache : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化资源缓存
		EXMETHOD HRESULT Initialize(size_t max_cache_size, uint32_t max_entries = 1000) PURE;

		/// 关闭资源缓存
		EXMETHOD void Shutdown() PURE;

		/// 添加资源到缓存
		EXMETHOD HRESULT AddResource(const std::wstring& key, void* resource,
									size_t size, ResourceType type) PURE;

		/// 从缓存获取资源
		EXMETHOD void* GetResource(const std::wstring& key) PURE;

		/// 移除资源
		EXMETHOD void RemoveResource(const std::wstring& key) PURE;

		/// 检查资源是否存在
		EXMETHOD bool HasResource(const std::wstring& key) const PURE;

		/// 清空缓存
		EXMETHOD void ClearCache() PURE;

		/// 获取缓存大小
		EXMETHOD size_t GetCacheSize() const PURE;

		/// 获取缓存条目数
		EXMETHOD uint32_t GetEntryCount() const PURE;

		/// 获取缓存命中率
		EXMETHOD float GetHitRate() const PURE;

		/// 设置缓存策略
		EXMETHOD void SetEvictionPolicy(uint32_t policy) PURE; // 0=LRU, 1=LFU, 2=FIFO

		/// 预热缓存
		EXMETHOD HRESULT WarmupCache(const std::vector<std::wstring>& resource_paths) PURE;
	};

	/// 流式加载器接口
	EXINTERFACE("B8C9D0E1-F2A3-4567-89AB-CDEF01234567") IStreamingLoader : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化流式加载器
		EXMETHOD HRESULT Initialize(IAsyncResourceLoader* async_loader,
								   IResourceCache* cache) PURE;

		/// 关闭流式加载器
		EXMETHOD void Shutdown() PURE;

		/// 开始流式加载
		EXMETHOD HRESULT StartStreaming(const std::wstring& base_path,
									   float view_distance = 1000.0f) PURE;

		/// 停止流式加载
		EXMETHOD void StopStreaming() PURE;

		/// 更新视点位置
		EXMETHOD void UpdateViewPosition(float x, float y, float z) PURE;

		/// 设置流式加载参数
		EXMETHOD void SetStreamingParams(float near_distance, float far_distance,
										uint32_t max_concurrent_loads = 8) PURE;

		/// 注册资源区域
		EXMETHOD HRESULT RegisterResourceRegion(const std::wstring& region_name,
											   float x, float y, float z, float radius,
											   const std::vector<std::wstring>& resources) PURE;

		/// 获取流式加载状态
		EXMETHOD bool IsStreaming() const PURE;

		/// 获取当前加载的资源数量
		EXMETHOD uint32_t GetActiveLoadCount() const PURE;
	};

	/// 异步资源加载工厂类
	class UIAsyncLoaderFactory
	{
	public:
		/// 创建优化的异步加载器
		static HRESULT CreateOptimalLoader(IRenderManager* render_manager,
										  IAsyncResourceLoader** loader);

		/// 创建资源缓存
		static HRESULT CreateResourceCache(size_t max_size, IResourceCache** cache);

		/// 创建流式加载器
		static HRESULT CreateStreamingLoader(IAsyncResourceLoader* async_loader,
											IResourceCache* cache,
											IStreamingLoader** streaming_loader);

		/// 获取推荐的线程数量
		static uint32_t GetRecommendedThreadCount();

		/// 获取推荐的缓存大小
		static size_t GetRecommendedCacheSize();
	};

	/// 全局函数声明

	/// 创建异步资源加载器
	HRESULT CreateAsyncResourceLoader(IAsyncResourceLoader** loader);

	/// 创建资源缓存
	HRESULT CreateResourceCache(IResourceCache** cache);

	/// 创建流式加载器
	HRESULT CreateStreamingLoader(IStreamingLoader** loader);

	/// 获取全局异步加载器
	IAsyncResourceLoader* GetGlobalAsyncLoader();

	/// 设置全局异步加载器
	void SetGlobalAsyncLoader(IAsyncResourceLoader* loader);

	/// 获取全局资源缓存
	IResourceCache* GetGlobalResourceCache();

	/// 设置全局资源缓存
	void SetGlobalResourceCache(IResourceCache* cache);

	// =====================================================================================
	// 资源生命周期管理系统 - C++17
	// =====================================================================================

	/// 资源引用类型
	enum class ReferenceType : uint32_t
	{
		STRONG = 0,         // 强引用
		WEAK = 1            // 弱引用
	};

	/// 资源状态
	enum class ResourceState : uint32_t
	{
		CREATING = 0,       // 创建中
		ACTIVE = 1,         // 活跃
		INACTIVE = 2,       // 非活跃
		DISPOSING = 3,      // 释放中
		DISPOSED = 4        // 已释放
	};

	/// 资源信息
	struct ResourceInfo
	{
		uint32_t resource_id;           // 资源ID
		ResourceType resource_type;     // 资源类型
		ResourceState state;            // 资源状态
		std::wstring name;              // 资源名称
		std::wstring file_path;         // 文件路径
		size_t memory_size;             // 内存大小
		uint32_t strong_ref_count;      // 强引用计数
		uint32_t weak_ref_count;        // 弱引用计数
		std::chrono::steady_clock::time_point created_time;    // 创建时间
		std::chrono::steady_clock::time_point last_access_time; // 最后访问时间
		uint32_t access_count;          // 访问次数
		void* resource_ptr;             // 资源指针

		ResourceInfo()
			: resource_id(0), resource_type(ResourceType::TEXTURE), state(ResourceState::CREATING)
			, memory_size(0), strong_ref_count(0), weak_ref_count(0), access_count(0)
			, resource_ptr(nullptr)
			, created_time(std::chrono::steady_clock::now())
			, last_access_time(std::chrono::steady_clock::now())
		{}
	};

	/// 资源监控统计
	struct ResourceMonitorStats
	{
		uint32_t total_resources;       // 总资源数
		uint32_t active_resources;      // 活跃资源数
		uint32_t leaked_resources;      // 泄漏资源数
		size_t total_memory_usage;      // 总内存使用量
		size_t peak_memory_usage;       // 峰值内存使用量
		uint32_t gc_cycles;             // GC周期数
		uint32_t resources_collected;   // 回收的资源数
		std::chrono::milliseconds avg_resource_lifetime; // 平均资源生命周期

		ResourceMonitorStats()
			: total_resources(0), active_resources(0), leaked_resources(0)
			, total_memory_usage(0), peak_memory_usage(0), gc_cycles(0)
			, resources_collected(0), avg_resource_lifetime(0)
		{}
	};

	/// 智能资源指针接口
	template<typename T>
	class ISmartResourcePtr
	{
	public:
		virtual ~ISmartResourcePtr() = default;

		/// 获取原始指针
		virtual T* Get() const = 0;

		/// 重置指针
		virtual void Reset(T* ptr = nullptr) = 0;

		/// 获取引用计数
		virtual uint32_t GetRefCount() const = 0;

		/// 检查是否有效
		virtual bool IsValid() const = 0;

		/// 获取资源ID
		virtual uint32_t GetResourceId() const = 0;
	};

	/// 资源生命周期管理器接口
	EXINTERFACE("C9D0E1F2-A3B4-5678-9ABC-DEF012345678") IResourceLifecycleManager : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化生命周期管理器
		EXMETHOD HRESULT Initialize(uint32_t gc_interval_seconds = 60) PURE;

		/// 关闭生命周期管理器
		EXMETHOD void Shutdown() PURE;

		/// 注册资源
		EXMETHOD HRESULT RegisterResource(void* resource, ResourceType type,
										 const std::wstring& name, size_t size,
										 uint32_t* resource_id) PURE;

		/// 注销资源
		EXMETHOD void UnregisterResource(uint32_t resource_id) PURE;

		/// 增加强引用
		EXMETHOD uint32_t AddStrongRef(uint32_t resource_id) PURE;

		/// 释放强引用
		EXMETHOD uint32_t ReleaseStrongRef(uint32_t resource_id) PURE;

		/// 增加弱引用
		EXMETHOD uint32_t AddWeakRef(uint32_t resource_id) PURE;

		/// 释放弱引用
		EXMETHOD uint32_t ReleaseWeakRef(uint32_t resource_id) PURE;

		/// 获取资源信息
		EXMETHOD bool GetResourceInfo(uint32_t resource_id, ResourceInfo& info) const PURE;

		/// 检查资源是否存在
		EXMETHOD bool IsResourceValid(uint32_t resource_id) const PURE;

		/// 获取资源指针
		EXMETHOD void* GetResourcePtr(uint32_t resource_id) PURE;

		/// 执行垃圾回收
		EXMETHOD uint32_t RunGarbageCollection() PURE;

		/// 设置自动GC
		EXMETHOD void SetAutoGC(bool enabled, uint32_t interval_seconds = 60) PURE;

		/// 获取监控统计
		EXMETHOD const ResourceMonitorStats& GetMonitorStats() const PURE;

		/// 重置统计信息
		EXMETHOD void ResetStats() PURE;

		/// 生成资源报告
		EXMETHOD void GenerateResourceReport(std::string& report) const PURE;

		/// 查找资源泄漏
		EXMETHOD std::vector<uint32_t> FindLeakedResources() const PURE;

		/// 强制释放资源
		EXMETHOD HRESULT ForceReleaseResource(uint32_t resource_id) PURE;
	};

	/// 智能资源指针实现
	template<typename T>
	class UISmartResourcePtr : public ISmartResourcePtr<T>
	{
	public:
		UISmartResourcePtr() : m_resource_id(0), m_lifecycle_manager(nullptr) {}

		UISmartResourcePtr(uint32_t resource_id, IResourceLifecycleManager* manager)
			: m_resource_id(resource_id), m_lifecycle_manager(manager)
		{
			if (m_lifecycle_manager) {
				m_lifecycle_manager->AddRef();
				m_lifecycle_manager->AddStrongRef(m_resource_id);
			}
		}

		UISmartResourcePtr(const UISmartResourcePtr& other)
			: m_resource_id(other.m_resource_id), m_lifecycle_manager(other.m_lifecycle_manager)
		{
			if (m_lifecycle_manager) {
				m_lifecycle_manager->AddRef();
				m_lifecycle_manager->AddStrongRef(m_resource_id);
			}
		}

		UISmartResourcePtr& operator=(const UISmartResourcePtr& other)
		{
			if (this != &other) {
				Reset();
				m_resource_id = other.m_resource_id;
				m_lifecycle_manager = other.m_lifecycle_manager;
				if (m_lifecycle_manager) {
					m_lifecycle_manager->AddRef();
					m_lifecycle_manager->AddStrongRef(m_resource_id);
				}
			}
			return *this;
		}

		~UISmartResourcePtr()
		{
			Reset();
		}

		T* Get() const override
		{
			if (m_lifecycle_manager && IsValid()) {
				return static_cast<T*>(m_lifecycle_manager->GetResourcePtr(m_resource_id));
			}
			return nullptr;
		}

		void Reset(T* ptr = nullptr) override
		{
			if (m_lifecycle_manager && m_resource_id != 0) {
				m_lifecycle_manager->ReleaseStrongRef(m_resource_id);
				m_lifecycle_manager->Release();
			}
			m_resource_id = 0;
			m_lifecycle_manager = nullptr;
		}

		uint32_t GetRefCount() const override
		{
			if (m_lifecycle_manager) {
				ResourceInfo info;
				if (m_lifecycle_manager->GetResourceInfo(m_resource_id, info)) {
					return info.strong_ref_count;
				}
			}
			return 0;
		}

		bool IsValid() const override
		{
			return m_lifecycle_manager && m_lifecycle_manager->IsResourceValid(m_resource_id);
		}

		uint32_t GetResourceId() const override
		{
			return m_resource_id;
		}

		T* operator->() const { return Get(); }
		T& operator*() const { return *Get(); }
		explicit operator bool() const { return IsValid(); }

	private:
		uint32_t m_resource_id;
		IResourceLifecycleManager* m_lifecycle_manager;
	};

	/// 弱引用智能指针
	template<typename T>
	class UIWeakResourcePtr
	{
	public:
		UIWeakResourcePtr() : m_resource_id(0), m_lifecycle_manager(nullptr) {}

		UIWeakResourcePtr(uint32_t resource_id, IResourceLifecycleManager* manager)
			: m_resource_id(resource_id), m_lifecycle_manager(manager)
		{
			if (m_lifecycle_manager) {
				m_lifecycle_manager->AddRef();
				m_lifecycle_manager->AddWeakRef(m_resource_id);
			}
		}

		~UIWeakResourcePtr()
		{
			if (m_lifecycle_manager && m_resource_id != 0) {
				m_lifecycle_manager->ReleaseWeakRef(m_resource_id);
				m_lifecycle_manager->Release();
			}
		}

		UISmartResourcePtr<T> Lock() const
		{
			if (m_lifecycle_manager && m_lifecycle_manager->IsResourceValid(m_resource_id)) {
				return UISmartResourcePtr<T>(m_resource_id, m_lifecycle_manager);
			}
			return UISmartResourcePtr<T>();
		}

		bool IsExpired() const
		{
			return !m_lifecycle_manager || !m_lifecycle_manager->IsResourceValid(m_resource_id);
		}

	private:
		uint32_t m_resource_id;
		IResourceLifecycleManager* m_lifecycle_manager;
	};

	/// 资源生命周期管理工厂类
	class UIResourceLifecycleFactory
	{
	public:
		/// 创建生命周期管理器
		static HRESULT CreateLifecycleManager(IResourceLifecycleManager** manager);

		/// 创建智能资源指针
		template<typename T>
		static UISmartResourcePtr<T> CreateSmartPtr(T* resource, ResourceType type,
												   const std::wstring& name, size_t size);

		/// 获取推荐的GC间隔
		static uint32_t GetRecommendedGCInterval();
	};

	/// 全局函数声明

	/// 创建资源生命周期管理器
	HRESULT CreateResourceLifecycleManager(IResourceLifecycleManager** manager);

	/// 获取全局生命周期管理器
	IResourceLifecycleManager* GetGlobalLifecycleManager();

	/// 设置全局生命周期管理器
	void SetGlobalLifecycleManager(IResourceLifecycleManager* manager);

	/// 便捷的智能指针创建函数
	template<typename T>
	UISmartResourcePtr<T> MakeSmartResource(T* resource, ResourceType type,
										   const std::wstring& name = L"", size_t size = 0);

	template<typename T>
	UIWeakResourcePtr<T> MakeWeakResource(const UISmartResourcePtr<T>& smart_ptr);

	// =====================================================================================
	// 阶段三：多线程渲染架构系统 - C++17
	// =====================================================================================

	/// 渲染命令类型
	enum class RenderCommandType : uint32_t
	{
		DRAW_INDEXED = 0,           // 绘制索引图元
		DRAW_INDEXED_INSTANCED = 1, // 绘制实例化索引图元
		DRAW = 2,                   // 绘制图元
		DRAW_INSTANCED = 3,         // 绘制实例化图元
		SET_VERTEX_BUFFER = 4,      // 设置顶点缓冲区
		SET_INDEX_BUFFER = 5,       // 设置索引缓冲区
		SET_SHADER = 6,             // 设置着色器
		SET_TEXTURE = 7,            // 设置纹理
		SET_CONSTANT_BUFFER = 8,    // 设置常量缓冲区
		SET_RENDER_TARGET = 9,      // 设置渲染目标
		CLEAR_RENDER_TARGET = 10,   // 清除渲染目标
		SET_VIEWPORT = 11,          // 设置视口
		SET_SCISSOR_RECT = 12,      // 设置裁剪矩形
		BEGIN_EVENT = 13,           // 开始事件标记
		END_EVENT = 14,             // 结束事件标记
		PRESENT = 15,               // 呈现
		BARRIER = 16,               // 内存屏障
		CUSTOM = 17                 // 自定义命令
	};

	/// 渲染命令优先级
	enum class CommandPriority : uint32_t
	{
		IMMEDIATE = 0,              // 立即执行
		HIGH = 1,                   // 高优先级
		NORMAL = 2,                 // 普通优先级
		LOW = 3,                    // 低优先级
		BACKGROUND = 4              // 后台优先级
	};

	/// 命令队列状态
	enum class CommandQueueState : uint32_t
	{
		IDLE = 0,                   // 空闲
		RECORDING = 1,              // 记录中
		EXECUTING = 2,              // 执行中
		COMPLETED = 3,              // 已完成
		QUEUE_ERROR = 4             // 错误状态
	};

	/// 渲染命令参数联合体
	union RenderCommandParams
	{
		struct DrawIndexedParams {
			uint32_t index_count;
			uint32_t start_index;
			uint32_t base_vertex;
		} draw_indexed;

		struct DrawIndexedInstancedParams {
			uint32_t index_count;
			uint32_t instance_count;
			uint32_t start_index;
			uint32_t base_vertex;
			uint32_t start_instance;
		} draw_indexed_instanced;

		struct DrawParams {
			uint32_t vertex_count;
			uint32_t start_vertex;
		} draw;

		struct DrawInstancedParams {
			uint32_t vertex_count;
			uint32_t instance_count;
			uint32_t start_vertex;
			uint32_t start_instance;
		} draw_instanced;

		struct SetVertexBufferParams {
			uint32_t slot;
			void* buffer; // 使用void*避免联合体中的类指针问题
			uint32_t stride;
			uint32_t offset;
		} set_vertex_buffer;

		struct SetIndexBufferParams {
			void* buffer; // 使用void*避免联合体中的类指针问题
			DXGI_FORMAT format;
			uint32_t offset;
		} set_index_buffer;

		struct SetShaderParams {
			void* shader; // 使用void*避免联合体中的类指针问题
			uint32_t stage; // 0=VS, 1=PS, 2=GS, 3=HS, 4=DS, 5=CS
		} set_shader;

		struct SetTextureParams {
			uint32_t slot;
			void* texture; // 使用void*避免联合体中的类指针问题
			uint32_t stage; // 着色器阶段
		} set_texture;

		struct SetConstantBufferParams {
			uint32_t slot;
			void* buffer; // 使用void*避免联合体中的类指针问题
			uint32_t stage; // 着色器阶段
		} set_constant_buffer;

		struct SetRenderTargetParams {
			uint32_t count;
			void* render_targets[8]; // 使用void*避免联合体中的类指针问题
			void* depth_stencil;
		} set_render_target;

		struct ClearRenderTargetParams {
			void* render_target; // 使用void*避免联合体中的类指针问题
			float color[4];
		} clear_render_target;

		struct SetViewportParams {
			float x, y, width, height;
			float min_depth, max_depth;
		} set_viewport;

		struct SetScissorRectParams {
			int32_t left, top, right, bottom;
		} set_scissor_rect;

		struct EventParams {
			wchar_t name[64];
			uint32_t color;
		} event;

		struct CustomParams {
			void* data;
			size_t size;
			void* executor; // 使用void*存储函数指针
		} custom;

		RenderCommandParams() { memset(this, 0, sizeof(*this)); }
	};

	/// 渲染命令结构
	struct RenderCommand
	{
		RenderCommandType type;             // 命令类型
		CommandPriority priority;           // 优先级
		RenderCommandParams params;         // 参数
		uint64_t timestamp;                 // 时间戳
		uint32_t thread_id;                 // 创建线程ID
		uint32_t command_id;                // 命令ID
		std::string debug_name;             // 调试名称

		RenderCommand()
			: type(RenderCommandType::DRAW_INDEXED)
			, priority(CommandPriority::NORMAL)
			, timestamp(0)
			, thread_id(0)
			, command_id(0)
		{}
	};

	/// 命令批次
	struct CommandBatch
	{
		std::vector<RenderCommand> commands;    // 命令列表
		uint32_t batch_id;                      // 批次ID
		CommandPriority priority;               // 批次优先级
		std::chrono::steady_clock::time_point submit_time; // 提交时间
		std::atomic<bool> is_completed{false}; // 是否完成
		std::function<void(HRESULT)> completion_callback; // 完成回调

		CommandBatch() : batch_id(0), priority(CommandPriority::NORMAL) {}

		// 拷贝构造函数
		CommandBatch(const CommandBatch& other)
			: commands(other.commands)
			, batch_id(other.batch_id)
			, priority(other.priority)
			, submit_time(other.submit_time)
			, is_completed(other.is_completed.load())
			, completion_callback(other.completion_callback)
		{}

		// 赋值操作符
		CommandBatch& operator=(const CommandBatch& other)
		{
			if (this != &other) {
				commands = other.commands;
				batch_id = other.batch_id;
				priority = other.priority;
				submit_time = other.submit_time;
				is_completed.store(other.is_completed.load());
				completion_callback = other.completion_callback;
			}
			return *this;
		}

		// 移动构造函数
		CommandBatch(CommandBatch&& other) noexcept
			: commands(std::move(other.commands))
			, batch_id(other.batch_id)
			, priority(other.priority)
			, submit_time(std::move(other.submit_time))
			, is_completed(other.is_completed.load())
			, completion_callback(std::move(other.completion_callback))
		{}

		// 移动赋值操作符
		CommandBatch& operator=(CommandBatch&& other) noexcept
		{
			if (this != &other) {
				commands = std::move(other.commands);
				batch_id = other.batch_id;
				priority = other.priority;
				submit_time = std::move(other.submit_time);
				is_completed.store(other.is_completed.load());
				completion_callback = std::move(other.completion_callback);
			}
			return *this;
		}
	};

	/// 命令队列统计
	struct CommandQueueStats
	{
		uint32_t total_commands;            // 总命令数
		uint32_t executed_commands;         // 已执行命令数
		uint32_t pending_commands;          // 等待命令数
		uint32_t failed_commands;           // 失败命令数
		uint32_t total_batches;             // 总批次数
		uint32_t active_batches;            // 活跃批次数
		std::chrono::milliseconds avg_execution_time; // 平均执行时间
		std::chrono::milliseconds queue_latency;      // 队列延迟
		float throughput_commands_per_sec;  // 吞吐量（命令/秒）
		size_t memory_usage;                // 内存使用量

		CommandQueueStats()
			: total_commands(0), executed_commands(0), pending_commands(0)
			, failed_commands(0), total_batches(0), active_batches(0)
			, avg_execution_time(0), queue_latency(0)
			, throughput_commands_per_sec(0.0f), memory_usage(0)
		{}
	};

	/// 渲染命令队列接口
	EXINTERFACE("D0E1F2A3-B4C5-6789-ABCD-EF0123456789") IRenderCommandQueue : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化命令队列
		EXMETHOD HRESULT Initialize(void* render_manager,
								   uint32_t max_commands = 10000,
								   bool enable_double_buffering = true) PURE;

		/// 关闭命令队列
		EXMETHOD void Shutdown() PURE;

		/// 开始记录命令
		EXMETHOD HRESULT BeginRecording() PURE;

		/// 结束记录命令
		EXMETHOD HRESULT EndRecording() PURE;

		/// 添加渲染命令
		EXMETHOD HRESULT AddCommand(const RenderCommand& command) PURE;

		/// 添加命令批次
		EXMETHOD HRESULT AddBatch(const CommandBatch& batch, uint32_t* batch_id) PURE;

		/// 执行命令队列
		EXMETHOD HRESULT ExecuteCommands() PURE;

		/// 异步执行命令队列
		EXMETHOD HRESULT ExecuteCommandsAsync(std::function<void(HRESULT)> completion_callback) PURE;

		/// 等待执行完成
		EXMETHOD HRESULT WaitForCompletion(uint32_t timeout_ms = INFINITE) PURE;

		/// 清空命令队列
		EXMETHOD void ClearCommands() PURE;

		/// 获取队列状态
		EXMETHOD CommandQueueState GetState() const PURE;

		/// 获取统计信息
		EXMETHOD const CommandQueueStats& GetStats() const PURE;

		/// 重置统计信息
		EXMETHOD void ResetStats() PURE;

		/// 设置命令优化
		EXMETHOD void SetCommandOptimization(bool enabled) PURE;

		/// 设置双缓冲
		EXMETHOD void SetDoubleBuffering(bool enabled) PURE;

		/// 获取当前缓冲区索引
		EXMETHOD uint32_t GetCurrentBufferIndex() const PURE;

		/// 交换缓冲区
		EXMETHOD HRESULT SwapBuffers() PURE;
	};

	/// 渲染命令构建器
	class UIRenderCommandBuilder
	{
	public:
		UIRenderCommandBuilder() = default;
		~UIRenderCommandBuilder() = default;

		/// 创建绘制索引命令
		static RenderCommand CreateDrawIndexedCommand(uint32_t index_count,
													  uint32_t start_index = 0,
													  uint32_t base_vertex = 0,
													  CommandPriority priority = CommandPriority::NORMAL);

		/// 创建绘制实例化索引命令
		static RenderCommand CreateDrawIndexedInstancedCommand(uint32_t index_count,
															   uint32_t instance_count,
															   uint32_t start_index = 0,
															   uint32_t base_vertex = 0,
															   uint32_t start_instance = 0,
															   CommandPriority priority = CommandPriority::NORMAL);

		/// 创建设置顶点缓冲区命令
		static RenderCommand CreateSetVertexBufferCommand(uint32_t slot, void* buffer,
														  uint32_t stride, uint32_t offset = 0,
														  CommandPriority priority = CommandPriority::NORMAL);

		/// 创建设置索引缓冲区命令
		static RenderCommand CreateSetIndexBufferCommand(void* buffer, DXGI_FORMAT format,
														 uint32_t offset = 0,
														 CommandPriority priority = CommandPriority::NORMAL);

		/// 创建设置着色器命令
		static RenderCommand CreateSetShaderCommand(void* shader, uint32_t stage,
													CommandPriority priority = CommandPriority::NORMAL);

		/// 创建设置纹理命令
		static RenderCommand CreateSetTextureCommand(uint32_t slot, void* texture, uint32_t stage,
													 CommandPriority priority = CommandPriority::NORMAL);

		/// 创建设置常量缓冲区命令
		static RenderCommand CreateSetConstantBufferCommand(uint32_t slot, void* buffer, uint32_t stage,
															CommandPriority priority = CommandPriority::NORMAL);

		/// 创建清除渲染目标命令
		static RenderCommand CreateClearRenderTargetCommand(void* render_target,
															const float color[4],
															CommandPriority priority = CommandPriority::NORMAL);

		/// 创建设置视口命令
		static RenderCommand CreateSetViewportCommand(float x, float y, float width, float height,
													  float min_depth = 0.0f, float max_depth = 1.0f,
													  CommandPriority priority = CommandPriority::NORMAL);

		/// 创建事件标记命令
		static RenderCommand CreateEventCommand(const wchar_t* name, uint32_t color = 0xFFFFFFFF,
												bool is_begin = true,
												CommandPriority priority = CommandPriority::NORMAL);

		/// 创建自定义命令
		static RenderCommand CreateCustomCommand(void* data, size_t size,
												 std::function<void(void*)> executor,
												 CommandPriority priority = CommandPriority::NORMAL);

		/// 创建命令批次
		static CommandBatch CreateBatch(const std::vector<RenderCommand>& commands,
									   CommandPriority priority = CommandPriority::NORMAL);

		/// 优化命令序列
		static std::vector<RenderCommand> OptimizeCommands(const std::vector<RenderCommand>& commands);

	private:
		static uint32_t GetNextCommandId();
		static uint64_t GetTimestamp();
	};

	/// 命令队列工厂类
	class UICommandQueueFactory
	{
	public:
		/// 创建优化的命令队列
		static HRESULT CreateOptimalQueue(void* render_manager,
										  IRenderCommandQueue** queue);

		/// 创建高性能命令队列
		static HRESULT CreateHighPerformanceQueue(void* render_manager,
												  uint32_t max_commands,
												  IRenderCommandQueue** queue);

		/// 获取推荐的队列大小
		static uint32_t GetRecommendedQueueSize();

		/// 获取推荐的配置
		static bool GetRecommendedDoubleBuffering();
	};

	/// 全局函数声明

	/// 创建渲染命令队列
	HRESULT CreateRenderCommandQueue(IRenderCommandQueue** queue);

	/// 获取全局命令队列
	IRenderCommandQueue* GetGlobalCommandQueue();

	/// 设置全局命令队列
	void SetGlobalCommandQueue(IRenderCommandQueue* queue);

	// =====================================================================================
	// 并行渲染管线系统 - C++17
	// =====================================================================================

	/// 渲染阶段类型
	enum class RenderStage : uint32_t
	{
		GEOMETRY_PROCESSING = 0,    // 几何处理
		CULLING = 1,                // 剔除
		BATCHING = 2,               // 批处理
		COMMAND_GENERATION = 3,     // 命令生成
		EXECUTION = 4,              // 执行
		POST_PROCESSING = 5,        // 后处理
		PRESENT = 6                 // 呈现
	};

	/// 管线任务类型
	enum class PipelineTaskType : uint32_t
	{
		TRANSFORM = 0,              // 变换计算
		FRUSTUM_CULLING = 1,        // 视锥剔除
		OCCLUSION_CULLING = 2,      // 遮挡剔除
		BATCH_GENERATION = 3,       // 批次生成
		COMMAND_RECORDING = 4,      // 命令记录
		RESOURCE_BINDING = 5,       // 资源绑定
		DRAW_CALL = 6,              // 绘制调用
		BARRIER_SYNC = 7            // 屏障同步
	};

	/// 任务优先级
	enum class TaskPriority : uint32_t
	{
		CRITICAL = 0,               // 关键任务
		HIGH = 1,                   // 高优先级
		NORMAL = 2,                 // 普通优先级
		LOW = 3,                    // 低优先级
		BACKGROUND = 4              // 后台任务
	};

	/// 管线任务
	struct PipelineTask
	{
		PipelineTaskType type;              // 任务类型
		TaskPriority priority;              // 优先级
		RenderStage stage;                  // 所属阶段
		uint32_t task_id;                   // 任务ID
		uint32_t dependency_count;          // 依赖数量
		std::vector<uint32_t> dependencies; // 依赖任务ID列表
		std::function<HRESULT()> executor;  // 执行函数
		std::atomic<bool> is_completed{false}; // 是否完成
		std::chrono::steady_clock::time_point start_time; // 开始时间
		std::chrono::milliseconds execution_time{0}; // 执行时间
		void* user_data{nullptr};           // 用户数据

		PipelineTask()
			: type(PipelineTaskType::TRANSFORM)
			, priority(TaskPriority::NORMAL)
			, stage(RenderStage::GEOMETRY_PROCESSING)
			, task_id(0)
			, dependency_count(0)
		{}

		// 拷贝构造函数
		PipelineTask(const PipelineTask& other)
			: type(other.type)
			, priority(other.priority)
			, stage(other.stage)
			, task_id(other.task_id)
			, dependency_count(other.dependency_count)
			, dependencies(other.dependencies)
			, executor(other.executor)
			, is_completed(other.is_completed.load())
			, start_time(other.start_time)
			, execution_time(other.execution_time)
			, user_data(other.user_data)
		{}

		// 赋值操作符
		PipelineTask& operator=(const PipelineTask& other)
		{
			if (this != &other) {
				type = other.type;
				priority = other.priority;
				stage = other.stage;
				task_id = other.task_id;
				dependency_count = other.dependency_count;
				dependencies = other.dependencies;
				executor = other.executor;
				is_completed.store(other.is_completed.load());
				start_time = other.start_time;
				execution_time = other.execution_time;
				user_data = other.user_data;
			}
			return *this;
		}

		// 移动构造函数
		PipelineTask(PipelineTask&& other) noexcept
			: type(other.type)
			, priority(other.priority)
			, stage(other.stage)
			, task_id(other.task_id)
			, dependency_count(other.dependency_count)
			, dependencies(std::move(other.dependencies))
			, executor(std::move(other.executor))
			, is_completed(other.is_completed.load())
			, start_time(std::move(other.start_time))
			, execution_time(other.execution_time)
			, user_data(other.user_data)
		{}

		// 移动赋值操作符
		PipelineTask& operator=(PipelineTask&& other) noexcept
		{
			if (this != &other) {
				type = other.type;
				priority = other.priority;
				stage = other.stage;
				task_id = other.task_id;
				dependency_count = other.dependency_count;
				dependencies = std::move(other.dependencies);
				executor = std::move(other.executor);
				is_completed.store(other.is_completed.load());
				start_time = std::move(other.start_time);
				execution_time = other.execution_time;
				user_data = other.user_data;
			}
			return *this;
		}
	};

	/// 管线阶段
	struct PipelineStage
	{
		RenderStage stage_type;             // 阶段类型
		std::vector<PipelineTask> tasks;    // 任务列表
		std::atomic<uint32_t> completed_tasks{0}; // 已完成任务数
		std::atomic<bool> is_stage_complete{false}; // 阶段是否完成
		std::chrono::steady_clock::time_point start_time; // 阶段开始时间
		std::chrono::milliseconds stage_duration{0}; // 阶段持续时间

		PipelineStage() : stage_type(RenderStage::GEOMETRY_PROCESSING) {}
	};

	/// 管线统计信息
	struct PipelineStats
	{
		uint32_t total_tasks;               // 总任务数
		uint32_t completed_tasks;           // 已完成任务数
		uint32_t failed_tasks;              // 失败任务数
		uint32_t active_threads;            // 活跃线程数
		std::chrono::milliseconds total_frame_time{0}; // 总帧时间
		std::chrono::milliseconds cpu_time{0};         // CPU时间
		std::chrono::milliseconds gpu_time{0};         // GPU时间
		std::chrono::milliseconds sync_time{0};        // 同步时间
		float cpu_utilization{0.0f};       // CPU利用率
		float gpu_utilization{0.0f};       // GPU利用率
		float parallelism_efficiency{0.0f}; // 并行效率
		uint32_t pipeline_stalls{0};       // 管线停顿次数

		PipelineStats()
			: total_tasks(0), completed_tasks(0), failed_tasks(0), active_threads(0)
			, pipeline_stalls(0)
		{}
	};

	/// 并行渲染管线接口
	EXINTERFACE("E1F2A3B4-C5D6-789A-BCDE-F012345678AB") IParallelRenderPipeline : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化并行渲染管线
		EXMETHOD HRESULT Initialize(void* render_manager,
								   void* command_queue,
								   uint32_t thread_count = 0) PURE;

		/// 关闭并行渲染管线
		EXMETHOD void Shutdown() PURE;

		/// 开始帧渲染
		EXMETHOD HRESULT BeginFrame() PURE;

		/// 结束帧渲染
		EXMETHOD HRESULT EndFrame() PURE;

		/// 添加渲染任务
		EXMETHOD HRESULT AddTask(const PipelineTask& task, uint32_t* task_id) PURE;

		/// 添加任务依赖
		EXMETHOD HRESULT AddTaskDependency(uint32_t task_id, uint32_t dependency_id) PURE;

		/// 执行管线
		EXMETHOD HRESULT ExecutePipeline() PURE;

		/// 异步执行管线
		EXMETHOD HRESULT ExecutePipelineAsync(std::function<void(HRESULT)> completion_callback) PURE;

		/// 等待管线完成
		EXMETHOD HRESULT WaitForCompletion(uint32_t timeout_ms = INFINITE) PURE;

		/// 获取管线状态
		EXMETHOD bool IsExecuting() const PURE;

		/// 获取统计信息
		EXMETHOD const PipelineStats& GetStats() const PURE;

		/// 重置统计信息
		EXMETHOD void ResetStats() PURE;

		/// 设置线程数量
		EXMETHOD void SetThreadCount(uint32_t thread_count) PURE;

		/// 获取线程数量
		EXMETHOD uint32_t GetThreadCount() const PURE;

		/// 启用/禁用阶段并行
		EXMETHOD void SetStageParallelism(bool enabled) PURE;

		/// 启用/禁用任务窃取
		EXMETHOD void SetWorkStealing(bool enabled) PURE;

		/// 设置CPU亲和性
		EXMETHOD HRESULT SetThreadAffinity(const std::vector<uint32_t>& cpu_cores) PURE;

		/// 获取阶段信息
		EXMETHOD bool GetStageInfo(RenderStage stage, PipelineStage& stage_info) const PURE;

		/// 强制同步点
		EXMETHOD HRESULT InsertSyncPoint() PURE;
	};

	/// 任务调度器接口
	EXINTERFACE("F2A3B4C5-D6E7-89AB-CDEF-0123456789BC") ITaskScheduler : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化任务调度器
		EXMETHOD HRESULT Initialize(uint32_t thread_count, bool enable_work_stealing = true) PURE;

		/// 关闭任务调度器
		EXMETHOD void Shutdown() PURE;

		/// 提交任务
		EXMETHOD HRESULT SubmitTask(const PipelineTask& task, uint32_t* task_id) PURE;

		/// 提交任务批次
		EXMETHOD HRESULT SubmitTaskBatch(const std::vector<PipelineTask>& tasks) PURE;

		/// 等待任务完成
		EXMETHOD HRESULT WaitForTask(uint32_t task_id, uint32_t timeout_ms = INFINITE) PURE;

		/// 等待所有任务完成
		EXMETHOD HRESULT WaitForAllTasks(uint32_t timeout_ms = INFINITE) PURE;

		/// 取消任务
		EXMETHOD HRESULT CancelTask(uint32_t task_id) PURE;

		/// 获取任务状态
		EXMETHOD bool IsTaskCompleted(uint32_t task_id) const PURE;

		/// 设置任务优先级
		EXMETHOD HRESULT SetTaskPriority(uint32_t task_id, TaskPriority priority) PURE;

		/// 获取活跃线程数
		EXMETHOD uint32_t GetActiveThreadCount() const PURE;

		/// 获取队列中的任务数
		EXMETHOD uint32_t GetPendingTaskCount() const PURE;

		/// 启用/禁用工作窃取
		EXMETHOD void SetWorkStealing(bool enabled) PURE;

		/// 设置线程亲和性
		EXMETHOD HRESULT SetThreadAffinity(uint32_t thread_index, uint32_t cpu_core) PURE;
	};

	/// 并行管线工厂类
	class UIParallelPipelineFactory
	{
	public:
		/// 创建优化的并行管线
		static HRESULT CreateOptimalPipeline(void* render_manager,
											 void* command_queue,
											 IParallelRenderPipeline** pipeline);

		/// 创建高性能并行管线
		static HRESULT CreateHighPerformancePipeline(void* render_manager,
													 void* command_queue,
													 uint32_t thread_count,
													 IParallelRenderPipeline** pipeline);

		/// 创建任务调度器
		static HRESULT CreateTaskScheduler(uint32_t thread_count, ITaskScheduler** scheduler);

		/// 获取推荐的线程数量
		static uint32_t GetRecommendedThreadCount();

		/// 获取推荐的CPU亲和性设置
		static std::vector<uint32_t> GetRecommendedCPUAffinity();

		/// 检测系统并行能力
		static bool DetectParallelCapabilities();
	};

	/// 任务构建器类
	class UIPipelineTaskBuilder
	{
	public:
		/// 创建变换任务
		static PipelineTask CreateTransformTask(void* transform_data,
												std::function<HRESULT()> executor,
												TaskPriority priority = TaskPriority::NORMAL);

		/// 创建剔除任务
		static PipelineTask CreateCullingTask(void* culling_data,
											  std::function<HRESULT()> executor,
											  TaskPriority priority = TaskPriority::HIGH);

		/// 创建批处理任务
		static PipelineTask CreateBatchingTask(void* batch_data,
											   std::function<HRESULT()> executor,
											   TaskPriority priority = TaskPriority::NORMAL);

		/// 创建命令记录任务
		static PipelineTask CreateCommandRecordingTask(void* command_data,
													   std::function<HRESULT()> executor,
													   TaskPriority priority = TaskPriority::HIGH);

		/// 创建同步屏障任务
		static PipelineTask CreateBarrierTask(TaskPriority priority = TaskPriority::CRITICAL);

		/// 创建自定义任务
		static PipelineTask CreateCustomTask(PipelineTaskType type, RenderStage stage,
											 std::function<HRESULT()> executor,
											 void* user_data = nullptr,
											 TaskPriority priority = TaskPriority::NORMAL);

		/// 添加任务依赖关系
		static void AddDependency(PipelineTask& task, uint32_t dependency_id);

		/// 创建任务链
		static std::vector<PipelineTask> CreateTaskChain(const std::vector<std::function<HRESULT()>>& executors,
														 RenderStage stage,
														 TaskPriority priority = TaskPriority::NORMAL);

	private:
		static uint32_t GetNextTaskId();
	};

	/// 全局函数声明

	/// 创建并行渲染管线
	HRESULT CreateParallelRenderPipeline(IParallelRenderPipeline** pipeline);

	/// 创建任务调度器
	HRESULT CreateTaskScheduler(ITaskScheduler** scheduler);

	/// 获取全局并行管线
	IParallelRenderPipeline* GetGlobalParallelPipeline();

	/// 设置全局并行管线
	void SetGlobalParallelPipeline(IParallelRenderPipeline* pipeline);

	/// 获取全局任务调度器
	ITaskScheduler* GetGlobalTaskScheduler();

	/// 设置全局任务调度器
	void SetGlobalTaskScheduler(ITaskScheduler* scheduler);

	// =====================================================================================
	// 工作线程池系统 - C++17
	// =====================================================================================

	/// 线程池状态
	enum class ThreadPoolState : uint32_t
	{
		IDLE = 0,                   // 空闲
		RUNNING = 1,                // 运行中
		PAUSED = 2,                 // 暂停
		SHUTTING_DOWN = 3,          // 关闭中
		SHUTDOWN = 4                // 已关闭
	};

	/// 工作线程信息
	struct WorkerThreadInfo
	{
		uint32_t thread_id;                 // 线程ID
		uint32_t cpu_core;                  // 绑定的CPU核心
		std::atomic<bool> is_active{false}; // 是否活跃
		std::atomic<uint32_t> tasks_processed{0}; // 已处理任务数
		std::chrono::milliseconds total_work_time{0}; // 总工作时间
		std::chrono::steady_clock::time_point last_activity; // 最后活动时间
		float cpu_utilization{0.0f};       // CPU利用率

		WorkerThreadInfo() : thread_id(0), cpu_core(0) {}

		// 拷贝构造函数
		WorkerThreadInfo(const WorkerThreadInfo& other)
			: thread_id(other.thread_id)
			, cpu_core(other.cpu_core)
			, is_active(other.is_active.load())
			, tasks_processed(other.tasks_processed.load())
			, total_work_time(other.total_work_time)
			, last_activity(other.last_activity)
			, cpu_utilization(other.cpu_utilization)
		{}

		// 赋值操作符
		WorkerThreadInfo& operator=(const WorkerThreadInfo& other)
		{
			if (this != &other) {
				thread_id = other.thread_id;
				cpu_core = other.cpu_core;
				is_active.store(other.is_active.load());
				tasks_processed.store(other.tasks_processed.load());
				total_work_time = other.total_work_time;
				last_activity = other.last_activity;
				cpu_utilization = other.cpu_utilization;
			}
			return *this;
		}

		// 移动构造函数
		WorkerThreadInfo(WorkerThreadInfo&& other) noexcept
			: thread_id(other.thread_id)
			, cpu_core(other.cpu_core)
			, is_active(other.is_active.load())
			, tasks_processed(other.tasks_processed.load())
			, total_work_time(std::move(other.total_work_time))
			, last_activity(std::move(other.last_activity))
			, cpu_utilization(other.cpu_utilization)
		{}

		// 移动赋值操作符
		WorkerThreadInfo& operator=(WorkerThreadInfo&& other) noexcept
		{
			if (this != &other) {
				thread_id = other.thread_id;
				cpu_core = other.cpu_core;
				is_active.store(other.is_active.load());
				tasks_processed.store(other.tasks_processed.load());
				total_work_time = std::move(other.total_work_time);
				last_activity = std::move(other.last_activity);
				cpu_utilization = other.cpu_utilization;
			}
			return *this;
		}
	};

	/// 线程池统计
	struct ThreadPoolStats
	{
		uint32_t total_threads;             // 总线程数
		uint32_t active_threads;            // 活跃线程数
		uint32_t idle_threads;              // 空闲线程数
		uint32_t total_tasks_processed;     // 总处理任务数
		uint32_t pending_tasks;             // 等待任务数
		float average_cpu_utilization;      // 平均CPU利用率
		std::chrono::milliseconds average_task_time; // 平均任务时间
		uint32_t work_stealing_events;      // 工作窃取事件数
		uint32_t load_balancing_events;     // 负载均衡事件数

		ThreadPoolStats()
			: total_threads(0), active_threads(0), idle_threads(0)
			, total_tasks_processed(0), pending_tasks(0)
			, average_cpu_utilization(0.0f), average_task_time(0)
			, work_stealing_events(0), load_balancing_events(0)
		{}
	};

	/// 工作线程池接口
	EXINTERFACE("A3B4C5D6-E7F8-9ABC-DEF0-123456789CDE") IWorkerThreadPool : public IUnknown
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化线程池
		EXMETHOD HRESULT Initialize(uint32_t thread_count = 0,
								   bool enable_work_stealing = true,
								   bool enable_cpu_affinity = true) PURE;

		/// 关闭线程池
		EXMETHOD void Shutdown() PURE;

		/// 提交工作项
		EXMETHOD HRESULT SubmitWork(std::function<void()> work_item,
								   TaskPriority priority = TaskPriority::NORMAL) PURE;

		/// 提交工作批次
		EXMETHOD HRESULT SubmitWorkBatch(const std::vector<std::function<void()>>& work_items,
										TaskPriority priority = TaskPriority::NORMAL) PURE;

		/// 等待所有工作完成
		EXMETHOD HRESULT WaitForAllWork(uint32_t timeout_ms = INFINITE) PURE;

		/// 暂停线程池
		EXMETHOD void Pause() PURE;

		/// 恢复线程池
		EXMETHOD void Resume() PURE;

		/// 获取线程池状态
		EXMETHOD ThreadPoolState GetState() const PURE;

		/// 获取统计信息
		EXMETHOD const ThreadPoolStats& GetStats() const PURE;

		/// 重置统计信息
		EXMETHOD void ResetStats() PURE;

		/// 设置线程数量
		EXMETHOD HRESULT SetThreadCount(uint32_t thread_count) PURE;

		/// 获取线程数量
		EXMETHOD uint32_t GetThreadCount() const PURE;

		/// 启用/禁用工作窃取
		EXMETHOD void SetWorkStealing(bool enabled) PURE;

		/// 启用/禁用CPU亲和性
		EXMETHOD void SetCPUAffinity(bool enabled) PURE;

		/// 设置特定线程的CPU亲和性
		EXMETHOD HRESULT SetThreadCPUAffinity(uint32_t thread_index, uint32_t cpu_core) PURE;

		/// 获取线程信息
		EXMETHOD bool GetThreadInfo(uint32_t thread_index, WorkerThreadInfo& info) const PURE;

		/// 获取所有线程信息
		EXMETHOD std::vector<WorkerThreadInfo> GetAllThreadInfo() const PURE;

		/// 设置负载均衡策略
		EXMETHOD void SetLoadBalancingStrategy(uint32_t strategy) PURE; // 0=Round Robin, 1=Least Loaded, 2=Random

		/// 强制负载均衡
		EXMETHOD HRESULT ForceLoadBalance() PURE;
	};

	/// 线程池工厂类
	class UIThreadPoolFactory
	{
	public:
		/// 创建优化的线程池
		static HRESULT CreateOptimalThreadPool(IWorkerThreadPool** thread_pool);

		/// 创建高性能线程池
		static HRESULT CreateHighPerformanceThreadPool(uint32_t thread_count,
													   IWorkerThreadPool** thread_pool);

		/// 获取推荐的线程数量
		static uint32_t GetRecommendedThreadCount();

		/// 获取推荐的CPU亲和性设置
		static std::vector<uint32_t> GetRecommendedCPUCores();

		/// 检测系统线程能力
		static bool DetectThreadingCapabilities();

		/// 获取系统CPU核心数
		static uint32_t GetSystemCoreCount();

		/// 获取系统逻辑处理器数
		static uint32_t GetSystemLogicalProcessorCount();
	};

	/// 全局函数声明

	/// 创建工作线程池
	HRESULT CreateWorkerThreadPool(IWorkerThreadPool** thread_pool);

	/// 获取全局线程池
	IWorkerThreadPool* GetGlobalThreadPool();

	/// 设置全局线程池
	void SetGlobalThreadPool(IWorkerThreadPool* thread_pool);
}