﻿#pragma once
namespace HHBUI
{
	enum info_loading_style {
		e_st_rainbow = 0,
		e_st_angle,
		e_st_dots,
		e_st_ang,
		e_st_vdots,
		e_st_bounce_ball,
		e_st_eclipse,
		e_st_ingyang,
		e_st_bouncedots,
		e_st_fadedots,
		e_st_scaledots,
		e_st_incscaledots,
		e_st_movingdots,
		e_st_rotatedots,
		e_st_hbodots,
		e_st_swingdots,
		e_st_clock,
		e_st_barchartsine,
		e_st_twinang180,
		e_st_incdots,
		e_st_incfulldots,
		e_st_wavedots,
		e_st_zipdots,
		e_st_barsrotatefade,
		e_st_fadebars,
		e_st_pulsar,
		e_st_barchartrainbow,
		e_st_angtwin,
		e_st_twinpulsar,
		e_st_blocks,
		e_st_twinball,
		e_st_gooeyballs,
		e_st_moonline,
		e_st_fluid,
		e_st_arcfade,
		e_st_fadepulsar,
		e_st_filledarcfade,
		e_st_rotatedatom,
		e_st_rainbowballs,
		e_st_scaleblocks,
		e_st_sinsquares,
		e_st_trianglesshift,
		e_st_circularlines,
		e_st_patternrings,
		e_st_pointsshift,
		e_st_circularpoints,
		e_st_curvedcircle,
		e_st_patterneclipse,
		e_st_rainbowshot,
		e_st_spiral,
		e_st_dnadots,
		e_st_solarballs,
		e_st_rotatingheart,
		e_st_fluidpoints,
		e_st_dotstopoints,
		e_st_threedots,
		e_st_caleidospcope,
		e_st_fivedots,
		e_st_herbertballs,
		e_st_herbertballs3d,
		e_st_squareloading,
		e_st_textfading,
		e_st_twinang360,
		e_st_pulsarball,
		e_st_rainbowmix,
		e_st_angmix,
		e_st_twinhbodots,
		e_st_moondots,
		e_st_rotatesegmentspulsar,
		e_st_pointsarcbounce,
		e_st_somescaledots,
		e_st_count
	};
	class TOAPI UILoading : public UIControl
	{
	public:
		UILoading(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0);
		//设置样式
		void SetStyle(info_loading_style style = e_st_rainbow);
		INT GetStyle();

		//设置Rainbow参数
		void SetRainbow(float radius, float thickness, float velocity, float ang_min, float ang_max, int arcs, int mode);
		//设置Dots参数
		void SetDots(float radius, float thickness, float velocity, size_t dots, float minth, int mode);
		//设置Ang参数
		void SetAng(float radius, float thickness, float velocity, float angle = 1.f, int mode = 0);
		//设置VDots参数
		void SetVDots(float radius, float thickness, float velocity, size_t dots = 12, size_t mdots = 6, int mode = 0);
		//设置BounceBall参数
		void SetBounceBall(float radius, float thickness, float velocity, int dots = 1, bool shadow = false);
		//设置AngEclipse参数
		void SetAngEclipse(float radius, float thickness, float velocity, float angle);
		//设置IngYang参数
		void SetIngYang(float radius, float thickness, bool reverse, float yang_detlta_r, float velocity, float angle);
		//设置BounceDots参数
		void SetBounceDots(float radius, float thickness, float velocity, size_t dots = 3, int mode = 0);
		//设置FadeDots参数
		void SetFadeDots(float radius, float thickness, float velocity, int mode = 0);
		//设置ScaleDots参数
		void SetScaleDots(float radius, float thickness, float velocity);
		//设置IncScaleDots参数
		void SetIncScaleDots(float radius, float thickness, float velocity, size_t dots = 6, float angle = 0.f, int mode = 0);
		//设置MovingDots参数
		void SetMovingDots(float radius, float thickness, float velocity, size_t dots = 3);
		//设置RotateDots参数
		void SetRotateDots(float radius, float thickness, float velocity, int dots = 2, int mode = 0);
		//设置Clock参数
		void SetClock(float radius, float thickness, float velocity);
		//设置BarChartSine参数
		void SetBarChartSine(float radius, float thickness, float velocity, int bars = 5, int mode = 0);
		//设置TwinAng180参数
		void SetTwinAng180(float radius1, float radius2, float thickness, float velocity, float angle = 0.f, int mode = 0);
		//设置IncDots参数
		void SetIncDots(float radius, float thickness, float velocity, size_t dots = 6);
		//设置IncFullDots参数
		void SetIncFullDots(float radius, float thickness, float velocity, size_t dots = 4);
		//设置BarsRotateFade参数
		void SetBarsRotateFade(float rmin, float rmax, float thickness, float velocity, size_t bars = 6);
		//设置FadeBars参数
		void SetFadeBars(float w, float velocity, size_t bars = 3, bool scale = false);
		//设置Pulsar参数
		void SetPulsar(float radius, float thickness, float velocity, bool sequence = true, float angle = 0.f, int mode = 0, bool isdots = false);
		//设置BarChartRainbow参数
		void SetBarChartRainbow(float radius, float thickness, float velocity, int bars = 5, int mode = 0, bool fColor = false);
		//设置AngTwin参数
		void SetAngTwin(float radius1, float radius2, float thickness, float velocity, float angle = 0.f, size_t arcs = 1, int mode = 0);
		//设置TwinPulsar参数
		void SetTwinPulsar(float radius, float thickness, float velocity, int rings = 2, int mode = 0);
		//设置Blocks参数
		void SetBlocks(float radius, float thickness, float velocity);
		//设置TwinBall参数
		void SetTwinBall(float radius1, float radius2, float thickness, float b_thickness, float velocity, size_t balls = 2);
		//设置GooeyBalls参数
		void SetGooeyBalls(float radius, float velocity, int mode = 0);
		//设置MoonLine参数
		void SetMoonLine(float radius, float thickness, float velocity, float angle = 0.f);
		//设置Fluid参数
		void SetFluid(float radius, float velocity, int bars = 3);
		//设置ArcFade参数
		void SetArcFade(float radius, float thickness, float velocity, size_t arcs = 4, int mode = 0);
		//设置FadePulsar参数
		void SetFadePulsar(float radius, float velocity, int rings = 2, int mode = 0);
		//设置FilledArcFade参数
		void SetFilledArcFade(float radius, float velocity, size_t arcs = 4, int mode = 0);
		//设置RotatedAtom参数
		void SetRotatedAtom(float radius, float thickness, float velocity, int elipses = 3, int mode = 0);
		//设置RainbowBalls参数
		void SetRainbowBalls(float radius, float thickness, float velocity, int balls = 5, int mode = 0);
		//设置ScaleBlocks参数
		void ScaleBlocks(float radius, float thickness, float velocity, int mode = 0);
		//设置HboDots参数
		void SetHboDots(float radius, float thickness, float minfade = 0.0f, float ryk = 0.f, float velocity = 1.f, size_t dots = 6);
		//设置SwingDots参数
		void SetSwingDots(float radius, float thickness, float velocity);
		//设置WaveDots参数
		void SetWaveDots(float radius, float thickness, float velocity);
		//设置SinSquares参数
		void SetSinSquares(float radius, float thickness, float velocity, int mode = 0);
		//设置ZipDots参数
		void SetZipDots(float radius, float thickness, float velocity, size_t dots = 5, float offset_k = 0.f, int mode = 0);
		//设置TrianglesShift参数
		void SetTrianglesShift(float radius, float thickness, float velocity, size_t bars = 8);
		//设置CircularLines参数
		void SetCircularLines(float radius, float velocity, int lines = 8, int mode = 0);
		//设置PatternRings参数
		void SetPatternRings(float radius, float thickness, float velocity, int elipses = 3, int mode = 0);
		//设置PointsShift参数
		void SetPointsShift(float radius, float thickness, float velocity, size_t bars = 8);
		//设置CircularPoints参数
		void SetCircularPoints(float radius, float thickness, float velocity, int lines = 8);
		//设置CurvedCircle参数
		void SetCurvedCircle(float radius, float thickness, float velocity, size_t circles = 1);
		//设置PatternEclipse参数
		void SetPatternEclipse(float radius, float thickness, float velocity, int elipses = 3, float delta_a = 2.f, float delta_y = 0.f);
		//设置RainbowShot参数
		void SetRainbowShot(float radius, float thickness, float velocity, int balls = 5);
		//设置Spiral参数
		void SetSpiral(float radius, float thickness, float velocity, size_t arcs = 4, int mode = 0);
		//设置DnaDots参数
		void SetDnaDots(float radius, float thickness, float velocity, float delta = 0.5f, bool mode = 0);
		//设置SolarBalls参数
		void SetSolarBalls(float radius, float thickness, float velocity, size_t balls = 4, bool Scale = 0);
		//设置RotatingHeart参数
		void SetRotatingHeart(float radius, float thickness, float velocity, float ang_min = 0.f);
		//设置FluidPoints参数
		void SetFluidPoints(float radius, float thickness, float velocity, size_t dots = 6, float delta = 0.35f);
		//设置DotsToPoints参数
		void SetDotsToPoints(float radius, float thickness, float offset_k, float velocity, size_t dots = 5);
		//设置ThreeDots参数
		void SetThreeDots(float radius, float thickness, float velocity);
		//设置Caleidospcope参数
		void SetCaleidospcope(float radius, float thickness, float velocity);
		//设置FiveDots参数
		void SetFiveDots(float radius, float thickness, float velocity);
		//设置HerbertBalls参数
		void SetHerbertBalls(float radius, float thickness, float velocity, int balls);
		//设置HerbertBalls3D参数
		void SetHerbertBalls3D(float radius, float thickness, float velocity);
		//设置SquareLoading参数
		void SetSquareLoading(float radius, float thickness, float velocity);
		//设置TwinAng360参数
		void SetTwinAng360(float radius1, float radius2, float thickness, float velocity, float speed2 = 2.5f, int mode = 0);
		//设置PulsarBall参数
		void SetPulsarBall(float radius, float thickness, float velocity, bool shadow = false, int mode = 0);
		//设置RainbowMix参数
		void SetRainbowMix(float radius, float thickness, float velocity, float ang_min = 0.f, float ang_max = 0.f, int arcs = 1, int mode = 0);
		//设置AngMix参数
		void SetAngMix(float radius, float thickness, float velocity, float angle = 0.f, int arcs = 4, int mode = 0);
		//设置TwinHboDots参数
		void SetTwinHboDots(float radius, float thickness, float minfade = 0.0f, float ryk = 0.f, float velocity = 1.1f, size_t dots = 6, float delta = 0.f);
		//设置MoonDots参数
		void SetMoonDots(float radius, float thickness, float velocity);
		//设置RotateSegmentsPulsar参数
		void SetRotateSegmentsPulsar(float radius, float thickness, float velocity = 2.8f, size_t arcs = 4, size_t layers = 1);
		//设置PointsArcBounce参数
		void SetPointsArcBounce(float radius, float thickness, float velocity = 2.8f, size_t points = 4, int circles = 2, float rspeed = 0.f);
		//设置SomeScaleDots参数
		void SetSomeScaleDots(float radius, float thickness, float velocity = 2.8f, size_t dots = 6, int mode = 0);


		//设置颜色
		void SetCrHue(UIColor normal);
		void GetCrHue(UIColor& normal);

		//设置背景颜色
		void SetCrBg(UIColor bg);
		void GetCrBg(UIColor& bg);

		void SetRadius(float radius);
		float GetRadius();

		void SetThickness(float thickness);
		void SetSpeed(float speed);
		void SetVelocity(float velocity);
		void SetAng_min(float ang_min);
		void SetAng_max(float ang_max);
		void SetArcs(int arcs);
		void SetMode(int mode);
		int GetMode();
		void SetNextdot(float* nextdot);
		void SetDots(size_t dots);
		size_t GetDots();

		void SetMdots(size_t mdots);
		void SetMinth(float minth);
		float GetMinth();
		void SetAngle(float angle);






	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		int CalcCircleAutoSegmentCount(float radius);
		void SetCircleTessellationMaxError(float max_error);

		void Rainbow(ps_context ps, float radius, float thickness, float speed, float ang_min = 0.f, float ang_max = 5.f, int arcs = 1, int mode = 0);
		void Dots(ps_context ps, float* nextdot, float radius, float thickness, float speed = 2.8f, size_t dots = 12, float minth = -1.f, int mode = 0);
		void Ang(ps_context ps, float radius, float thickness, float speed = 2.8f, float angle = 1.f, int mode = 0);
		void VDots(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 12, size_t mdots = 6, int mode = 0);
		void BounceBall(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 1, bool shadow = false);
		void AngEclipse(ps_context ps, float radius, float thickness, float speed = 2.8f, float angle = 0.f);
		void IngYang(ps_context ps, float radius, float thickness, bool reverse, float yang_detlta_r, float speed = 2.8f, float angle = 0.f);
		void BounceDots(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 3, int mode = 0);
		void FadeDots(ps_context ps, float radius, float thickness, float speed = 2.8f, int mode = 0);
		void ScaleDots(ps_context ps, float radius, float thickness, float speed = 2.8f);
		void IncScaleDots(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 6, float angle = 0.f, int mode = 0);
		void MovingDots(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 3);
		void RotateDots(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 2, int mode = 0);
		void Clock(ps_context ps, float radius, float thickness, float speed = 2.8f);
		void BarChartSine(ps_context ps, float radius, float thickness, float speed, size_t bars = 5, int mode = 0);
		void TwinAng180(ps_context ps, float radius1, float radius2, float thickness, float speed = 2.8f, float angle = 0.f, int mode = 0);
		void IncDots(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 6);
		void IncFullDots(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 4);
		void BarsRotateFade(ps_context ps, float rmin, float rmax, float thickness, float speed = 2.8f, size_t bars = 6);
		void FadeBars(ps_context ps, float w, float speed = 2.8f, size_t bars = 3, bool scale = false);
		void Pulsar(ps_context ps, float radius, float thickness, float speed = 2.8f, bool sequence = true, float angle = 0.f, int mode = 0, bool isdots = false);
		void BarChartRainbow(ps_context ps, float radius, float thickness, float speed, size_t bars = 5, int mode = 0, bool fColor = false);
		void AngTwin(ps_context ps, float radius1, float radius2, float thickness, float speed = 2.8f, float angle = 0.f, size_t arcs = 1, int mode = 0);
		void TwinPulsar(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t rings = 2, int mode = 0);
		void Blocks(ps_context ps, float radius, float thickness, float speed);
		void TwinBall(ps_context ps, float radius1, float radius2, float thickness, float b_thickness, float speed = 2.8f, size_t balls = 2);
		void GooeyBalls(ps_context ps, float radius, float speed, int mode = 0);
		void MoonLine(ps_context ps, float radius, float thickness, float speed = 2.8f, float angle = 0.f);
		void Fluid(ps_context ps, float radius, float speed, size_t bars = 3);
		void ArcFade(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t arcs = 4, int mode = 0);
		void FadePulsar(ps_context ps, float radius, float speed = 2.8f, size_t rings = 2, int mode = 0);
		void FilledArcFade(ps_context ps, float radius, float speed = 2.8f, size_t arcs = 4, int mode = 0);
		void RotatedAtom(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t elipses = 3, int mode = 0);
		void RainbowBalls(ps_context ps, float radius, float thickness, float speed, size_t balls = 5, int mode = 0);
		void ScaleBlocks(ps_context ps, float radius, float thickness, float speed, int mode = 0);
		void HboDots(ps_context ps, float radius, float thickness, float minfade = 0.0f, float ryk = 0.f, float speed = 1.1f, size_t dots = 6);
		void SwingDots(ps_context ps, float radius, float thickness, float speed = 2.8f);
		void WaveDots(ps_context ps, float radius, float thickness, float speed = 2.8f);
		void SinSquares(ps_context ps, float radius, float thickness, float speed, int mode = 0);
		void ZipDots(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 5, float offset_k = 0.f, int mode = 0);
		void TrianglesShift(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t bars = 8);
		void CircularLines(ps_context ps, float radius, float speed = 1.8f, size_t lines = 8, int mode = 0);
		void PatternRings(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t elipses = 3, int mode = 0);
		void PointsShift(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t bars = 8);
		void CircularPoints(ps_context ps, float radius, float thickness, float speed = 1.8f, size_t lines = 8);
		void CurvedCircle(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t circles = 1);
		void PatternEclipse(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t elipses = 3, float delta_a = 2.f, float delta_y = 0.f);
		void RainbowShot(ps_context ps, float radius, float thickness, float speed, size_t balls = 5);
		void Spiral(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t arcs = 4, int mode = 0);
		void DnaDots(ps_context ps, float radius, float thickness, float speed = 2.8f, float delta = 0.5f, bool mode = 0);
		void SolarBalls(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t balls = 4, bool Scale = 0);
		void RotatingHeart(ps_context ps, float radius, float thickness, float speed, float ang_min = 0.f);
		void FluidPoints(ps_context ps, float radius, float thickness, float speed, size_t dots = 6, float delta = 0.35f);
		void DotsToPoints(ps_context ps, float radius, float thickness, float offset_k, float speed = 1.8f, size_t dots = 5);
		void ThreeDots(ps_context ps, float radius, float thickness, float speed = 2.8f);
		void Caleidospcope(ps_context ps, float radius, float thickness, float speed = 2.8f);
		void FiveDots(ps_context ps, float radius, float thickness, float speed = 2.8f);
		void HerbertBalls(ps_context ps, float radius, float thickness, float speed, size_t balls);
		void HerbertBalls3D(ps_context ps, float radius, float thickness, float speed);
		void SquareLoading(ps_context ps, float radius, float thickness, float speed = 2.8f);
		void TextFading(ps_context ps);
		void TwinAng360(ps_context ps, float radius1, float radius2, float thickness, float speed1 = 2.8f, float speed2 = 2.5f, int mode = 0);
		void PulsarBall(ps_context ps, float radius, float thickness, float speed = 2.8f, bool shadow = false, int mode = 0);
		void RainbowMix(ps_context ps, float radius, float thickness, float speed, float ang_min = 0.f, float ang_max = 0.f, size_t arcs = 1, int mode = 0);
		void AngMix(ps_context ps, float radius, float thickness, float speed = 2.8f, float angle = 0.f, size_t arcs = 4, int mode = 0);
		void TwinHboDots(ps_context ps, float radius, float thickness, float minfade = 0.0f, float ryk = 0.f, float speed = 1.1f, size_t dots = 6, float delta = 0.f);
		void MoonDots(ps_context ps, float radius, float thickness, float speed = 1.1f);
		void RotateSegmentsPulsar(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t arcs = 4, size_t layers = 1);
		void PointsArcBounce(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t points = 4, size_t circles = 2, float rspeed = 0.f);
		void SomeScaleDots(ps_context ps, float radius, float thickness, float speed = 2.8f, size_t dots = 6, int mode = 0);


		struct Loading_s
		{
			float radius = 16.f, CircleSegmentMaxError = 0.f, thickness = 6.f, speed = 8.f, ang_min = 0.f, 
				ang_max = 5.f, minth = -1.f, velocity = 0.f, angle = 1.f, vtime = 0.f;
			float* nextdot = nullptr;
			size_t dots = 12, mdots = 6;
			int arcs = 1, mode = 0;
			bool shadow = false, isdots = false;
			UIBrush* hBrush = nullptr;
			info_loading_style style = e_st_rainbow;
			BYTE CircleSegmentCounts[64];
			UIPath* pathGeometry = nullptr;
			UIColor crHue,crbg;
		}p_data;

	};
}
