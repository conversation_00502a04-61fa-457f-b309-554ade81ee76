﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="application">
      <UniqueIdentifier>{a73f32f5-27e8-448d-9fd2-4a9f6aacb3cb}</UniqueIdentifier>
    </Filter>
    <Filter Include="engine">
      <UniqueIdentifier>{b9734cac-0d8b-4805-adf4-7fee48ecd6bc}</UniqueIdentifier>
    </Filter>
    <Filter Include="common">
      <UniqueIdentifier>{7730cd09-4b49-434b-8624-52c15b9ee37a}</UniqueIdentifier>
    </Filter>
    <Filter Include="element">
      <UniqueIdentifier>{6d3e9874-dda6-413f-b7dd-f972af006a2c}</UniqueIdentifier>
    </Filter>
    <Filter Include="control">
      <UniqueIdentifier>{244f2397-1948-4d64-aa41-b6f8f5746e0d}</UniqueIdentifier>
    </Filter>
    <Filter Include="ThirdParty">
      <UniqueIdentifier>{0229aabe-da87-4092-80f6-537e64899e70}</UniqueIdentifier>
    </Filter>
    <Filter Include="ThirdParty\pugixml">
      <UniqueIdentifier>{9e6b15da-8a1a-4791-9695-351157e57b5e}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\HHBUI\engine\engine.cpp">
      <Filter>engine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\common\status_handle.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\common\atom.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\common\winapi.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\engine\renderd2d.cpp">
      <Filter>engine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\font_pool.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\font.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\common\data.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\common\memory.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\common\res_pool.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\wnd.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\hook.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\common\mem_dc.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\image.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\control.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\combutton.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\pch.cpp" />
    <ClCompile Include="..\..\HHBUI\element\canvas.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\brush.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\array.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\layout.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\ThirdParty\pugixml\pugixml.cpp">
      <Filter>ThirdParty\pugixml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\application\config.cpp">
      <Filter>application</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\button.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\static.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\page.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\item.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\menu.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\scroll.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\listview.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\list.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\path.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\region.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\engine\animation.cpp">
      <Filter>engine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\engine\base.cpp">
      <Filter>engine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\badge.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\check.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\color.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\edit.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\resource.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\common\ziparchive.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\element\droptarget.cpp">
      <Filter>element</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\tabs.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\progress.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\slider.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\groupbox.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\hotkey.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\imagebox.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\colorpicker.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\combobox.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\treeview.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\table.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\common\assist.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\loading.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\wrappanel.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\knobs.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\datebox.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\waveringview.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\tour.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\miniblink.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\chart.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\segmented.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\timeline.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\splashscreen.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\control\login.cpp">
      <Filter>control</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\engine\dx11_buffer.cpp">
      <Filter>engine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\engine\dx11_render_manager.cpp">
      <Filter>engine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\engine\dx11_shader.cpp">
      <Filter>engine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\engine\gdi_plus_integration.cpp">
      <Filter>engine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\engine\render_integration.cpp">
      <Filter>engine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\engine\render_profiler.cpp">
      <Filter>engine</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\HHBUI\application\define.h">
      <Filter>application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\engine.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\Exception.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\status_handle.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\unknown_impl.hpp">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\atom.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\winapi.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\renderd2d.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\font_pool.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\font.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\singleton.hpp">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\data.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\memory.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\application\config.h">
      <Filter>application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\hhbui.h" />
    <ClInclude Include="..\..\HHBUI\common\res_pool.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\mem_pool.hpp">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\object_impl.hpp">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\wnd.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\hook.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\coordinate.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\text_render.hpp">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\matrix.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\mem_dc.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\image.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\control.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\combutton.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\pch.h" />
    <ClInclude Include="..\..\HHBUI\element\canvas.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\brush.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\array.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\layout.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\ThirdParty\pugixml\pugiconfig.hpp">
      <Filter>ThirdParty\pugixml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\ThirdParty\pugixml\pugixml.hpp">
      <Filter>ThirdParty\pugixml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\button.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\static.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\page.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\item.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\menu.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\scroll.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\listview.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\list.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\path.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\region.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\animation.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\base.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\lock.hpp">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\badge.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\check.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\color.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\edit.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="resource.h" />
    <ClInclude Include="..\..\HHBUI\common\auto_ptr.hpp">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\resource.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\ziparchive.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\element\droptarget.h">
      <Filter>element</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\tabs.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\progress.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\slider.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\groupbox.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\hotkey.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\imagebox.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\colorpicker.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\combobox.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\treeview.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\table.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\assist.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\loading.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\loading_internal.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\wrappanel.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\knobs.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\datebox.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\datebox_lunarday.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\waveringview.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\vstring.hpp">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\tour.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\miniblink.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\chart.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\segmented.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\timeline.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\splashscreen.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\control\login.h">
      <Filter>control</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\common\UIString.hpp">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\dx11_buffer.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\dx11_render_manager.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\dx11_shader.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\gdi_plus_integration.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\render_api.h">
      <Filter>engine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\HHBUI\engine\render_profiler.h">
      <Filter>engine</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="hhbui_lib.rc" />
  </ItemGroup>
</Project>