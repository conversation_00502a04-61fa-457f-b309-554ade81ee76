﻿#include "pch.h"
#include "control.h"
#include <common/Exception.h>
#include <common/winapi.h>
#include <CommCtrl.h>
#include <strsafe.h>
#pragma comment(lib, "UxTheme.lib") 

BOOL HHBUI::UIControl::InitSubControl(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpClsname, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx,
    INT nID, INT dwTextFormat, LPARAM lParam, MsgPROC pfnCallback)
{
    if (hParent->m_UIWindow)
    {
        m_data.pWnd = (UIWnd*)hParent->m_UIWindow;
        m_data.ScaleWindow = TRUE;
    }
	else if((UIControl*)hParent->m_UIView)
		m_data.pWnd = ((UIControl*)hParent->m_UIView)->m_data.pWnd;
    if (!m_data.pWnd)
    {
        ExOutError(_M_DBG_INFO_(M_INFO_DBG), L"没有找到可用的父控件！");
        return FALSE;
    }
    m_data.pfnSubClass = pfnCallback;
    m_UIView = this;
    m_UIWindow = nullptr;
    m_data.Parent = hParent;
    m_data.lParam = lParam;
    RECT rcObj{ 0 };
    if ((dwStyle & eos_nodpiscale) == eos_nodpiscale)
    {
        rcObj.left = x + (m_data.ScaleWindow ? m_data.pWnd->m_data.size_tmp : 0);
        rcObj.top = y + (m_data.ScaleWindow ? m_data.pWnd->m_data.size_tmp : 0);
        rcObj.right = rcObj.left + width;
        rcObj.bottom = rcObj.top + height;
    }
    else
    {
        rcObj.left = UIEngine::fScale(x + (m_data.ScaleWindow ? m_data.pWnd->m_data.size_tmp : 0.f));
        rcObj.top = UIEngine::fScale(y + (m_data.ScaleWindow ? m_data.pWnd->m_data.size_tmp : 0.f));
        rcObj.right = rcObj.left + UIEngine::fScale(width);
        rcObj.bottom = rcObj.top + UIEngine::fScale(height);
    }
    m_data.Frame = rcObj;
	m_data.Frame_c = rcObj;
    m_data.dwStyle = dwStyle;
    if (dwStyleEx == 0)
        dwStyleEx = eos_ex_focusable;
    m_data.dwStyleEx = dwStyleEx;
    m_data.dwTextFormat = (dwTextFormat == -1 ? SingleLine | Center | Middle : dwTextFormat);
    SetCursor(IDC_ARROW);
    if (nID > 0)
	{
		m_data.nID = nID;//通过ID存储
		m_data.pWnd->m_hTableObjects.insert(std::make_pair(std::to_wstring(nID).c_str(), m_UIView));
	}
    m_data.pWnd->m_hTableObjects.insert(std::make_pair(lpClsname, m_UIView));
	m_data.alpha = 255;
    m_data.canvas = new UICanvas(m_data.pWnd, rcObj.right - rcObj.left, rcObj.bottom - rcObj.top);
	m_data.lpClsname = StrDupW(lpClsname);
    if ((dwStyleEx & eos_ex_acceptfiles) == eos_ex_acceptfiles)
    {
        HRESULT hr = OleInitialize(0); //可能已经初始化
        try
        {
            m_data.lpIDropTarget = new HHBUI::DropTarget(m_data.pWnd);
            throw_if_failed(RegisterDragDrop(m_data.pWnd->GethWnd(), (HHBUI::DropTarget*)(m_data.lpIDropTarget)), L"注册拖放信息失败");
        }
        catch_continue({ if (m_data.lpIDropTarget) {
            delete (HHBUI::DropTarget*)m_data.lpIDropTarget;
            m_data.lpIDropTarget = nullptr;
        } });
       
    }
    if ((dwStyle & eos_hidden) != eos_hidden)
    {
        m_data.dwState |=  state_hidden;
    }
    if ((dwStyle & eos_disabled) == eos_disabled)
    {
        m_data.dwState |= state_disable;
    }
    if ((dwStyleEx & eos_ex_focusable) == eos_ex_focusable)
    {
        m_data.dwState |= state_allowfocus;
    }
    //开始处理滚动条
    INT style = ess_rightbottomalign | eos_hidden;

    if ((dwStyle & eos_scroll_disableno) == eos_scroll_disableno)
    {
        style |= eos_scroll_disableno;
    }
    if ((dwStyle & eos_scroll_amds) == eos_scroll_amds)
    {
        style |= eos_scroll_amds;
    }
    if ((dwStyle & eos_scroll_controlbutton) == eos_scroll_controlbutton)
    {
        style |= ess_controlbutton;
    }
    if ((dwStyle & eos_scroll_v) == eos_scroll_v)
    {
        m_data.objVScroll = new UIScroll(this, 0, 0, 0, 0, style | ess_verticalscroll, eos_ex_topmost);
    }
    if ((dwStyle & eos_scroll_h) == eos_scroll_h)
    {
        m_data.objHScroll = new UIScroll(this, 0, 0, 0, 0, style | ess_horizontalscroll, eos_ex_topmost);

    }
    INT flags = SWP_NOACTIVATE | SWP_NOCOPYBITS | SWP_DRAWFRAME | SWP_EX_NODPISCALE;
    if ((dwStyle & eos_hidden) != eos_hidden)
        flags |= SWP_SHOWWINDOW;
    
    //m_data.Color.crBackground = {};
    //m_data.Color.crBorder = {};
    m_data.Color.crNormal = UIColor(0, 0, 0, 255);
    m_data.Color.crHover = UIColor(0, 0, 0, 255);
    m_data.Color.crDown = UIColor(0, 0, 0, 255);
    //m_data.Color.crFocus = UIColor(0, 108, 190, 255);
    m_data.Color.crBan = UIColor(100, 100, 100, 255);
    //m_data.Color.crShadow = {};
    m_data.lpBackgBrush = new UIBrush(m_data.Color.crBackground);

   
    OnBaseProc(m_data.pWnd->GethWnd(), WM_NCCREATE, 0, 0);
    OnBaseProc(m_data.pWnd->GethWnd(), WM_CREATE, 0, 0);
    if (lpszName)
    {
        m_data.pstrTitle = StrDupW(lpszName);
        OnBaseProc(GethWnd(), WM_SETTEXT, 0, 0);
    }
    SetPos(m_data.Frame.left, m_data.Frame.top, rcObj.right - rcObj.left, rcObj.bottom - rcObj.top,
        ((dwStyleEx & eos_ex_bottom) == eos_ex_bottom ? HWND_BOTTOM : HWND_TOP), flags);
    FLAGS_ADD(m_data.dwFlags, EOF_INITED);
    return TRUE;
}

HHBUI::UIControl::~UIControl()
{
    if (m_UIView)
        OnBaseProc(m_data.pWnd->GethWnd(), WM_DESTROY, 0, 0);
}
BOOL HHBUI::UIControl::SetParent(UIBase *hParent)
{
    if (m_data.canvas && m_data.Parent != hParent)
    {
        m_data.ScaleWindow = FALSE;
        if (hParent->m_UIWindow)
        {
            m_data.pWnd = (UIWnd*)hParent->m_UIWindow;
            m_data.ScaleWindow = TRUE;
        }
        else if ((UIControl*)hParent->m_UIView)
            m_data.pWnd = ((UIControl*)hParent->m_UIView)->m_data.pWnd;
        if (!m_data.pWnd)
        {
            ExOutError(_M_DBG_INFO_(M_INFO_DBG), L"没有找到可用的父控件！");
            return FALSE;
        }
        delete m_data.canvas;
        ExRectF rcObj{};
        GetRect(rcObj, 0, TRUE);
        m_data.canvas = new UICanvas(m_data.pWnd, rcObj.right - rcObj.left, rcObj.bottom - rcObj.top);
        m_data.Parent = hParent;
        SetPos(m_data.Frame.left, m_data.Frame.top, rcObj.right - rcObj.left, rcObj.bottom - rcObj.top, HWND_TOP, SWP_NOACTIVATE | SWP_NOCOPYBITS | SWP_DRAWFRAME | SWP_EX_NODPISCALE);
        //m_data.pWnd->Update();
        return TRUE;
    }
    return FALSE;
}

void HHBUI::UIControl::Move(INT x, INT y, INT width, INT height, BOOL bRepaint, BOOL bNodpiscale)
{
    INT flags = SWP_NOZORDER | SWP_NOACTIVATE | SWP_NOOWNERZORDER | SWP_ASYNCWINDOWPOS;
    if (x == CW_USEDEFAULT && y == CW_USEDEFAULT)
    {
        flags = flags | SWP_NOMOVE;
    }
    if (width == CW_USEDEFAULT && height == CW_USEDEFAULT)
    {
        flags = flags | SWP_NOSIZE;
    }
    if (bRepaint)
        flags = flags | SWP_EX_UPDATEOBJECT;
    if (bNodpiscale)
        flags = flags | SWP_EX_NODPISCALE;

    SetPos(x, y, width, height, 0, flags);
}

void HHBUI::UIControl::SetPos(INT x, INT y, INT width, INT height, HWND hObjInsertAfter, INT flags)
{
    if (!m_data.pWnd) return;
    if (width < 0 && width != CW_USEDEFAULT) width = 0;
    if (height < 0 && height != CW_USEDEFAULT) height = 0;
    HWND hWnd = m_data.pWnd->m_data.hWnd; 
    BOOL fAsyn = (flags & SWP_ASYNCWINDOWPOS) != 0;
    BOOL fNotify = (flags & SWP_NOSENDCHANGING) == 0;
    if ((flags & SWP_NOZORDER) == 0) obj_zset(hObjInsertAfter);
    if (obj_autosize(&width, &height))
    {
        flags = flags - (flags & SWP_NOSIZE);
        flags = flags | SWP_EX_NODPISCALE;
    }
    NCCALCSIZE_PARAMS np = { 0 };
    if ((flags & SWP_NOMOVE) == 0 || (flags & SWP_NOSIZE) == 0 || (flags & SWP_DRAWFRAME) != 0)
    {
        BOOL fScale = (flags & SWP_EX_NODPISCALE) == 0;
        if ((flags & SWP_NOMOVE) == 0) //移动
        {
            if (x == CW_USEDEFAULT)
            {
                x = m_data.Frame.left;
            }
            else
            {
                if (fScale)
                {
                    x = UIEngine::fScale(x);
                }
            }
            if (y == CW_USEDEFAULT)
            {
                y = m_data.Frame.top;
            }
            else
            {
                if (fScale)
                {
                    y = UIEngine::fScale(y);
                }
            }
        }
        if ((flags & SWP_NOSIZE) == 0) //修改尺寸
        {
            if (width == CW_USEDEFAULT)
            {
                width = m_data.Frame.right - m_data.Frame.left;
            }
            else
            {
                if (fScale)
                {
                    width = UIEngine::fScale(width);
                }
            }
            if (height == CW_USEDEFAULT)
            {
                height = m_data.Frame.bottom - m_data.Frame.top;
            }
            else
            {
                if (fScale)
                {
                    height = UIEngine::fScale(height);
                }
            }
        }

        WINDOWPOS wp{};
        wp.hwnd = hWnd;
        wp.hwndInsertAfter = hObjInsertAfter;
        wp.x = x;
        wp.y = y;
        wp.cx = width;
        wp.cy = height;
        wp.flags = flags;
        np.lppos = &wp;
      
        if (fNotify)
        {
            if (fAsyn)
            {
                SendMsg(WM_WINDOWPOSCHANGING, 0, (size_t)np.lppos);
                SendMsg(WM_GETMINMAXINFO, 0, (size_t)m_data.minmax);
            }
            else
            {
                OnBaseProc(hWnd, WM_WINDOWPOSCHANGING, 0, (size_t)np.lppos);
                OnBaseProc(hWnd, WM_GETMINMAXINFO, 0, (size_t)m_data.minmax);
            }
        }

        //WM_NCCALCSIZE
        np.rgrc[1] = m_data.Frame.ToRect();
        if ((flags & SWP_NOMOVE) != 0) //老位置
        {
            np.rgrc[0].left = np.rgrc[1].left;
            np.rgrc[0].top = np.rgrc[1].top;
        }
        else
        {
            np.rgrc[0].left = x;
            np.rgrc[0].top = y;
        }
        if ((flags & SWP_NOSIZE) != 0) //老尺寸
        {
            np.rgrc[0].right = np.rgrc[0].left + np.rgrc[1].right - np.rgrc[1].left;
            np.rgrc[0].bottom = np.rgrc[0].top + np.rgrc[1].bottom - np.rgrc[1].top;
        }
        else
        {
            np.rgrc[0].right = np.rgrc[0].left + width;
            np.rgrc[0].bottom = np.rgrc[0].top + height;
        }
        np.rgrc[2] = np.rgrc[0];
        m_data.Frame = np.rgrc[0];
        OffsetRect(&np.rgrc[2], (-np.rgrc[0].left), (-np.rgrc[0].top));
        m_data.Frame_c = np.rgrc[2];
     
        if (fNotify)
        {
            if (fAsyn)
                SendMsg(WM_NCCALCSIZE, 1, (size_t)&np);
            else
                OnBaseProc(hWnd, WM_NCCALCSIZE, 1, (size_t)&np);
        }
        RECT rcOld = m_data.Frame_w.ToRect();

        obj_updatewindowpostion((flags & SWP_NOMOVE) == 0);
        //更新被修改后的位置信息
        if ((flags & SWP_NOMOVE) == 0)
        {
            x = np.rgrc[0].left;
            y = np.rgrc[0].top;
        }
        if ((flags & SWP_NOSIZE) == 0)
        {
            width = np.rgrc[0].right - np.rgrc[0].left;
            height = np.rgrc[0].bottom - np.rgrc[0].top;
        }
        if (FLAGS_CHECK(m_data.dwFlags, EOF_BPATH))
        {
            obj_regionalpath(this, np.rgrc[2].left + 1.f, np.rgrc[2].top + 1.f, np.rgrc[2].right - 1.f, np.rgrc[2].bottom - 1.f, m_data.hPath_Client);
            obj_regionalpath(this, m_data.Frame_w.left, m_data.Frame_w.top, m_data.Frame_w.right, m_data.Frame_w.bottom, m_data.hPath_Window);
            OnBaseProc(hWnd, WM_BPATHBYROUNDEDRECT, 0, 0);
        }
        if ((flags & SWP_NOSIZE) == 0)
        {
            m_data.canvas->Resize(width, height);
        }


        //WM_WINDOWPOSCHANGED 如果用户处理掉了，则不发送 WM_SIZE / WM_MOVE
        if (fNotify)
        {
            if (fAsyn)
                SendMsg(WM_WINDOWPOSCHANGED, 0, (size_t)&np.lppos);
            else
                OnBaseProc(hWnd, WM_WINDOWPOSCHANGED, 0, (size_t)&np.lppos);
        }
        if (((m_data.dwFlags & EOF_BUSERPROCESSESED) != EOF_BUSERPROCESSESED) && fNotify)
        {
            //应该得发送客户区矩形
            if ((flags & SWP_NOMOVE) == 0)
            {
                if (fAsyn)
                    SendMsg(WM_MOVE, 0, MAKELONG(x, y));
                else
                    OnBaseProc(hWnd, WM_MOVE, 0, MAKELONG(x, y));
            }

            if ((flags & SWP_NOSIZE) == 0)
            {
                if (fAsyn)
                    SendMsg(WM_SIZE, 0, MAKELONG(width, height));
                else
                    OnBaseProc(hWnd, WM_SIZE, 0, MAKELONG(width, height));
            }
        }
        obj_scrollrepostion(TRUE); //重新更新滚动条位置，与其状态无关
        RECT scr = m_data.Frame_w.ToRect();
        UnionRect(&np.rgrc[2], &scr, &rcOld);
    }
    else
    {
        np.rgrc[2] = m_data.Frame.ToRect();
    }

    if (((m_data.dwStyle & eos_hidden) != eos_hidden))
    {
        BOOL fScale = ((m_data.pWnd->m_data.dwFlags & EWF_SIZED) == EWF_SIZED);
        if ((flags & SWP_NOREDRAW) == 0) //禁止重画
        {
            Redraw();
            if (!fScale)
            {
                InvalidateRect(hWnd, 0, FALSE);
            }
        }
        if (!fScale)
        {
            if ((flags & SWP_EX_UPDATEOBJECT) == SWP_EX_UPDATEOBJECT)
            {
                UpdateWindow(hWnd);
            }
        }
    }
}

void HHBUI::UIControl::Redraw(ExRectF lprcRedraw)
{
    m_data.dwFlags |= EOF_BNEEDREDRAW;
    if (lprcRedraw.empty() || (m_data.dwStyleEx & eos_ex_composited) == eos_ex_composited)
    {
        m_data.Frame_d = m_data.Frame;
        m_data.Frame_d.Offset(-m_data.Frame.left, -m_data.Frame.top);
    }
    else
    {
        if (m_data.Frame_d.empty())
        {
            m_data.Frame_d = lprcRedraw;
        }
        else
        {
            m_data.Frame_d.UnionRect(m_data.Frame_d, lprcRedraw);
        }
    }
    auto pRC = m_data.Frame_d;
    pRC.Offset(m_data.Frame_w.left, m_data.Frame_w.top);
    if (obj_makeupinvalidaterect(pRC))
    {
        RECT scr = pRC.ToRect();
        InvalidateRect(m_data.pWnd->GethWnd(), &scr, FALSE);
    }
}

LPVOID HHBUI::UIControl::GetNode(INT nCmd)
{
    LPVOID ret = nullptr;
    if (nCmd == GW_CHILD)
    {
        ret = m_objChildFirst;
    }
    else if (nCmd == GW_HWNDFIRST)
    {
        ret = m_data.Parent->m_UIView;
        if (ret == 0)
        {
            ret = m_data.pWnd->m_objChildFirst;
        }
        else
        {
            ret = ((UIControl*)ret)->m_objChildFirst;
        }
    }
    else if (nCmd == GW_HWNDLAST)
    {
        ret = m_data.Parent->m_UIView;
        if (ret == 0)
        {
            ret = m_data.pWnd->m_objChildLast;
        }
        else
        {
            ret = ((UIControl*)ret)->m_objChildLast;
        }
    }
    else if (nCmd == GW_HWNDNEXT)
    {
        ret = m_data.objNext;
    }
    else if (nCmd == GW_HWNDPREV)
    {
        ret = m_data.objPrev;
    }
    else if (nCmd == GW_OWNER)
    {
        ret = m_UIWindow;
    }
    return ret;
}

void HHBUI::UIControl::GetColor(INT nIndex, UIColor& rel)
{
    switch (nIndex)
    {
    case color_background:
        rel = m_data.Color.crBackground;
        break;
    case color_border:
        rel = m_data.Color.crBorder;
        break;
    case color_text_normal:
        rel = m_data.Color.crNormal;
        break;
    case color_text_hover:
        rel = m_data.Color.crHover;
        break;
    case color_text_down:
        rel = m_data.Color.crDown;
        break;
    case color_focus:
        rel = m_data.Color.crFocus;
        break;
    case color_text_ban:
        rel = m_data.Color.crBan;
        break;
    case color_text_shadow:
        rel = m_data.Color.crShadow;
        break;
    default:
        return;
    }
}

BOOL HHBUI::UIControl::SetColor(INT nIndex, UIColor dwColor, BOOL fRedraw)
{
    switch (nIndex)
    {
    case color_background:
        m_data.Color.crBackground = dwColor;
        break;
    case color_border:
        m_data.Color.crBorder = dwColor;
        break;
    case color_text_normal:
        m_data.Color.crNormal = dwColor;
        break;
    case color_text_hover:
        m_data.Color.crHover = dwColor;
        break;
    case color_text_down:
        m_data.Color.crDown = dwColor;
        break;
    case color_focus:
        m_data.Color.crFocus = dwColor;
        break;
    case color_text_ban:
        m_data.Color.crBan = dwColor;
        break;
    case color_text_shadow:
        m_data.Color.crShadow = dwColor;
        break;
    default:
        return FALSE;
    }
    OnBaseProc(m_data.pWnd->m_data.hWnd, WM_SYSCOLORCHANGE, nIndex, 0);
    if (fRedraw)
    {
        Redraw();
    }
    return TRUE;
}

HHBUI::UIWnd* HHBUI::UIControl::GetUIWnd()
{
    return m_data.pWnd;
}

HWND HHBUI::UIControl::GethWnd()
{
    return m_data.pWnd->m_data.hWnd;
}

HHBUI::UIBase* HHBUI::UIControl::GetParent(BOOL tParent)
{
    if (tParent)
        return this;
    return m_data.Parent;
}

HHBUI::UIWnd* HHBUI::UIControl::GetParentUIWnd()
{
    return (UIWnd*)m_data.Parent->m_UIWindow;
}

LPCWSTR HHBUI::UIControl::GetlClassName()
{
    return m_data.lpClsname;
}

void HHBUI::UIControl::SetRadius(FLOAT topleft, FLOAT topright, FLOAT bottomright, FLOAT bottomleft, BOOL fUpdate)
{
    m_data.radius = { topleft ,topright ,bottomright ,bottomleft };

    if (m_data.radius.empty())
    {
        FLAGS_DEL(m_data.dwFlags, EOF_BPATH);
    }
    else
    {
        if (!FLAGS_CHECK(m_data.dwFlags, EOF_BPATH))
            FLAGS_ADD(m_data.dwFlags, EOF_BPATH);
        INT flags = SWP_NOZORDER | SWP_NOCOPYBITS | SWP_ASYNCWINDOWPOS | SWP_EX_UPDATEPATH;//移除SWP_NOSENDCHANGING DPI下会影响客户区顶边位置
        if (fUpdate)
            flags |= SWP_EX_UPDATEOBJECT;
     
        SetPos(CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT, 0, flags);
    }
}

BOOL HHBUI::UIControl::SetFontFromFamily(LPCWSTR lpszFontfamily, INT dwFontsize, INT dwFontstyle, BOOL fRedraw)
{
    if (!lpszFontfamily && dwFontsize == 0 && dwFontstyle == 0)
        return FALSE;
    auto hFont = new UIFont(lpszFontfamily, dwFontsize, dwFontstyle);
    return obj_setfont(hFont, fRedraw);
}
BOOL HHBUI::UIControl::SetFontLogFont(LOGFONTW* Fontinfo, BOOL fRedraw)
{
    auto hFont = new UIFont(Fontinfo);
    return obj_setfont(hFont, fRedraw);
}
HHBUI::UIFont* HHBUI::UIControl::GetFont()
{
    return (m_data.hFont == 0 ? UIWinApi::ToList.default_font : m_data.hFont);
}
BOOL HHBUI::UIControl::SetBackgImage(LPVOID lpImage, size_t dwImageLen, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    if (dwImageLen != 0)
    {     
        auto hImg = new UIImage(lpImage, dwImageLen);
        return obj_setbackgImage(hImg, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
    }
    return obj_setbackgImage(0, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
}
BOOL HHBUI::UIControl::SetBackgImage(UIImage *hImage, BOOL fReset, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    return obj_setbackgImage(hImage, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
}
BOOL HHBUI::UIControl::SetBackgImage(LPCWSTR lpImagefile, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    if (lpImagefile)
    {
        auto hImg = new UIImage(lpImagefile);
        return obj_setbackgImage(hImg, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
    }
    return obj_setbackgImage(0, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
}
void HHBUI::UIControl::SetBackgInfo(INT x, INT y, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    auto lpBI = m_data.lpBackgroundImage;
    if (lpBI)
    {
        lpBI->dwFlags = dwFlags;
        lpBI->x = x;
        lpBI->y = y;
        lpBI->dwRepeat = 0;
        lpBI->dwAlpha = dwAlpha;
        lpBI->lpGrid = lpGrid;
    }
}
BOOL HHBUI::UIControl::SetBackgPlay(BOOL fPlayFrames, BOOL fResetFrame)
{
    auto lpBI = m_data.lpBackgroundImage;
    if (lpBI)
    {
        auto pImg = lpBI->hImage;
        if (pImg->GetFrameCount() > 1)
        {
            if (fPlayFrames && lpBI->nDelay != 0)
            {
                lpBI->dwFlags = lpBI->dwFlags | bif_playimage;
                SetTimer((size_t)this + TIMER_BKG, lpBI->nDelay);
            }
            else
            {
                lpBI->dwFlags = lpBI->dwFlags - (lpBI->dwFlags & bif_playimage);
                KillTimer((size_t)this + TIMER_BKG);
            }
            if (fResetFrame)
                pImg->SetCurFrame(0);

            return TRUE;
        }
    }
    return FALSE;
}


void HHBUI::UIControl::SetBlur(FLOAT fDeviation, BOOL fmodesoft, BOOL bRedraw)
{
    m_data.fBlur = fDeviation;
    m_data.fmodesoft = fmodesoft;
    if (bRedraw)
        Redraw();
}
BOOL HHBUI::UIControl::SetTimer(size_t uTimerID, UINT nElapse)
{
    if (NULL == m_data.pWnd) return false;

    return m_data.pWnd->SetTimer(this, uTimerID, nElapse);
}

BOOL HHBUI::UIControl::KillTimer(size_t uTimerID)
{
    if (NULL == m_data.pWnd) return false;
    return m_data.pWnd->KillTimer(this, uTimerID);
}
BOOL HHBUI::UIControl::KillTimer()
{
    if (NULL == m_data.pWnd) return false;
    return m_data.pWnd->KillTimer(this);
}
void HHBUI::UIControl::SetAlpha(INT fAlpha)
{
    m_data.alpha = fAlpha;
}
INT HHBUI::UIControl::GetAlpha()
{
    return m_data.alpha;
}
void HHBUI::UIControl::SetID(INT nID)
{
    if (nID > 0)
    {
        m_data.nID = nID;
        auto& hTableObjects = m_data.pWnd->m_hTableObjects;
        hTableObjects.insert_or_assign(std::to_wstring(nID).c_str(), m_UIView);
    }
}
INT HHBUI::UIControl::GetID()
{
    return m_data.nID;
}
LPARAM HHBUI::UIControl::GetlParam()
{
    return m_data.lParam;
}
void HHBUI::UIControl::SetlParam(LPARAM dwlParam)
{
    m_data.lParam = dwlParam;
}
INT HHBUI::UIControl::Style(INT dwStyle)
{
    if (dwStyle == 0)
        return m_data.dwStyle;
    if (m_data.dwStyle & dwStyle)
        m_data.dwStyle &= ~dwStyle;
    else
        m_data.dwStyle |= dwStyle;
    return m_data.dwStyle;
}
INT HHBUI::UIControl::StyleEx(INT dwStyleEx)
{
    if (dwStyleEx == 0)
        return m_data.dwStyleEx;
    if (m_data.dwStyleEx & dwStyleEx)
        m_data.dwStyleEx &= ~dwStyleEx;
    else
        m_data.dwStyleEx |= dwStyleEx;
    return m_data.dwStyleEx;
}
BOOL HHBUI::UIControl::GetScrollInfo(BOOL bHScroll, INT& lpnMin, INT& lpnMax, INT& lpnPos, INT& lpnTrackPos)
{
    auto objParent = (bHScroll ? (UIScroll*)m_data.objHScroll : (UIScroll*)m_data.objVScroll);
    if (objParent)
    {
        lpnMin = objParent->p_data.nMin;
        lpnMax = objParent->p_data.nMax;
        lpnPos = objParent->p_data.nPos;
        lpnTrackPos = objParent->p_data.nTrackPos;
        return TRUE;
    }
    return FALSE;
}
INT HHBUI::UIControl::SetScrollInfo(BOOL bHScroll, INT Mask, INT nMin, INT nMax, INT nPage, INT nPos, BOOL bRedraw)
{
    auto objParent = (bHScroll ? (UIScroll*)m_data.objHScroll : (UIScroll*)m_data.objVScroll);
    if (objParent)
    {
        return objParent->sb_realsetinfo(m_data.pWnd->GethWnd(), Mask, nMin, nMax, nPage, nPos, bRedraw);
    }
    return 0;
}
INT HHBUI::UIControl::GetScrollPos(BOOL bHScroll)
{
    auto objParent = (bHScroll ? (UIScroll*)m_data.objHScroll : (UIScroll*)m_data.objVScroll);
    if (objParent)
        return objParent->p_data.nPos;
    return 0;
}
INT HHBUI::UIControl::SetScrollPos(BOOL bHScroll, INT nPos, BOOL bRedraw)
{
    auto objParent = (bHScroll ? (UIScroll*)m_data.objHScroll : (UIScroll*)m_data.objVScroll);
    if (objParent)
    {
        return objParent->sb_realsetinfo(m_data.pWnd->GethWnd(), SIF_POS, 0, 0, 0, nPos, bRedraw);
    }
    return 0;
}
INT HHBUI::UIControl::SetScrollRange(BOOL bHScroll, INT nMin, INT nMax, BOOL bRedraw)
{
    INT ret = 0;
    auto objParent = (bHScroll ? (UIScroll*)m_data.objHScroll : (UIScroll*)m_data.objVScroll);
    if (objParent)
    {
        INT nPage = 0;
        INT nLine = 0;
        if (bHScroll)
        {
            nPage = m_data.Frame.right - m_data.Frame.left;
            nLine = nPage;
        }
        else
        {
            nPage = m_data.Frame.bottom - m_data.Frame.top;
            nLine = nPage;
        }
        ret = objParent->sb_realsetinfo(m_data.pWnd->GethWnd(), SIF_RANGE | SIF_PAGE, nMin, nMax, nPage, 0, bRedraw);
    }
    return ret;
}
BOOL HHBUI::UIControl::GetScrollRange(BOOL bHScroll, INT& lpnMinPos, INT& lpnMaxPos)
{
    auto objParent = (bHScroll ? (UIScroll*)m_data.objHScroll : (UIScroll*)m_data.objVScroll);
    if (objParent)
    {
        lpnMinPos = objParent->p_data.nMin;
        lpnMaxPos = objParent->p_data.nMax;
        return TRUE;
    }
    return FALSE;
}
INT HHBUI::UIControl::GetScrollTrackPos(BOOL bHScroll)
{
    auto objParent = (bHScroll ? (UIScroll*)m_data.objHScroll : (UIScroll*)m_data.objVScroll);
    if (objParent)
        return objParent->p_data.nTrackPos;
    return 0;
}
void HHBUI::UIControl::SetScrollShow(BOOL bHScroll, BOOL fShow)
{
    auto objParent = (bHScroll ? (UIScroll*)m_data.objHScroll : (UIScroll*)m_data.objVScroll);
    if (objParent)
    {
        objParent->Show(fShow);
        obj_scrollrepostion(FALSE);
    }
}
void HHBUI::UIControl::SetScrollEnable(INT wSB, INT wArrows)
{
    if (wSB == SB_BOTH)
    {
        auto pHSB = (UIScroll*)m_data.objHScroll;
        if(pHSB)
            pHSB->sb_set_wArrows(wArrows, TRUE);
        auto pVSB = (UIScroll*)m_data.objVScroll;
        if(pVSB)
            pVSB->sb_set_wArrows(wArrows, TRUE);
    }
    else if (wSB == SB_HORZ)
    {
        auto pHSB = (UIScroll*)m_data.objHScroll;
        if (pHSB)
            pHSB->sb_set_wArrows(wArrows, TRUE);
    }
    else if (wSB == SB_VERT)
    {
        auto pVSB = (UIScroll*)m_data.objVScroll;
        if (pVSB)
            pVSB->sb_set_wArrows(wArrows, TRUE);
    }
}
void HHBUI::UIControl::SetScrollColor(UIColor color_normal, UIColor color_hover, UIColor color_down, UIColor color_btn_up, UIColor color_btn_down, BOOL bRedraw)
{
    auto pHSB = (UIScroll*)m_data.objHScroll;
    auto pVSB = (UIScroll*)m_data.objVScroll;
    if (pHSB)
    {
        if (!color_normal.empty())
            pHSB->p_data.color_normal = color_normal;
        if (!color_hover.empty())
            pHSB->p_data.color_hover = color_hover;
        if (!color_down.empty())
            pHSB->p_data.color_down = color_down;

        if (!color_btn_up.empty())
            pHSB->p_data.color_btn_up = color_btn_up;
        if (!color_btn_down.empty())
            pHSB->p_data.color_btn_down = color_btn_down;
        if (bRedraw)
            pHSB->Redraw();
    }
    if (pVSB)
    {
        if (!color_normal.empty())
            pVSB->p_data.color_normal = color_normal;
        if (!color_hover.empty())
            pVSB->p_data.color_hover = color_hover;
        if (!color_down.empty())
            pVSB->p_data.color_down = color_down;

        if (!color_btn_up.empty())
            pVSB->p_data.color_btn_up = color_btn_up;
        if (!color_btn_down.empty())
            pVSB->p_data.color_btn_down = color_btn_down;
        if (bRedraw)
            pVSB->Redraw();
    }
}

void HHBUI::UIControl::SetScrollRadius(BOOL bRadius)
{
    auto pHSB = (UIScroll*)m_data.objHScroll;
    auto pVSB = (UIScroll*)m_data.objVScroll;
    if (pHSB)
        pHSB->p_data.isRadius = bRadius;
    if (pVSB)
        pVSB->p_data.isRadius = bRadius;
}

INT HHBUI::UIControl::PostScrollMsg(INT uMsg, WPARAM wParam, LPARAM lParam, INT nLine)
{
    auto pHSB = (UIScroll*)m_data.objHScroll;
    auto pVSB = (UIScroll*)m_data.objVScroll;
    INT nPage = 0;
    if (pHSB)
    {
        nPage = m_data.Frame.right - m_data.Frame.left;
        return pHSB->sb_OnScrollbar(uMsg, wParam, lParam, nPage, nLine);
    }
    if (pVSB)
    {
        nPage = m_data.Frame.bottom - m_data.Frame.top;
        return pVSB->sb_OnScrollbar(uMsg, wParam, lParam, nPage, nLine);
    }
    return 0;
}

void HHBUI::UIControl::SetCursor(HCURSOR fCursor)
{
    if (m_data.hCursor)
        DestroyCursor(m_data.hCursor);
    m_data.hCursor = fCursor;
}

void HHBUI::UIControl::SetCursor(LPCTSTR lpCursorName)
{
    SetCursor(LoadCursorW(nullptr, lpCursorName));
    //SetCursor((HCURSOR)LoadImageW(NULL, lpCursorName, IMAGE_CURSOR, 0, 0, LR_DEFAULTSIZE));
}

HRESULT HHBUI::UIControl::ToImage(UIImage** dstImg, BOOL cdraw, FLOAT fBlur)
{
    return m_data.canvas->ToImage(dstImg, cdraw, fBlur);
}

HRESULT HHBUI::UIControl::CheckDropFormat(LPVOID pDataObject, DWORD dwFormat)
{
    if (dwFormat == 0)
        dwFormat = CF_UNICODETEXT;
    FORMATETC cFmtIn;
    cFmtIn.cfFormat = dwFormat;
    cFmtIn.ptd = 0;
    cFmtIn.dwAspect = DVASPECT_CONTENT;
    cFmtIn.lindex = -1;
    cFmtIn.tymed = TYMED_HGLOBAL;
    FORMATETC cFmtOUT{ 0 };
    cFmtIn.cfFormat = dwFormat;
    cFmtIn.ptd = 0;
    cFmtIn.dwAspect = DVASPECT_CONTENT;
    cFmtIn.lindex = -1;
    cFmtIn.tymed = TYMED_HGLOBAL;
    handle_if_failed(((IDataObject*)pDataObject)->GetCanonicalFormatEtc(&cFmtIn, &cFmtOUT), L"查询拖放信息错误");
    return S_OK;
}
INT HHBUI::UIControl::CheckDropString(LPVOID pDataObject, LPWSTR lpwzBuffer, INT cchMaxLength)
{
    FORMATETC cFmtUnicode{};
    cFmtUnicode.cfFormat = CF_UNICODETEXT;
    cFmtUnicode.dwAspect = DVASPECT_CONTENT;
    cFmtUnicode.lindex = -1;
    cFmtUnicode.tymed = TYMED_HGLOBAL;

    FORMATETC cFmtAnsi{};
    cFmtAnsi.cfFormat = CF_TEXT;
    cFmtAnsi.dwAspect = DVASPECT_CONTENT;
    cFmtAnsi.lindex = -1;
    cFmtAnsi.tymed = TYMED_HGLOBAL;

    STGMEDIUM stgMedium = { 0 };
    LPWSTR lpData = nullptr;
    BOOL isUnicode = FALSE;
    INT ret = 0;

    // Check for Unicode format
    HRESULT hr = ((IDataObject*)pDataObject)->QueryGetData(&cFmtUnicode);
    if (SUCCEEDED(hr))
    {
        hr = ((IDataObject*)pDataObject)->GetData(&cFmtUnicode, &stgMedium);
        if (SUCCEEDED(hr))
        {
            lpData = (LPWSTR)GlobalLock(stgMedium.hGlobal);
            if (lpData)
            {
                isUnicode = TRUE;
                ret = lstrlenW(lpData);
                if (lpwzBuffer && cchMaxLength > 0)
                {
                    // Ensure safe string copy
                    if (FAILED(StringCchCopyW(lpwzBuffer, cchMaxLength, lpData)))
                    {
                        // Handle copy failure, possibly truncate or handle error
                        lpwzBuffer[0] = L'\0'; // Ensure null-termination in case of failure
                    }
                }
                GlobalUnlock(stgMedium.hGlobal);
            }
            ReleaseStgMedium(&stgMedium);
        }
    }

    // If not Unicode, check for ANSI format
    if (!isUnicode)
    {
        hr = ((IDataObject*)pDataObject)->QueryGetData(&cFmtAnsi);
        if (SUCCEEDED(hr))
        {
            hr = ((IDataObject*)pDataObject)->GetData(&cFmtAnsi, &stgMedium);
            if (SUCCEEDED(hr))
            {
                LPSTR lpAnsiData = (LPSTR)GlobalLock(stgMedium.hGlobal);
                if (lpAnsiData)
                {
                    std::wstring unicodeString = vstring::a2w(lpAnsiData);
                    ret = (INT)unicodeString.length();
                    if (lpwzBuffer && cchMaxLength > 0)
                    {
                        // Ensure safe string copy
                        if (FAILED(StringCchCopyW(lpwzBuffer, cchMaxLength, unicodeString.c_str())))
                        {
                            // Handle copy failure, possibly truncate or handle error
                            lpwzBuffer[0] = L'\0'; // Ensure null-termination in case of failure
                        }
                    }
                    GlobalUnlock(stgMedium.hGlobal);
                }
                ReleaseStgMedium(&stgMedium);
            }
        }
    }

    return ret;
}

UINT HHBUI::UIControl::CheckDropFileNumber(WPARAM wParam)
{
    return DragQueryFileW((HDROP)wParam, 0xFFFFFFFF, NULL, 0);
}

UINT HHBUI::UIControl::CheckDragQueryFile(WPARAM wParam, UINT iFile, LPWSTR lpszFile, UINT cch)
{
    return DragQueryFileW((HDROP)wParam, iFile, lpszFile, cch);
}
BOOL HHBUI::UIControl::Lock(INT Left, INT Top, INT Right, INT Bottom, INT Width, INT Height)
{
    auto pLayout = (UILayout*)m_data.Parent;
    if (pLayout)
    {
        if (pLayout->Layout_GetType() == 0)
            pLayout->Layout_Init(elt_absolute);
        else if (pLayout->Layout_GetType() != elt_absolute)
            return FALSE;


        if (Left != -1)
            pLayout->Layout_Absolute_Setedge(this, elcp_absolute_left, elcp_absolute_type_px, Left);
        if (Top != -1)
            pLayout->Layout_Absolute_Setedge(this, elcp_absolute_top, elcp_absolute_type_px, Top);
        if (Right != -1)
            pLayout->Layout_Absolute_Setedge(this, elcp_absolute_right, elcp_absolute_type_px, Right);
        if (Bottom != -1)
            pLayout->Layout_Absolute_Setedge(this, elcp_absolute_bottom, elcp_absolute_type_px, Bottom);
        if (Width != -1)
            pLayout->Layout_Absolute_Setedge(this, elcp_absolute_width, elcp_absolute_type_px, Width);
        if (Height != -1)
            pLayout->Layout_Absolute_Setedge(this, elcp_absolute_height, elcp_absolute_type_px, Height);

        return pLayout->Layout_Update();
    }

    return FALSE;
}
LPVOID HHBUI::UIControl::FindUIView(LPCWSTR lpName)
{
    auto iter = m_data.pWnd->m_hTableObjects.find(lpName);
    if (iter != m_data.pWnd->m_hTableObjects.end())
        return iter->second;
    return nullptr;
}
INT HHBUI::UIControl::GetChildrenCout() const
{
    return 0;
}
LPVOID HHBUI::UIControl::GetChildren(INT index) const
{
    return LPVOID();
}
void HHBUI::UIControl::SetRotate(FLOAT fRotate, BOOL pStart)
{
    m_data.fRotate = fRotate;
    m_data.IsRotate = pStart;
    if (!pStart && m_data.canvas->IsDrawing())
        m_data.canvas->SetTransform(0);
}
template <typename T>
void itow_s(T value, std::wstring& dest) {
    wchar_t buffer[12];
    _itow_s(value, buffer, 12, 10);
    dest.append(buffer);
}
LRESULT HHBUI::UIControl::OnBaseProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
    m_data.dwFlags |= EOF_BUSERPROCESSESED;
    LRESULT ret = S_OK;
    // 处理子类回调
    if (m_data.pfnSubClass != nullptr)
    {
        ret = m_data.pfnSubClass(hWnd, m_data.pWnd, this, m_data.nID, uMsg, wParam, lParam);
        if (ret == S_FALSE)
            return ret;
    }

    // 消息处理
    switch (uMsg)
    {
    case WM_NCHITTEST:
        ret = obj_wm_nchittest(hWnd, uMsg, wParam, lParam);
        if (ret == HTTRANSPARENT)
            return ret;
        break;

    case WM_SIZE:
        Layout_Update();
        break;
    case WM_SETFOCUS:
    {
        auto objFocus = reinterpret_cast<UIControl*>(wParam);
        if (objFocus)
        {
            objFocus->OnBaseProc(hWnd, WM_KILLFOCUS, reinterpret_cast<size_t>(this), 0);
        }
        m_data.pWnd->m_data.objFocus = this;
        m_data.dwState |= state_focus;

        if (FLAGS_CHECK(m_data.dwFlags, EOF_BIME))
        {
            ImmAssociateContext(hWnd, m_data.pWnd->m_data.hImc);
        }
        break;
    }

    case WM_KILLFOCUS:
    {
        m_data.pWnd->wm_popupclose();
        auto objFocus = reinterpret_cast<UIControl*>(wParam);
        if (m_data.pWnd->m_data.objFocus == this)
        {
            m_data.pWnd->m_data.objFocus = objFocus;
            m_data.dwState &= ~state_focus;
            if (m_data.pWnd->m_data.objTrack == this)
            {
                ReleaseCapture();
            }
            ret = TRUE;
        }
        if (FLAGS_CHECK(m_data.dwFlags, EOF_BIME))
        {
            ImmAssociateContext(hWnd, 0);
        }
        break;
    }

    case WM_LBUTTONDOWN:
    case WM_RBUTTONDOWN:
    case WM_MBUTTONDOWN:
        FLAGS_ADD(m_data.dwFlags, EOF_BDOWN);
        break;

    case WM_LBUTTONUP:
    case WM_RBUTTONUP:
    case WM_MBUTTONUP:
        FLAGS_DEL(m_data.dwFlags, EOF_BDOWN);
        break;

    default:
        break;
    }

    if (m_data.pfnClsProc)
        ret = m_data.pfnClsProc(hWnd, uMsg, wParam, lParam);
    else
        ret = OnMsgProc(hWnd, uMsg, wParam, lParam);
   
    m_data.dwFlags &= ~EOF_BUSERPROCESSESED;
    if (ret == S_FALSE)
        return ret;
    switch (uMsg)
    {
    case WM_NCHITTEST:
        DispatchNotify(WMM_NCHITTEST, wParam, lParam);
        return HTCLIENT;

    case WM_DESTROY:
    {
        auto pWnd = m_data.pWnd;
        BOOL fReDraw = obj_makeupinvalidaterect(m_data.Frame_w);
        BOOL fRelaseCaption = FALSE;

        // Helper function to reset pointers
        // Reset a given pointer if it matches the specified object
        auto resetPointerIfMatching = [](UIControl* objToCheck, UIControl*& target) {
            if (target == objToCheck) {
                target = nullptr;
            }
            };

        // Load the atomic values once into local variables
        auto objTrack = pWnd->m_data.objTrack.load();
        auto objFocus = pWnd->m_data.objFocus.load();
        auto objHittest = pWnd->m_data.objHittest.load();
        auto objHittestPrev = pWnd->m_data.objHittestPrev.load();
        auto objFocusPrev = pWnd->m_data.objFocusPrev.load();
        auto objTrackPrev = pWnd->m_data.objTrackPrev.load();

        // Reset the relevant pointers
        resetPointerIfMatching(this, objTrack);
        if (objTrack == nullptr) {
            fRelaseCaption = TRUE;
        }

        resetPointerIfMatching(this, objFocus);
        resetPointerIfMatching(this, objHittest);
        resetPointerIfMatching(this, objHittestPrev);
        resetPointerIfMatching(this, objFocusPrev);
        resetPointerIfMatching(this, objTrackPrev);

        // Optionally, store the updated values back to the atomic variables if needed
        pWnd->m_data.objTrack.store(objTrack);
        pWnd->m_data.objFocus.store(objFocus);
        pWnd->m_data.objHittest.store(objHittest);
        pWnd->m_data.objHittestPrev.store(objHittestPrev);
        pWnd->m_data.objFocusPrev.store(objFocusPrev);
        pWnd->m_data.objTrackPrev.store(objTrackPrev);

        // Update child pointers
        if (pWnd->m_objChildLast == this) {
            pWnd->m_objChildLast = m_data.objPrev;
        }
        if (pWnd->m_objChildFirst == this) {
            pWnd->m_objChildFirst = m_data.objNext;
        }

        // Clean up resources
        delete m_data.canvas;
        m_data.canvas = nullptr;

        // Remove from object table
        if (m_data.nID != 0) {
            auto idIter = m_data.pWnd->m_hTableObjects.find(std::to_wstring(m_data.nID).c_str());
            if (idIter != m_data.pWnd->m_hTableObjects.end()) {
                m_data.pWnd->m_hTableObjects.erase(idIter);
            }
        }
        auto clsnameIter = m_data.pWnd->m_hTableObjects.find(m_data.lpClsname);
        if (clsnameIter != m_data.pWnd->m_hTableObjects.end()) {
            m_data.pWnd->m_hTableObjects.erase(clsnameIter);
        }

        // Reset background image
        obj_setbackgImage(0, 0, 0, 0, 0, 0, 0);

        // Kill the timer and clear Z-order
        KillTimer();
        UIControl* tmp1 = nullptr;
        UIBase* tmp2 = nullptr;
        obj_zclear(tmp1, tmp2);

        // Clean up child components
        auto sObj = (UIControl*)m_objChildFirst;
        if (sObj) {
            sObj->OnBrothers(WM_DESTROY, 0, 0, FALSE, FALSE);
        }

        // Delete window path resources
        if (m_data.hPath_Window) {
            delete m_data.hPath_Window;
            m_data.hPath_Window = nullptr;
        }
        if (m_data.hPath_Client) {
            delete m_data.hPath_Client;
            m_data.hPath_Client = nullptr;
        }

        // Clean up drop target
        if (m_data.lpIDropTarget) {
            delete (DropTarget*)m_data.lpIDropTarget;
        }

        // Clean up brushes and title
        if (m_data.hPathBrush) {
            delete m_data.hPathBrush;
        }
        // Reset references to external objects
        m_data.lpIDropTarget = nullptr;
        m_objChildFirst = nullptr;
        m_UIView = nullptr;
        m_UIWindow = nullptr;
        m_data.Parent = nullptr;

        // Delete background brush
        delete m_data.lpBackgBrush;
        m_data.lpBackgBrush = nullptr;

        // Delete font
        if (m_data.hFont) {
            delete m_data.hFont;
        }
        m_data.hFont = nullptr;

        // Release capture if necessary
        if (fRelaseCaption) {
            ReleaseCapture();
        }

        // Invalidate window if necessary
        if (fReDraw && !((pWnd->m_data.dwFlags & EWF_BDESTROYWINDOW) == EWF_BDESTROYWINDOW)) {
            RECT scr = m_data.Frame_w.ToRect();
            InvalidateRect(pWnd->m_data.hWnd, &scr, FALSE);
        }

        // Clear subclass pointer
        m_data.pfnSubClass = nullptr;
        m_data.pWnd = nullptr;
    }
        break;
    case WM_SETCURSOR:
        if (m_data.pWnd && m_data.hCursor && m_data.pWnd->m_data.dwHitCode == HTCLIENT) {
            ::SetCursor(m_data.hCursor);
            return S_FALSE;
        }
        break;

    case WM_TIMER:
    {
        if (wParam == (size_t)this + TIMER_BKG)
        {
            if (m_data.lpBackgroundImage != 0)
            {
                INT iCur = m_data.lpBackgroundImage->curFrame + 1;
                INT iMax = m_data.lpBackgroundImage->maxFrame - 1;
                if (iCur > iMax)
                {
                    iCur = 0;
                }
                auto img = m_data.lpBackgroundImage->hImage;
                img->SetCurFrame(iCur);
                m_data.lpBackgroundImage->curFrame = iCur;

                Redraw();
                break;
            }
        }
    }
        return DispatchNotify(WMM_TIMER, wParam, lParam);

    case WM_ENABLE:
        obj_update(FALSE);
        return DispatchNotify(WMM_ENABLE, wParam, lParam);

    case WM_SHOWWINDOW:
        obj_update(FALSE);
        return DispatchNotify(WMM_SHOW, wParam, lParam);

    case WM_MOUSEHOVER:
        if (UIEngine::QueryDebug())
        {
            std::wstring wzPostion;

            std::wstring wstrLeft, wstrTop, wstrWidth, wstrHeight, wstrRight, wstrBottom;
            itow_s(m_data.Frame_w.left, wstrLeft);
            itow_s(m_data.Frame_w.top, wstrTop);
            itow_s(m_data.Frame_w.right - m_data.Frame_w.left, wstrWidth);
            itow_s(m_data.Frame_w.bottom - m_data.Frame_w.top, wstrHeight);
            itow_s(m_data.Frame_w.right, wstrRight);
            itow_s(m_data.Frame_w.bottom, wstrBottom);

            wzPostion.append(L"ClsName：");
            wzPostion.append(m_data.lpClsname);
            wzPostion.append(L"\nRectF：\n");
            wzPostion.append(wstrLeft);
            wzPostion.append(L",");
            wzPostion.append(wstrTop);
            wzPostion.append(L",");
            wzPostion.append(wstrWidth);
            wzPostion.append(L",");
            wzPostion.append(wstrHeight);
            wzPostion.append(L"\nRect：\n");
            wzPostion.append(wstrLeft);
            wzPostion.append(L",");
            wzPostion.append(wstrTop);
            wzPostion.append(L",");
            wzPostion.append(wstrRight);
            wzPostion.append(L",");
            wzPostion.append(wstrBottom);

            TooltipsPop(wzPostion.c_str(), m_data.Color.crBlack_pstrTips, m_data.Color.crText_pstrTips, -1, -1, -1, FALSE);
        }
        else
        {
            TooltipsPop(m_data.pstrTips, m_data.Color.crBlack_pstrTips, m_data.Color.crText_pstrTips, -1, -1, -1, FALSE);
            return DispatchNotify(WMM_HOVER, wParam, lParam);
        }
        break;

    case WM_MOUSELEAVE:
        TooltipsPop(NULL, {}, {}, -1, -1, -1, FALSE);
        return DispatchNotify(WMM_LEAVE, wParam, lParam);

    case WM_SETFOCUS:
        return DispatchNotify(WMM_SETFOCUS, wParam, lParam);

    case WM_KILLFOCUS:
        return DispatchNotify(WMM_KILLFOCUS, wParam, lParam);

    case WM_LBUTTONDOWN:
        return DispatchNotify(WMM_LDOWN, wParam, lParam);

    case WM_LBUTTONUP:
        return DispatchNotify(WMM_LUP, wParam, lParam);

    case WM_RBUTTONDOWN:
        return DispatchNotify(WMM_RDOWN, wParam, lParam);

    case WM_RBUTTONUP:
        return DispatchNotify(WMM_RUP, wParam, lParam);

    case WM_EX_LCLICK:
        return DispatchNotify(WMM_CLICK, wParam, lParam);

    case WM_EX_RCLICK:
        return DispatchNotify(WMM_RCLICK, wParam, lParam);

    case WM_LBUTTONDBLCLK:
        return DispatchNotify(WMM_DBLCLK, wParam, lParam);

    case WM_RBUTTONDBLCLK:
        return DispatchNotify(WMM_RDBLCLK, wParam, lParam);

    case WM_KEYDOWN:
        return DispatchNotify(WMM_KEYDOWN, wParam, lParam);

    case WM_CHAR:
        return DispatchNotify(WMM_CHAR, wParam, lParam);

    case WM_SIZE:
        return DispatchNotify(WMM_SIZE, wParam, lParam);

    case WM_MOVE:
        return DispatchNotify(WMM_MOVE, wParam, lParam);

    case WM_CREATE:
        return DispatchNotify(WMM_CREATE, wParam, lParam);

    case WM_MOUSEWHEEL:
    {
        auto pHSB = (UIScroll*)m_data.objHScroll;
        auto pVSB = (UIScroll*)m_data.objVScroll;
        SHORT zDelta = (SHORT)GET_Y_LPARAM(wParam);

        if (lstrcmpW(m_data.lpClsname, L"form-scroll") == 0)
        {
            if (pHSB) pHSB->sb_parentnotify((zDelta > 0 ? SB_LINEUP : SB_LINEDOWN), 0, 0);
            if (pVSB) pVSB->sb_parentnotify((zDelta > 0 ? SB_LINEUP : SB_LINEDOWN), 0, 0);
            return S_FALSE;
        }
        else if ((m_data.dwStyle & eos_scroll_v) == eos_scroll_v)
        {
            OnBaseProc(hWnd, WM_VSCROLL, (zDelta > 0 ? SB_LINEUP : SB_LINEDOWN), 0);
            return S_FALSE;
        }
        else if ((m_data.dwStyle & eos_scroll_h) == eos_scroll_h)
        {
            OnBaseProc(hWnd, WM_HSCROLL, (zDelta > 0 ? SB_LINEUP : SB_LINEDOWN), 0);
            return S_FALSE;
        }
    }
    break;

    case WM_EX_EASING:
        return DispatchNotify(WMM_EASING, wParam, lParam);

    default:
        break;
    }

    return ret;
}

void HHBUI::UIControl::OnBrothers(INT uMsg, WPARAM wParam, LPARAM lParam, BOOL bBypassSelf, BOOL bSameClass)
{
    UIBase* pObjEntry = m_data.Parent;
    if (pObjEntry->m_UIView == nullptr)
    {
        pObjEntry = m_data.pWnd;
    }
    auto objEntry = (UIControl*)pObjEntry->m_objChildFirst;
    while (objEntry)
    {
        if (bBypassSelf)
        {
            if (objEntry == this)
            {
                objEntry = objEntry->m_data.objNext;
                continue;
            }
        }
        if (bSameClass)
        {
            LPCWSTR atomName = objEntry->m_data.lpClsname;
            LPCWSTR atomName2 = m_data.lpClsname;
            if (lstrcmpW(atomName, atomName2) != 0)
            {
                objEntry = objEntry->m_data.objNext;
                continue;
            }
        }
        auto pNext = objEntry->m_data.objNext;
        objEntry->OnBaseProc(objEntry->GethWnd(), uMsg, wParam, lParam);
        objEntry = pNext;
    }
}

BOOL HHBUI::UIControl::obj_setbackgImage(UIImage *hImg, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    if (m_data.lpBackgroundImage)
    {
        m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & bif_playimage);
        KillTimer((size_t)this + TIMER_BKG);
        auto hImage = m_data.lpBackgroundImage->hImage;
        delete hImage;
        delete m_data.lpBackgroundImage;
        m_data.lpBackgroundImage = nullptr;
    }
    if (hImg)
    {
        UIImage* hImage = nullptr;
        INT width = 0, height = 0;
        if (dwFlags != bif_disablescale && dwFlags != bif_playimage)
        {
            hImg->Scale(m_data.Frame.right - m_data.Frame.left, m_data.Frame.bottom - m_data.Frame.top, &hImage);
            delete hImg;
        }
        else
        {
            hImage = hImg;
        }

        if (hImage != 0)
        {
            m_data.lpBackgroundImage = new info_backgroundimage();
            if (m_data.lpBackgroundImage)
            {
                m_data.lpBackgroundImage->dwFlags = dwFlags;
                m_data.lpBackgroundImage->hImage = hImage;
                m_data.lpBackgroundImage->x = x;
                m_data.lpBackgroundImage->y = y;
                m_data.lpBackgroundImage->dwRepeat = dwRepeat;
                m_data.lpBackgroundImage->dwAlpha = dwAlpha;
                m_data.lpBackgroundImage->lpGrid = lpGrid;

                if (hImage->GetFrameCount() > 1)
                {
                    UINT nDelay = 0;
                    if (hImage->GetFrameDelay(nDelay, 1))
                    {
                        m_data.lpBackgroundImage->nDelay = nDelay;
                        m_data.lpBackgroundImage->maxFrame = hImage->GetFrameCount();

                        m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & bif_playimage);
                        KillTimer((size_t)this + TIMER_BKG);
                        if ((dwFlags & bif_playimage) != 0 && nDelay != 0)
                        {
                            m_data.dwFlags |= bif_playimage;
                            SetTimer((size_t)this + TIMER_BKG, nDelay);
                        }
                    }
                }
                else
                {
                    Redraw();
                }
                return TRUE;
            }
        }
    }
    return FALSE;
}
void CALLBACK _obj_tooltips_pop_func(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime)
{
    KillTimer(hWnd, idEvent);
    HHBUI::UIWnd* pWnd = (HHBUI::UIWnd*)(idEvent - TIMER_TOOLTIPS_POP);
    pWnd->Flags(pWnd->Flags() - (pWnd->Flags() & EWF_BTOOLTIPSPOPUP));
    HHBUI::ti_s* offset = nullptr;

    if (((pWnd->Flags() & EWF_BTOOLTIPSTRACKPOSTION) == EWF_BTOOLTIPSTRACKPOSTION))
    {
        offset = pWnd->GetToolrack(TRUE);
    }
    else
    {
        offset = pWnd->GetToolrack(FALSE);
    }
    SendMessageW(hWnd, TTM_TRACKACTIVATE, 0, (size_t)offset); // needs hwnd, uId, cbSize
}
void CALLBACK _obj_tooltips_popup_func(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime)
{
    if (uMsg == 275)
        KillTimer(hWnd, idEvent);
    HHBUI::UIWnd* pWnd = (HHBUI::UIWnd*)(idEvent - TIMER_TOOLTIPS_POPUP);
    HHBUI::ti_s* offset = nullptr;

    if (!((pWnd->Flags() & EWF_BTOOLTIPSPOPUP) == EWF_BTOOLTIPSPOPUP))
    {
        pWnd->Flags(pWnd->Flags() | EWF_BTOOLTIPSPOPUP);

        if (((pWnd->Flags() & EWF_BTOOLTIPSTRACKPOSTION) == EWF_BTOOLTIPSTRACKPOSTION))
        {
            offset = pWnd->GetToolrack(TRUE);
        }
        else
        {
            offset = pWnd->GetToolrack(FALSE);
        }
        SendMessageW(hWnd, TTM_TRACKACTIVATE, 1, (size_t)offset); //TTM_TRACKACTIVATE
        INT offset_ = SendMessageW(hWnd, 1045, 2, 0);
        if (offset_ != 0)
        {
            SetTimer(hWnd, (size_t)pWnd + TIMER_TOOLTIPS_POP, offset_, _obj_tooltips_pop_func);
        }
    }
}
void HHBUI::UIControl::obj_tippopup(LPCWSTR lpText, INT x, INT y, INT dwTime, BOOL fShow)
{
    HWND hWnd = m_data.pWnd->m_data.hWndTips;
    if (lpText == nullptr)
    {
        ::KillTimer(hWnd, (size_t)m_data.pWnd + TIMER_TOOLTIPS_POPUP);
        _obj_tooltips_pop_func(hWnd, WM_TIMER, (size_t)m_data.pWnd + TIMER_TOOLTIPS_POP, 0);
    }
    else
    {
        auto lpTI = m_data.pWnd->m_data.toolauto;
        INT postion = MAKELONG(x, y);
        if (postion == -1)
        {
            m_data.pWnd->m_data.dwFlags -= (m_data.pWnd->m_data.dwFlags & EWF_BTOOLTIPSTRACKPOSTION);
            lpTI = m_data.pWnd->m_data.toolauto;
        }
        else
        {
            SendMessageW(hWnd, 1042, 0, postion); //TTM_TRACKPOSITION
            lpTI = m_data.pWnd->m_data.toolrack;
            m_data.pWnd->m_data.dwFlags |=  EWF_BTOOLTIPSTRACKPOSTION;
        }
        SetWindowTheme(hWnd, L"", L"");  //必填代码，缺少会引发设置颜色无效的问题
        lpTI->hWnd_ = m_data.pWnd->GethWnd();
        lpTI->lpszText_ = lpText;
        UIColor rgbBlack = UIColor(255, 255, 255, 255);
        UIColor rgbcrText = UIColor(0, 0, 0, 255);
        if (!m_data.Color.crBlack_pstrTips.empty())
            rgbBlack = m_data.Color.crBlack_pstrTips;
        if (!m_data.Color.crText_pstrTips.empty())
            rgbcrText = m_data.Color.crText_pstrTips;

        tagRECT rect{ 5,5,5,5 };
        SendMessageW(hWnd, TTM_SETMARGIN, 0, (LPARAM)(LPRECT)&rect);
        SendMessageW(hWnd, TTM_SETTIPBKCOLOR, (WPARAM)RGB(rgbBlack.GetR() * 255, rgbBlack.GetG() * 255, rgbBlack.GetB() * 255), 0L);
        SendMessageW(hWnd, TTM_SETTIPTEXTCOLOR, (WPARAM)RGB(rgbcrText.GetR() * 255, rgbcrText.GetG() * 255, rgbcrText.GetB() * 255), 0L);
        SendMessageW(hWnd, TTM_SETDELAYTIME, TTDT_AUTOPOP, dwTime);
        SendMessageW(hWnd, TTM_UPDATETIPTEXTW, 0, (LPARAM)lpTI);
        if (fShow)
        {
            _obj_tooltips_popup_func(hWnd, 0, (size_t)m_data.pWnd + TIMER_TOOLTIPS_POPUP, 0);
        }
        else
        {
            ::SetTimer(hWnd, (size_t)m_data.pWnd + TIMER_TOOLTIPS_POPUP, 500, _obj_tooltips_popup_func);
        }
    }
}

void HHBUI::UIControl::obj_scrollrepostion(BOOL fDispatch)
{
    RECT rcClient = m_data.Frame_c.ToRect();
    auto pHSB = (UIScroll*)m_data.objHScroll;
    auto pVSB = (UIScroll*)m_data.objVScroll;

    // 调整竖直滚动条的影响
    if (m_data.dwStyle & eos_scroll_v)
    {
        if (pVSB && (pVSB->m_data.dwStyle & eos_hidden) != eos_hidden)
        {
            if (pVSB->m_data.dwStyle & ess_rightbottomalign)
            {
                rcClient.right -= pVSB->p_data.xyz.right;
            }
            else
            {
                rcClient.left += pVSB->p_data.xyz.left;
            }
        }
    }

    // 调整水平滚动条的影响
    if (m_data.dwStyle & eos_scroll_h)
    {
        if (pHSB && (pHSB->m_data.dwStyle & eos_hidden) != eos_hidden)
        {
            if (pHSB->m_data.dwStyle & ess_rightbottomalign)
            {
                rcClient.bottom -= pHSB->p_data.xyz.right;
            }
            else
            {
                rcClient.top += pHSB->p_data.xyz.left;
            }
        }
    }

    if (pVSB)
    {
        obj_scrollup(TRUE, rcClient.left, rcClient.top, rcClient.right, rcClient.bottom - m_data.radius.bottom, fDispatch);
    }

    if (pHSB)
    {
        obj_scrollup(FALSE, rcClient.left + UIEngine::fScale(m_data.radius.left), rcClient.top, rcClient.right - UIEngine::fScale(m_data.radius.left), rcClient.bottom, fDispatch);
    }
}


void HHBUI::UIControl::obj_scrollup(BOOL bVScroll, INT cLeft, INT cTop, INT cRight, INT cBottom, BOOL fDispatch)
{
    auto pSB = (bVScroll ? (UIScroll*)m_data.objVScroll : (UIScroll*)m_data.objHScroll);
    auto xyz2 = pSB->p_data.xyz.right;
    INT l, t, r, b;
   
    if (((pSB->m_data.dwStyle & ess_rightbottomalign) == ess_rightbottomalign))
    {
        if (bVScroll)
        {
            l = cRight;
            t = cTop;
            r = l + xyz2;
            b = cBottom;
        }
        else
        {
            l = cLeft;
            t = cBottom;
            r = cRight;
            b = t + xyz2;
        }
    }
    else
    {
        if (bVScroll)
        {
            l = cLeft - xyz2;
            t = cTop;
            r = cLeft;
            b = cBottom;
        }
        else
        {
            l = cLeft;
            t = cTop - xyz2;
            r = cRight;
            b = cTop;
        }
    }
    INT flag = SWP_NOZORDER | SWP_NOCOPYBITS | SWP_NOACTIVATE | SWP_EX_NODPISCALE;
    if (!fDispatch)
    {
        flag = flag | SWP_ASYNCWINDOWPOS;
    }
    pSB->SetPos(l, t, r - l, b - t, 0, flag);
}


INT HHBUI::UIControl::obj_wm_nchittest(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	INT ret = HTTRANSPARENT;
	if ((m_data.dwStyleEx & eos_ex_transparent) != eos_ex_transparent)
	{
		BOOL fHit = FALSE;

		if (((m_data.dwFlags & EOF_BPATH) == EOF_BPATH) && m_data.hPath_Client)
		{
			fHit = (m_data.hPath_Client->HitTest(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam)) == S_OK);
		}
		else
		{
            //POINT aa = { GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam) };
            //fHit = PtInRect(&m_data.Frame_c, aa);
            fHit = m_data.Frame_c.PtInRect(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
		}
        if (fHit)
        {
            if (m_data.pfnClsProc)
                return m_data.pfnClsProc(hWnd, uMsg, wParam, lParam);
            else
                return OnMsgProc(hWnd, uMsg, wParam, lParam);
        }
	}
	return ret;
}
void HHBUI::UIControl::obj_zset(HWND hObjInsertAfter)
{
    // 清除现有的父子关系
    UIControl* hParent = nullptr;
    UIBase* pParent = nullptr;
    obj_zclear(hParent, pParent);

    // 如果有父对象，更新父对象指向当前对象
    if (hParent)
        m_data.Parent = pParent;

    // 父控件为空，直接返回
    if (!pParent)
        return;

    // 获取父控件的子控件链表
    UIControl* objChildFirst = (UIControl*)pParent->m_objChildFirst;
    UIControl* objChildLast = (UIControl*)pParent->m_objChildLast;

    // 如果父控件没有子控件，直接将当前控件设置为第一个和最后一个子控件
    if (!objChildFirst || !objChildLast)
    {
        pParent->m_objChildFirst = this;
        pParent->m_objChildLast = this;
        return;
    }

    // 获取当前控件是否置顶
    BOOL bTopmost = (m_data.dwStyleEx & eos_ex_topmost) == eos_ex_topmost;

    // 判断hObjInsertAfter的值，并执行相应的操作
    if (hObjInsertAfter == HWND_NOTOPMOST) // 取消置顶
    {
        m_data.dwStyleEx &= ~eos_ex_topmost;  // 清除置顶样式
        obj_setzbeforet(objChildLast, pParent); // 将控件移动到倒数第二位置
    }
    else if (hObjInsertAfter == HWND_TOPMOST) // 置顶
    {
        m_data.dwStyleEx |= eos_ex_topmost; // 设置为置顶
        // 将当前控件插入为最后一个子控件
        objChildLast->m_data.objNext = this;
        m_data.objPrev = objChildLast;
        pParent->m_objChildLast = this;
    }
    else if (hObjInsertAfter == HWND_TOP) // 顶层
    {
        if (bTopmost)
        {
            // 如果已经是置顶状态，移动到最后
            objChildLast->m_data.objNext = this;
            m_data.objPrev = objChildLast;
            pParent->m_objChildLast = this;
        }
        else
        {
            // 否则，插入到子控件链的倒数第二个位置
            obj_setzbeforet(objChildLast, pParent);
        }
    }
    else if (hObjInsertAfter == HWND_BOTTOM) // 底层
    {
        if (bTopmost)
        {
            // 如果已经是置顶状态，插入到倒数第二个位置
            obj_setzbeforet(objChildLast, pParent);
        }
        else
        {
            // 将当前控件设置为第一个子控件
            objChildFirst->m_data.objPrev = this;
            m_data.objNext = objChildFirst;
            pParent->m_objChildFirst = this;
        }
    }
}



BOOL HHBUI::UIControl::obj_autosize(INT* width, INT* height)
{
    BOOL ret = FALSE;
    if ((m_data.dwStyleEx & eos_ex_autosize) == eos_ex_autosize && (m_data.dwFlags & EOF_BAUTOSIZED) != EOF_BAUTOSIZED)
    {
        FLAGS_ADD(m_data.dwFlags, EOF_BAUTOSIZED);
        auto parentObj = (UIControl*)m_data.Parent->m_UIView;
        INT iWidth = 0;
        INT iHeight = 0;
        if (parentObj == nullptr)
        {
            auto tsize = ((UIWnd*)m_data.Parent->m_UIWindow)->m_data.size;
            iWidth = tsize.right - tsize.left;
            iHeight = tsize.bottom - tsize.top;
        }
        else
        {
            iWidth = parentObj->m_data.Frame.right - parentObj->m_data.Frame.left;
            iHeight = parentObj->m_data.Frame.bottom - parentObj->m_data.Frame.top;
        }
        iWidth = iWidth - m_data.Frame.left;
        iHeight = iHeight - m_data.Frame.top;
        if (iWidth < 0)
            iWidth = 0;
        if (iHeight < 0)
            iHeight = 0;

        FLOAT w = 0;
        FLOAT h = 0;
        if (m_data.pstrTitle)
        {
            auto hFont = GetFont();
            UICanvas::CalcTextSize(hFont, m_data.pstrTitle, m_data.dwTextFormat, iWidth, iHeight, &w, &h);
            *width = m_data.Frame_t.left + m_data.Frame_t.right + w + hFont->GetSize();
            *height = m_data.Frame_t.top + m_data.Frame_t.bottom + h + hFont->GetSize();
        }
        ret = TRUE;
    }
    return ret;
}

void HHBUI::UIControl::obj_update(BOOL fUpdateWindow)
{
    // 定义临时变量
    //RECT dRect = m_data.Frame;
    //OffsetRect(&dRect, m_data.Frame_w.left - m_data.Frame.left, m_data.Frame_w.top - m_data.Frame.top);
    m_data.Frame.Offset(m_data.Frame_w.left - m_data.Frame.left, m_data.Frame_w.top - m_data.Frame.top);
    FLAGS_ADD(m_data.dwFlags, EOF_BNEEDREDRAW);
    if (obj_makeupinvalidaterect(m_data.Frame))
    {
        RECT scr = m_data.Frame.ToRect();
        InvalidateRect(m_data.pWnd->m_data.hWnd, &scr, FALSE);
        if (fUpdateWindow)
        {
            UpdateWindow(m_data.pWnd->m_data.hWnd);
        }
    }
    
}

BOOL HHBUI::UIControl::obj_makeupinvalidaterect(ExRectF prc)
{
    if (!m_data.pWnd)
        return FALSE;
    if (m_data.pWnd->m_data.dwFlags & EWF_SIZED)
        return FALSE;

    // 更新最终混合的窗口位置
    if (!m_data.Parent)
        return FALSE;
    auto objParent = (UIControl*)m_data.Parent->m_UIView;
    while (objParent)
    {
        if (!prc.IntersectRect(prc, objParent->m_data.Frame_w))
            return FALSE;
    
        objParent = (UIControl*)objParent->m_data.Parent->m_UIView;
    }
    // Z序混合检测-blur
    /*
    if (m_data.pWnd->m_data.dwFlags & EWF_BCOMPOSITEDCHECK)
    {
        RECT ppObj1{};
        obj_zcompositedcheck(prc, (UIControl*)m_data.pWnd->m_objChildLast, this, ppObj1);
    }
    */
    return TRUE;
}
void HHBUI::UIControl::Show(BOOL fShow)
{
    BOOL isCurrentlyHidden = (m_data.dwStyle & eos_hidden) != eos_hidden;
    if (isCurrentlyHidden != fShow)
    {
        // Clear the hover and down states
        m_data.dwState &= ~(state_hover | state_down);

        if (fShow)
        {
            // Show the control: clear the hidden state and remove the hidden style
            m_data.dwState &= ~state_hidden;
            m_data.dwStyle &= ~eos_hidden;
        }
        else
        {
            // Hide the control: set the hidden state and add the hidden style
            KillFocus();
            m_data.dwState |= state_hidden;
            m_data.dwStyle |= eos_hidden;
        }
    }
    Redraw();
}

BOOL HHBUI::UIControl::IsVisible()
{
    BOOL isCurrentlyHidden = (m_data.dwStyle & eos_hidden) != eos_hidden;
    return isCurrentlyHidden;
}
BOOL HHBUI::UIControl::obj_zcompositedcheck(ExRectF prc, UIControl* objLast, UIControl* objStop, ExRectF lpsrcInsert)
{
    auto objPrev = objLast;
    while (objPrev)
    {
        if (objStop == objPrev)
            return TRUE;
        objLast = (UIControl*)objPrev->m_objChildLast;
        if (objLast != 0)
        {
            if (obj_zcompositedcheck(prc, objLast, objStop, lpsrcInsert))
                return TRUE;
        }
       
        if (((objPrev->m_data.dwStyle & eos_hidden) != eos_hidden))
        {

            if (((objPrev->m_data.dwStyleEx & eos_ex_composited) == eos_ex_composited))
            {
                if (lpsrcInsert.IntersectRect(prc, objPrev->m_data.Frame_w))
                {
                    FLAGS_ADD(objPrev->m_data.dwFlags, EOF_BNEEDREDRAW);
                    m_data.Frame_d = m_data.Frame;
                    m_data.Frame_d.Offset(-m_data.Frame.left, -m_data.Frame.top);
                    prc.UnionRect(prc, m_data.Frame_w);
                    //OffsetRect(&m_data.Frame_d, -m_data.Frame.left, -m_data.Frame.top);
                    //UnionRect(prc, prc, (RECT*)&m_data.Frame_w);
                }
            }
        }
        objPrev = objPrev->m_data.objPrev;
    }
    return FALSE;
}
void HHBUI::UIControl::obj_getcontext(ps_context& lpPS)
{
    lpPS.hCanvas = m_data.canvas;
    lpPS.rcPaint = m_data.Frame_d;
    lpPS.uWidth = m_data.Frame.right - m_data.Frame.left;
    lpPS.uHeight = m_data.Frame.bottom - m_data.Frame.top;

    lpPS.rcText.right = lpPS.uWidth;
    lpPS.rcText.bottom = lpPS.uHeight;
    lpPS.rcText.left = m_data.Frame_t.left;
    lpPS.rcText.top = m_data.Frame_t.top;
    lpPS.rcText.right = lpPS.rcText.right - m_data.Frame_t.right;
    lpPS.rcText.bottom = lpPS.rcText.bottom - m_data.Frame_t.bottom;
    lpPS.dpi = UIWinApi::ToList.drawing_default_dpi;
    lpPS.dwStyle = m_data.dwStyle;
    lpPS.dwStyleEx = m_data.dwStyleEx;
    lpPS.dwTextFormat = m_data.dwTextFormat;
    if (m_data.hFont == 0)
        lpPS.hFont = UIWinApi::ToList.default_font;
    else
        lpPS.hFont = m_data.hFont;

    lpPS.dwState = m_data.dwState;
}
BOOL HHBUI::UIControl::obj_setfont(UIFont *hFont, BOOL fRedraw)
{
    if (hFont != 0)
    {
        if (m_data.hFont)
            delete m_data.hFont;
        m_data.hFont = hFont;
        if (FLAGS_CHECK(m_data.dwStyleEx, eos_ex_autosize))
        {
            FLAGS_DEL(m_data.dwFlags, EOF_BAUTOSIZED);
            SetPos(0, 0, 1, 1, 0, SWP_NOMOVE | SWP_NOZORDER);
        }
        OnBaseProc(m_data.pWnd->GethWnd(), WM_SETFONT, (size_t)hFont, 0);
        if (fRedraw)
            Redraw();
        return TRUE;
    }
    return FALSE;
}
BOOL HHBUI::UIControl::BeginPaint(ps_context& lpPS)
{
    BOOL ret = FALSE;
    obj_getcontext(lpPS);
    if (SUCCEEDED(m_data.canvas->BeginDraw()))
    {
        m_data.canvas->SetClipRect(m_data.Frame_d.left, m_data.Frame_d.top, m_data.Frame_d.right, m_data.Frame_d.bottom);
        if (m_data.IsRotate)
            m_data.canvas->Rotate(m_data.fRotate);
        UIColor crBkg = m_data.Color.crBackground;
        m_data.canvas->Clear(crBkg);

        if (OnBaseProc(GethWnd(), WM_ERASEBKGND, (size_t)&lpPS, MAKELONG(lpPS.uWidth, lpPS.uHeight)) == S_OK)
        {
            if (((m_data.dwStyleEx & eos_ex_composited) == eos_ex_composited))
            {
                m_data.canvas->DrawCanvas(m_data.pWnd->m_data.canvas_display, m_data.Frame_d.left, m_data.Frame_d.top, m_data.Frame_d.right, m_data.Frame_d.bottom,
                    m_data.Frame_w.left + m_data.Frame_d.left, m_data.Frame_w.top + m_data.Frame_d.top, CanvasDrawMode::Over, m_data.alpha);

                if (!crBkg.empty())
                {
                    m_data.lpBackgBrush->SetColor(crBkg);
                    m_data.canvas->FillRect(m_data.lpBackgBrush, m_data.Frame_d.left, m_data.Frame_d.top, m_data.Frame_d.right, m_data.Frame_d.bottom);
                }
            }
            auto bk = m_data.lpBackgroundImage;
            if (bk)
            {
                BOOL lpGrid = FALSE;
                if (bk->lpGrid)
                {
                    if ((bk->lpGrid->left != bk->lpGrid->right) || (bk->lpGrid->top != bk->lpGrid->bottom))
                        lpGrid = TRUE;
                }
                if (lpGrid)
                {
                    info_GridsImage grids{};
                    m_data.canvas->DrawGridsImage(bk->hImage, bk->lpGrid->left, bk->lpGrid->top, bk->lpGrid->right, bk->lpGrid->bottom, &grids, bk->dwAlpha);
                }
                else if (bk->dwRepeat == bir_default)
                {
                    m_data.canvas->DrawImageRect(bk->hImage, bk->x, bk->y, m_data.Frame.right - m_data.Frame.left, m_data.Frame.bottom - m_data.Frame.top, ImageMode::Default, bk->dwAlpha);
                }
                else if (bk->dwRepeat == bir_epault)
                {
                    uint32_t iw = 0, ih = 0;
                    auto hImg = bk->hImage;
                    if (hImg)
                        hImg->GetSize(iw, ih);
                    //iw ih=图片原始宽高，ww wh=窗口宽高，sw sh=缩放后宽高，x y=图片绘制坐标
                    INT ww = m_data.Frame.right, wh = m_data.Frame.bottom, sw = 0, sh = 0, x = bk->x, y = bk->y;

                    float sx = static_cast<float>(ww) / iw;
                    float sy = static_cast<float>(wh) / ih;
                    float sc = std::max(sx, sy);
                    sw = iw * sc;
                    sh = ih * sc;
                    if (sw > ww) x = (ww - sw) / 2;
                    else if (sh > wh) y = (wh - sh) / 2;
                    m_data.canvas->DrawImageRect(bk->hImage, x, y, x + sw, y + sh, ImageMode::Default, bk->dwAlpha);
                }
                else if (bk->dwRepeat == bir_epault_center)
                {
                    m_data.canvas->DrawImageRect(bk->hImage, 0, 0, m_data.Frame.right, m_data.Frame.bottom, ImageMode::ScaleFill, bk->dwAlpha);
                }
                else if (bk->dwRepeat == bir_no_repeat)
                {
                    m_data.canvas->DrawImage(bk->hImage, bk->x, bk->y, bk->dwAlpha);
                }
                else if (bk->dwRepeat == bir_repeat)
                {
                    m_data.canvas->DrawImageRect(bk->hImage, 0, 0, m_data.Frame.right, m_data.Frame.bottom, ImageMode::ScaleCenter, bk->dwAlpha);
                }
                else if (bk->dwRepeat == bir_repeat_center)
                {
                    m_data.canvas->DrawImageRect(bk->hImage, 0, 0, m_data.Frame.right, m_data.Frame.bottom, ImageMode::CenterMiddle, bk->dwAlpha);
                }
            }
            ret = TRUE;
        }
        if (m_data.fBlur != 0.f)
            m_data.canvas->blur(nullptr, m_data.fBlur, nullptr, 0, m_data.fmodesoft);
       
        m_data.canvas->ResetClip();
    }
    return ret;
}
void HHBUI::UIControl::EndPaint()
{
    ps_context lpPS{};
    obj_getcontext(lpPS);
    if (((m_data.dwStyleEx & eos_ex_customdraw) == eos_ex_customdraw))
    {
        DispatchNotify(WMM_CUSTOMDRAW, 0, (size_t)&lpPS);
    }
    if (!FLAGS_CHECK(m_data.dwFlags, EOF_NOBORDER))
    {
        UIColor crb{};
        GetColor(color_border, crb);
        if (!crb.empty())
        {
            if ((m_data.dwState & state_focus) != 0)
            {
                UIColor crfocus{};
                GetColor(color_focus, crfocus);
                if (!crfocus.empty())
                    crb = crfocus;
            }
            m_data.lpBackgBrush->SetColor(crb);
            if (m_data.hPath_Client)
                lpPS.hCanvas->DrawPath(m_data.lpBackgBrush, m_data.hPath_Client, 1.0f);
            else
                lpPS.hCanvas->DrawRect(m_data.lpBackgBrush, m_data.Frame_c.left, m_data.Frame_c.top, m_data.Frame_c.right - 1.f, m_data.Frame_c.bottom - 1.f,
                    1.f, D2D1_DASH_STYLE_SOLID);

        }
    }
    if (m_data.IsRotate)
        m_data.canvas->SetTransform(0);
    m_data.canvas->EndDraw();
}
void HHBUI::UIControl::obj_setchildrenpostion(UIControl* pObjChild, INT x, INT y)
{
    auto objChild = (UIControl*)pObjChild->m_objChildFirst;
    while (objChild != nullptr)
    {
        objChild->m_data.Frame_w = objChild->m_data.Frame;
        objChild->m_data.Frame_w.Offset(x, y);
        //OffsetRect(&objChild->m_data.Frame_w, x, y);
        if (FLAGS_CHECK(objChild->m_data.dwFlags, 32))
        { // TODO: UNKNOWN FLAG
            obj_regionalpath(objChild, objChild->m_data.Frame_w.left, objChild->m_data.Frame_w.top, objChild->m_data.Frame_w.right, objChild->m_data.Frame_w.bottom, objChild->m_data.hPath_Window);
        }
        obj_setchildrenpostion(objChild, objChild->m_data.Frame_w.left, objChild->m_data.Frame_w.top);
        objChild = objChild->m_data.objNext;
    }
}


void HHBUI::UIControl::Enable(BOOL fEnable)
{
    if (((m_data.dwStyle & eos_disabled) == eos_disabled) != fEnable)
    {
        KillFocus();
        m_data.dwState -= (m_data.dwState & (state_hover | state_down));
        if (fEnable)
        {
            m_data.dwState -= (m_data.dwState & state_disable);
            m_data.dwStyle |= eos_disabled;
        }
        else
        {
            m_data.dwState |= state_disable;
            m_data.dwStyle -= (m_data.dwStyle & eos_disabled);
        }
        // _obj_baseproc(hWnd, parent, pObj, WM_STYLECHANGED, ol_style, pObj->dwStyle_);
    }
}

BOOL HHBUI::UIControl::IsEnable()
{
    return !((m_data.dwStyle & eos_disabled) == eos_disabled);
}

BOOL HHBUI::UIControl::SetFocus()
{
    BOOL ret = FALSE;
    if (((m_data.dwState & state_allowfocus) == state_allowfocus))
    {
        auto objFocus = m_data.pWnd->m_data.objFocus.load();
        if (objFocus != this)
            OnBaseProc(GethWnd(), WM_SETFOCUS, (size_t)objFocus, 0);
        ret = TRUE;
    }
    return ret;
}

LPVOID HHBUI::UIControl::GetFocus()
{
    return m_data.pWnd->m_data.objFocus;
}

void HHBUI::UIControl::KillFocus()
{
    OnBaseProc(GethWnd(), WM_KILLFOCUS, 0, 0);
}

BOOL HHBUI::UIControl::SetText(LPCWSTR lpString)
{
    BOOL ret = FALSE;
    if (lstrcmpW(m_data.pstrTitle, lpString) != 0)
    {
        if (m_data.pstrTitle)
        {
            LocalFree((HLOCAL)m_data.pstrTitle);
            m_data.pstrTips = nullptr;
        }
        if (lpString)
            m_data.pstrTitle = StrDupW(lpString);
        ret = TRUE;
        OnBaseProc(GethWnd(), WM_SETTEXT, 0, 0);
        if (m_data.dwStyleEx & eos_ex_autosize)
        {
            FLAGS_DEL(m_data.dwFlags, EOF_BAUTOSIZED);
            SetPos(0, 0, 1, 1, 0, SWP_NOMOVE | SWP_NOZORDER);
        }
        else
        {
            Redraw();
        }
    }
    return ret;
}

LPCWSTR HHBUI::UIControl::GetText()
{
    return m_data.pstrTitle;
}

DWORD HHBUI::UIControl::SetTextFormat(DWORD dwTextFormat, BOOL bRedraw)
{
   auto ret = m_data.dwTextFormat;
   if (dwTextFormat != -1)
       m_data.dwTextFormat = dwTextFormat;
   return ret;
}

DWORD HHBUI::UIControl::GetTextFormat()
{
    return m_data.dwTextFormat;
}

void HHBUI::UIControl::SetPadding(INT left, INT top, INT right, INT bottom, BOOL fRedraw)
{
    if (left != CW_USEDEFAULT)
        m_data.Frame_t.left = UIEngine::fScale(left);
    if (top != CW_USEDEFAULT)
        m_data.Frame_t.top = UIEngine::fScale(top);
    if (right != CW_USEDEFAULT)
        m_data.Frame_t.right = UIEngine::fScale(right);
    if (bottom != CW_USEDEFAULT)
        m_data.Frame_t.bottom = UIEngine::fScale(bottom);
    if (FLAGS_CHECK(m_data.dwStyleEx, eos_ex_autosize))
    {
        FLAGS_DEL(m_data.dwFlags, EOF_BAUTOSIZED);
        SetPos(0, 0, 1, 1, 0, SWP_NOMOVE | SWP_NOZORDER);
    }
    if (fRedraw)
    {
        if (lstrcmpW(m_data.lpClsname, L"form-edit") == 0)
        {
            if (m_data.pfnClsProc)
                m_data.pfnClsProc(GethWnd(), WM_SIZE, 0, 0);
            else
                OnMsgProc(GethWnd(), WM_SIZE, 0, 0);
        }

        Redraw();
    }
}

void HHBUI::UIControl::SetPaddingClient(INT left, INT top, INT right, INT bottom, BOOL fRedraw)
{
    if (left != CW_USEDEFAULT)
        m_data.Frame_c.left = UIEngine::fScale(left);
    if (top != CW_USEDEFAULT)
        m_data.Frame_c.top = UIEngine::fScale(top);
    if (right != CW_USEDEFAULT)
        m_data.Frame_c.right = UIEngine::fScale(right);
    if (bottom != CW_USEDEFAULT)
        m_data.Frame_c.bottom = UIEngine::fScale(bottom);
    if (fRedraw)
        Redraw();
}

void HHBUI::UIControl::SetTipsText(LPCWSTR lpText, UIColor crBlack, UIColor crText)
{
    if (m_data.pstrTips)
    {
        LocalFree((HLOCAL)m_data.pstrTips);
        m_data.pstrTips = nullptr;
    } 
    if (lpText)
        m_data.pstrTips = StrDupW(lpText);
    m_data.Color.crBlack_pstrTips = crBlack;
    m_data.Color.crText_pstrTips = crText;
}

void HHBUI::UIControl::TooltipsPop(LPCWSTR lpText, UIColor crBlack, UIColor crText, INT x, INT y, INT dwTime, BOOL fShow)
{
    m_data.Color.crBlack_pstrTips = crBlack;
    m_data.Color.crText_pstrTips = crText;
    obj_tippopup(lpText, x, y, dwTime, fShow);
}

LRESULT HHBUI::UIControl::DispatchNotify(INT nCode, WPARAM wParam, LPARAM lParam)
{
    LRESULT ret = S_OK;
    if (!m_data.pWnd)
        return S_FALSE;
    if (((m_data.pWnd->m_data.dwFlags & EWF_BDESTROYWINDOW) == EWF_BDESTROYWINDOW))
        return S_FALSE;
    auto iter = m_data.pWnd->m_hTableEvent.find(nCode);
    if (iter != m_data.pWnd->m_hTableEvent.end())
    {
        std::vector<EX_EVENT_HANDLER>& eventHandlerTable = iter->second;
        for (auto& handler : eventHandlerTable)
        {
            if (handler.parent == this)
            {
                ret = handler.pfnCallback(m_data.pWnd, this, m_data.nID, nCode, wParam, lParam);

                if (!ret)
                {
                    break;
                }
                return ret;
            }
        }
    }
    if (m_data.pfnSubClass != 0)//是否有消息回调
        ret = m_data.pfnSubClass(m_data.pWnd->GethWnd(), m_data.pWnd, this, m_data.nID, nCode, wParam, lParam);
    return ret;
}

LRESULT HHBUI::UIControl::SendMsg(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    if (!m_data.pWnd)
        return S_FALSE;
    mempoolmsg_s* p = new mempoolmsg_s();
    LRESULT ret = S_OK;
    if (p != 0)
    {
        p->pObj = this;
        p->uMsg = uMsg;
        p->wParam = wParam;
        p->lParam = lParam;
        ret = SendMessageW(m_data.pWnd->GethWnd(), UIWinApi::ToList.dwMessage, (WPARAM)p, MAKELONG(EMT_OBJECT, 0));
    }
    return ret;
}

LRESULT HHBUI::UIControl::SendMsgProc(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    LRESULT ret = S_OK;
    if (m_data.pfnSubClass != nullptr)
        ret = m_data.pfnSubClass(m_data.pWnd->GethWnd(), m_data.pWnd, this, m_data.nID, uMsg, wParam, lParam);
    return ret;
}

BOOL HHBUI::UIControl::SetEvent(INT nEvent, EventHandlerPROC pfnCallback)
{
    // 查找事件处理程序表
    auto it = m_data.pWnd->m_hTableEvent.find(nEvent);
    if (it == m_data.pWnd->m_hTableEvent.end())
    {
        // 创建新的事件处理程序表并添加到map中
        std::vector<EX_EVENT_HANDLER> eventHandlerTable;
        m_data.pWnd->m_hTableEvent.insert(std::make_pair(nEvent, eventHandlerTable));
        it = m_data.pWnd->m_hTableEvent.find(nEvent);
    }

    std::vector<EX_EVENT_HANDLER>& eventHandlerTable = it->second;

    // 查找匹配的处理程序
    auto handlerIt = std::find_if(eventHandlerTable.begin(), eventHandlerTable.end(),
        [this](const EX_EVENT_HANDLER& handler) { return handler.parent == this; }
    );

    // 处理回调函数和处理程序的逻辑
    if (pfnCallback)
    {
        // 更新或添加处理程序
        if (handlerIt != eventHandlerTable.end())
        {
            handlerIt->pfnCallback = pfnCallback;
        }
        else
        {
            // 添加新的处理程序
            EX_EVENT_HANDLER newHandler{};
            newHandler.parent = this;
            newHandler.pfnCallback = pfnCallback;
            eventHandlerTable.push_back(newHandler);
        }
    }
    else
    {
        // 移除处理程序
        if (handlerIt == eventHandlerTable.end())
        {
            return TRUE;  // 没有匹配的处理程序，返回成功
        }
        eventHandlerTable.erase(handlerIt);
        if (eventHandlerTable.empty())
        {
            m_data.pWnd->m_hTableEvent.erase(it);
        }
    }

    if (nEvent == WMM_CREATE)
        DispatchNotify(WMM_CREATE, 0, 0);

    return TRUE;
}

void HHBUI::UIControl::SetMsgProc(MsgPROC pfnCallback)
{
    m_data.pfnSubClass = pfnCallback;
}

void HHBUI::UIControl::SetMsgCls(ClsPROC pfnClsProc)
{
    m_data.pfnClsProc = pfnClsProc;
}

void HHBUI::UIControl::SetState(DWORD dwState, BOOL fRemove, ExRectF lprcRedraw)
{
    if (fRemove)
        m_data.dwState -= (m_data.dwState & dwState);
    else
        m_data.dwState |= dwState;
    if (!lprcRedraw.empty())
        Redraw(lprcRedraw);
}

INT HHBUI::UIControl::GetState()
{
    return m_data.dwState;
}

BOOL HHBUI::UIControl::GetRect(ExRectF& lpRect, INT nType, BOOL fScale)
{
    ExRectF tmp{};
    FLOAT default_dpi = UIWinApi::ToList.drawing_default_dpi;

    switch (nType)
    {
    case grt_default:
        tmp = m_data.Frame;
        break;
    case grt_client:
        tmp = m_data.Frame_c;
        break;
    case grt_window:
        tmp = m_data.Frame_w;
        break;
    case grt_dirty:
        tmp = m_data.Frame_d;
        break;
    case grt_text:
        tmp.left = m_data.Frame_t.left;
        tmp.top = m_data.Frame_t.top;
        tmp.right = m_data.Frame.right - m_data.Frame.left - m_data.Frame_t.right;
        tmp.bottom = m_data.Frame.bottom - m_data.Frame.top - m_data.Frame_t.bottom;
        break;
    case grt_textoff:
        tmp = m_data.Frame_t;
        break;
    default:
        return FALSE;
    }
    if (!fScale)
        lpRect = tmp.fScaleNo(default_dpi);
    else
        lpRect = tmp;
    return TRUE;
}


void HHBUI::UIControl::obj_regionalpath(UIControl* pObjChild, FLOAT left, FLOAT top, FLOAT right, FLOAT bottom, UIPath*& dpath)
{
    if (dpath)
        delete dpath;
    auto objChild = pObjChild;
    dpath = new UIPath();
    dpath->BeginPath();
    if (SUCCEEDED(dpath->StartFigure(left, top)))
    {
        dpath->AddCustomRoundRect(left, top, right, bottom, UIEngine::fScale(objChild->m_data.radius.left), UIEngine::fScale(objChild->m_data.radius.top), 
            UIEngine::fScale(objChild->m_data.radius.right), UIEngine::fScale(objChild->m_data.radius.bottom));

    }
    dpath->EndPath();
}

void HHBUI::UIControl::obj_updatewindowpostion(BOOL fChild)
{
    m_data.Frame_w = m_data.Frame;
    auto parentObj = (UIControl*)m_data.Parent->m_UIView;
    while (parentObj)
    {
        m_data.Frame_w.Offset(parentObj->m_data.Frame.left, parentObj->m_data.Frame.top);
        //OffsetRect(&m_data.Frame_w, parentObj->m_data.Frame.left, parentObj->m_data.Frame.top);
        parentObj = (UIControl*)parentObj->m_data.Parent->m_UIView;
    }
    if (fChild)
    {
        obj_setchildrenpostion(this, m_data.Frame_w.left, m_data.Frame_w.top);
    }
}
void HHBUI::UIControl::obj_setzbeforet(UIControl* objChildLast, UIBase* pParent)
{
    auto lastControl = objChildLast;
    if (lastControl && (lastControl->m_data.dwStyleEx & eos_ex_topmost) == eos_ex_topmost)
    {
        if (lastControl->m_data.objPrev == nullptr) // 没有置顶组件
        {
            pParent->m_objChildFirst = this;
            lastControl->m_data.objPrev = this;
            m_data.objNext = objChildLast;
        }
        else
        {
            UIControl* objPrev = lastControl;
            while (objPrev)
            {
                if (objPrev && (objPrev->m_data.dwStyleEx & eos_ex_topmost) == eos_ex_topmost)
                {
                    UIControl* tmp = objPrev->m_data.objPrev;
                    if (tmp != nullptr)
                    {
                        objPrev = tmp;
                        continue;
                    }
                    else
                    {
                        objPrev->m_data.objPrev = this;
                        m_data.objNext = objPrev;
                        pParent->m_objChildFirst = this;
                    }
                }
                else
                {
                    UIControl* tmp = objPrev->m_data.objNext;
                    objPrev->m_data.objNext = this;
                    m_data.objNext = tmp;
                    m_data.objPrev = objPrev;
                    if (tmp)
                    {
                        tmp->m_data.objPrev = this;
                    }
                    else
                    {
                        pParent->m_objChildFirst = this;
                    }
                }
                break;
            }
        }
    }
    else if (lastControl)
    {
        lastControl->m_data.objNext = this;
        m_data.objPrev = objChildLast;
        pParent->m_objChildLast = this;
    }
}
void HHBUI::UIControl::obj_zclear(UIControl*& hParent, UIBase*& pParent)
{
    // 保存父级视图和窗口
    pParent = m_data.Parent;
    hParent = (pParent) ? (UIControl*)pParent->m_UIView : nullptr;

    if (!hParent)
    {
        // 如果没有有效的父视图，设置为窗口父级
        pParent = m_data.pWnd;
    }

    if (pParent)
    {
        // 修复父级组件链表
        if (pParent->m_objChildFirst == m_UIView)
        {
            pParent->m_objChildFirst = m_data.objNext;
        }
        if (pParent->m_objChildLast == m_UIView)
        {
            pParent->m_objChildLast = m_data.objPrev;
        }
    }

    // 修复兄弟组件链表
    if (m_data.objNext)
    {
        m_data.objNext->m_data.objPrev = m_data.objPrev;
    }

    if (m_data.objPrev)
    {
        m_data.objPrev->m_data.objNext = m_data.objNext;
    }

    // 清除当前组件的链接关系
    m_data.objNext = nullptr;
    m_data.objPrev = nullptr;
}



