﻿#include "pch.h"
#include "static.h"
#include <common/winapi.h>
struct RECTF
{
	FLOAT left;
	FLOAT top;
	FLOAT right;
	FLOAT bottom;
};
struct H_STATICEX_INFO
{
	LPCTSTR pwzText;
	HHBUI::UIColor color_normal;
	HHBUI::UIColor color_backg;
	LPCTSTR lpwzFontFace;
	INT dwFontSize;
	DWORD dwFontStyle;
	RECTF pctf;
};

HHBUI::UIStatic::UIStatic(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-static", lpszName, dwStyle, dwStyleEx, nID, dwTextFormat);
}

void HHBUI::UIStatic::SetLineColor(UIColor lpLine)
{
	p_data.clr[0] = lpLine;
}

void HHBUI::UIStatic::SetLineWidth(FLOAT width)
{
	p_data.P_Width = width;
}

void HHBUI::UIStatic::SetLineStyle(INT Style)
{
	p_data.type = Style;
}

void HHBUI::UIStatic::SetLineOffset(FLOAT Offset)
{
	p_data.sWidth = Offset;
}

void HHBUI::UIStatic::SetRollStyle(INT Style)
{
	p_data.type = Style;
}

void HHBUI::UIStatic::SetRollSize(INT sWidth)
{
	p_data.sWidth = sWidth;
}

void HHBUI::UIStatic::SetTextBlur(FLOAT fDeviation)
{
	p_data.P_Width = fDeviation;
	SafeRelease(p_data.pEffect);
	p_data.pEffect = 0;
	Redraw();
}

void HHBUI::UIStatic::AddText(LPCWSTR pwzText, UIColor color_normal, UIColor color_backg, LPCWSTR lpwzFontFace, INT dwFontSize, DWORD dwFontStyle)
{
	auto pTR = new H_STATICEX_INFO();
	pTR->pwzText = StrDupW(pwzText);
	pTR->color_normal = color_normal;
	pTR->color_backg = color_backg;

	if (lpwzFontFace == NULL)
		lpwzFontFace = UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName;
	if (dwFontSize == 0)
		dwFontSize = -UIWinApi::ToList.drawing_default_fontLogFont->lfHeight;
	pTR->lpwzFontFace = lpwzFontFace;
	pTR->dwFontSize = dwFontSize;
	pTR->dwFontStyle = dwFontStyle;
	auto Ifont = UIFont(lpwzFontFace, dwFontSize / UIWinApi::ToList.drawing_default_dpi, dwFontStyle);
	FLOAT nTextWidth = NULL, nTextHeight = NULL;
	UICanvas::CalcTextSize(&Ifont, pwzText, m_data.dwTextFormat, m_data.Frame.right - m_data.Frame.left, m_data.Frame.bottom - m_data.Frame.top, &nTextWidth, &nTextHeight);
	nTextWidth += UIEngine::fScale(5);
	nTextHeight += UIEngine::fScale(5);
	auto arraySize = p_data.pArray->size();
	if (arraySize == 0)
	{
		pTR->pctf = { 0,0,nTextWidth,nTextHeight };
	}
	else
	{
		auto pInfo = (H_STATICEX_INFO*)p_data.pArray->get(arraySize);
		pTR->pctf = { pInfo->pctf.right,0,pInfo->pctf.right + nTextWidth,nTextHeight };
	}
	p_data.pArray->insert((size_t)pTR);
}


LRESULT HHBUI::UIStatic::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_CREATE)
	{
		if ((m_data.dwStyle & eos_static_dline) == eos_static_dline)
		{
			p_data.type = D2D1_DASH_STYLE_SOLID;
			p_data.P_Width = 1.0f;
			p_data.sWidth = 30;
		}
		else if ((m_data.dwStyle & eos_static_roll) == eos_static_roll)
		{
			p_data.P_Bool = 0;
			p_data.dstImg = 0;
			p_data.nWidthText = 0;
			p_data.p_Left = 0;
			p_data.P_Width = 0;
			p_data.type = 0;
			p_data.stimer = 300;
			p_data.time = 10;
			p_data.sWidth = 0;
			p_data.direc = 0;
			p_data.hCanvas = new UICanvas(m_data.pWnd, 1, 1);
		}
		else if ((m_data.dwStyle & eos_static_blurtext) == eos_static_blurtext)
		{
			p_data.P_Width = 25;
		}
		else if ((m_data.dwStyle & eos_static_ex) == eos_static_ex)
		{
			p_data.pArray = new UIarray();
		}
	}
	else if (uMsg == WM_MOUSEHOVER)
	{
		SetState(state_hover, FALSE);
		Redraw();
	}
	else if (uMsg == WM_MOUSELEAVE)
	{
		SetState(state_hover, TRUE);
		Redraw();
	}
	else if (uMsg == WM_DESTROY)
	{
		if ((m_data.dwStyle & eos_static_roll) == eos_static_roll)
		{
			delete p_data.hCanvas;
			if (p_data.dstImg)
				delete p_data.dstImg;

		}
		else if ((m_data.dwStyle & eos_static_blurtext) == eos_static_blurtext)
		{
			SafeRelease(p_data.pEffect);
			p_data.pEffect = 0;
		}
		else if ((m_data.dwStyle & eos_static_ex) == eos_static_ex)
		{
			delete p_data.pArray;
		}
	}
	else if (uMsg == WM_SETTEXT || uMsg == WM_SETFONT || uMsg == WM_SIZE || uMsg == WM_SYSCOLORCHANGE)
	{
		if ((m_data.dwStyle & eos_static_roll) == eos_static_roll)
		{
			static_roll_to();
		}
		else if ((m_data.dwStyle & eos_static_blurtext) == eos_static_blurtext)
		{
			SafeRelease(p_data.pEffect);
			p_data.pEffect = 0;
			Redraw();
		}
	}
	else if (uMsg == WM_TIMER && wParam == 7007)
	{
		if ((m_data.dwStyle & eos_static_roll) == eos_static_roll)
		{
			if (p_data.type == 0)
			{
				if (p_data.nWidthText > p_data.P_Width)
				{
					if (p_data.P_Bool == 0)
					{
						p_data.delay = p_data.delay + 1;
						if (p_data.delay > p_data.stimer)
						{
							p_data.delay = 0;
							p_data.P_Bool = 1;
						}
					}
					else
					{
						p_data.step = p_data.step + 1;
						p_data.p_Left = p_data.step;
						Redraw();
					}
					if (p_data.step > p_data.nWidthText)
					{
						p_data.step = 0;
						p_data.P_Bool = 0;
						p_data.p_Left = 0;
						Redraw();
					}

				}
			}
			else if (p_data.type == 1)
			{
				if (p_data.nWidthText > p_data.P_Width)
				{
					if (p_data.P_Bool == 0)
					{
						p_data.delay = p_data.delay + 1;
						if (p_data.delay > p_data.stimer)
						{
							p_data.delay = 0;
							if (p_data.direc)
							{
								p_data.P_Bool = 2;
							}
							else
							{
								p_data.P_Bool = 1;
							}
						}
					}
					else if (p_data.P_Bool == 1)
					{
						p_data.step = p_data.step + 1;
						p_data.p_Left = p_data.step;
						Redraw();
					}
					if (p_data.step > p_data.nWidthText && p_data.P_Bool == 1)
					{
						p_data.direc = 1;
						p_data.P_Bool = 0;
						p_data.p_Left = 0;
						Redraw();
					}
					else if (p_data.P_Bool == 2)
					{
						p_data.step = p_data.step - 1;
						p_data.p_Left = p_data.step;
						if (p_data.step < 0)
						{
							p_data.direc = 0;
							p_data.P_Bool = 0;
							p_data.p_Left = 0;
						}
						Redraw();
					}

				}
			}
		}
	}
	return S_OK;
}

void HHBUI::UIStatic::OnPaintProc(ps_context ps)
{
	auto pstrTitle = GetText();
	UIColor yColor;
	GetColor(color_text_normal, yColor);
	if ((ps.dwState & state_hover) != 0)//还可以添加更多状态 实现各种颜色切换
	{
		GetColor(color_text_hover, yColor);
		if (yColor.empty())//如果没有预先设置悬浮颜色？改为默认
			GetColor(color_text_normal, yColor);
	}
	if ((ps.dwStyle & eos_static_dline) == eos_static_dline)
	{
		FLOAT nWidthText = 0, nHeightText = 0, nTop = ps.uHeight / 2.f;
		if (pstrTitle)
		{
			ps.hCanvas->CalcTextSize(ps.hFont, pstrTitle, DT_LEFT, ps.uWidth, ps.uHeight, &nWidthText, &nHeightText);
			ps.hCanvas->DrawTextByColor(ps.hFont, pstrTitle, ps.dwTextFormat, p_data.sWidth, 0, nWidthText + p_data.sWidth, ps.uHeight, yColor);
		}
		auto hBrush = UIBrush(p_data.clr[0]);
		ps.hCanvas->DrawLine(&hBrush, 0, nTop, p_data.sWidth - 5, nTop, p_data.P_Width, p_data.type);
		ps.hCanvas->DrawLine(&hBrush, nWidthText + p_data.sWidth + 5, nTop, ps.uWidth, nTop, p_data.P_Width, p_data.type);
	}
	else if ((ps.dwStyle & eos_static_roll) == eos_static_roll)
	{
		if (p_data.dstImg)
		{
			ps.hCanvas->DrawImagePartRect(p_data.dstImg, 0, 0, p_data.nWidthText - p_data.p_Left + 2, ps.uHeight, p_data.p_Left, 0, p_data.nWidthText - p_data.p_Left + p_data.p_Left, ps.uHeight);
			if (p_data.p_Left != 0)
			{
				if (p_data.p_Left > p_data.nWidthText - p_data.P_Width || p_data.nWidthText > p_data.P_Width)
				{
					FLOAT m_Value = p_data.P_Width - (p_data.nWidthText - p_data.p_Left);
					ps.hCanvas->DrawImagePartRect(p_data.dstImg, p_data.P_Width - m_Value, 0, p_data.nWidthText + p_data.P_Width - m_Value, ps.uHeight, 0, 0, p_data.nWidthText, ps.uHeight);
				}
			}
		}
		else if (pstrTitle)
		{
			ps.hCanvas->DrawTextByColor(ps.hFont, pstrTitle, DT_VCENTER | DT_SINGLELINE, ps.rcText.left + 2, ps.rcText.top, ps.rcText.right - 2, ps.rcText.bottom, yColor);
		}
	}
	else if ((ps.dwStyle & eos_static_blurtext) == eos_static_blurtext)
	{
		if (p_data.pEffect == 0)
		{
			ID2D1DeviceContext* pDeviceContext = nullptr;
			if (UIDrawContext::ToList.d2d_device->CreateDeviceContext(D2D1_DEVICE_CONTEXT_OPTIONS_NONE, &pDeviceContext) == 0)
			{
				pDeviceContext->SetUnitMode(D2D1_UNIT_MODE_PIXELS);

				ID2D1Bitmap* pBitmap = UIDrawContext::CreateBitmap(ps.uWidth, ps.uHeight);
				pDeviceContext->SetTarget(pBitmap);

				pDeviceContext->BeginDraw();
				auto brush = UIBrush(yColor);
				ps.hCanvas->DeviceText(pDeviceContext, &brush, ps.hFont, pstrTitle, ps.dwTextFormat, ps.rcText.left, ps.rcText.top, ps.rcText.right, ps.rcText.bottom);

				pDeviceContext->EndDraw();

				// 高斯模糊
				UIDrawContext::ToList.d2d_dc->Flush();
				UIDrawContext::ToList.d2d_dc->CreateEffect(CLSID_D2D1GaussianBlur, &p_data.pEffect);
				if (p_data.pEffect)
				{
					p_data.pEffect->SetInput(0, pBitmap);
					p_data.pEffect->SetValue(D2D1_GAUSSIANBLUR_PROP_BORDER_MODE, D2D1_BORDER_MODE_SOFT);
					FLOAT fScale = p_data.P_Width / 10;
					p_data.pEffect->SetValue(D2D1_GAUSSIANBLUR_PROP_STANDARD_DEVIATION, fScale);
					D2D1_POINT_2F targetOffset = { 5, 0 };
					UIDrawContext::ToList.d2d_dc->DrawImage(p_data.pEffect, targetOffset);
					SafeRelease(pBitmap);
					SafeRelease(pDeviceContext);
				}
			}
		}
		else
		{
			D2D1_POINT_2F targetOffset = { 5, 0 };
			UIDrawContext::ToList.d2d_dc->DrawImage(p_data.pEffect, targetOffset);
		}
	}
	else if ((ps.dwStyle & eos_static_pro) == eos_static_pro)
	{
		if (pstrTitle)
		{
			auto brush = UIBrush(yColor);
			ps.hCanvas->DrawTextEx(&brush, ps.hFont, pstrTitle, ps.dwTextFormat, ps.rcText.left, ps.rcText.top, ps.rcText.right, ps.rcText.bottom);
		}
	}
	else if ((ps.dwStyle & eos_static_ex) == eos_static_ex)
	{
		auto brush = UIBrush(yColor);
		auto arraySize = p_data.pArray->size();
		for (size_t i = 1; i <= arraySize; i++)
		{
			auto pInfo = (H_STATICEX_INFO*)p_data.pArray->get(i);
			if (!pInfo->color_backg.empty())
			{
				brush.SetColor(pInfo->color_backg);
				ps.hCanvas->FillRect(&brush, pInfo->pctf.left, 0, pInfo->pctf.right, ps.uHeight);
			}
			if (!pInfo->color_normal.empty())
			{
				brush.SetColor(pInfo->color_normal);
			}
			else
			{
				brush.SetColor(yColor);
			}
			ps.hCanvas->DrawTextAndFontName(&brush, pInfo->pwzText, ps.dwTextFormat, pInfo->pctf.left, 0, pInfo->pctf.right, ps.uHeight, pInfo->lpwzFontFace, pInfo->dwFontSize, pInfo->dwFontStyle);
		}
	}
	else
	{
		if (pstrTitle)
			ps.hCanvas->DrawTextByColor(ps.hFont, pstrTitle, ps.dwTextFormat, ps.rcText.left, ps.rcText.top, ps.rcText.right, ps.rcText.bottom, yColor);
	}
}

void HHBUI::UIStatic::static_roll_to()
{
	KillTimer();
	if (p_data.dstImg)
	{
		delete p_data.dstImg;
		p_data.dstImg = nullptr;
	}
	p_data.nWidthText = p_data.sWidth;
	if (p_data.sWidth == 0)
	{
		FLOAT nHeightText = 0;
		UICanvas::CalcTextSize(GetFont(), GetText(), DT_VCENTER | DT_SINGLELINE, m_data.Frame.right - m_data.Frame.left, 0, &p_data.nWidthText, &nHeightText);
		p_data.nWidthText += UIEngine::fScale(40);
	}
	INT sw = m_data.Frame.bottom - m_data.Frame.top + UIEngine::fScale(2);
	if (p_data.nWidthText != p_data.hCanvas->GetSize().cx && sw != p_data.hCanvas->GetSize().cy)
		p_data.hCanvas->Resize(p_data.nWidthText, sw);
	ps_customdraw ecd{};
	ecd.hCanvas = p_data.hCanvas;
	ecd.dwState = m_data.dwState;
	ecd.dwStyle = m_data.dwStyle;
	ecd.dwTextFormat = m_data.dwTextFormat;
	ecd.hFont = m_data.hFont;
	ecd.dpi = UIEngine::GetDefaultScale();
	ecd.rcPaint.left = m_data.Frame.left;
	ecd.rcPaint.top = m_data.Frame.top;
	ecd.rcPaint.right = m_data.Frame.right;
	ecd.rcPaint.bottom = m_data.Frame.bottom;
	ecd.uWidth = p_data.nWidthText;
	ecd.uHeight = m_data.Frame.bottom - m_data.Frame.top;

	p_data.hCanvas->BeginDraw();
	if (OnPsCustomDraw(ecd) == false)
	{
		if (p_data.nWidthText > m_data.Frame.right - m_data.Frame.left)
		{
			UIColor yColor;
			GetColor(color_text_normal, yColor);
			p_data.hCanvas->DrawTextByColor(GetFont(), GetText(), DT_VCENTER | DT_SINGLELINE, 0, 0, p_data.nWidthText, ecd.uHeight, yColor);
			p_data.hCanvas->EndDraw();
			p_data.hCanvas->ToImage(&p_data.dstImg);
			SetTimer(7007, p_data.time);
		}
		else
		{
			p_data.p_Left = 0;
			Redraw();
		}
	}
	else
	{
		p_data.hCanvas->EndDraw();
		p_data.hCanvas->ToImage(&p_data.dstImg);
	}
	
}

