﻿#include "hhbui.h"
using namespace HHBUI;

void testmodal(HWND hWnd)
{
    auto window = new UIWnd(0, 0, 400, 200, L"hello Modal", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
        UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE | UISTYLE_MMODAL, hWnd);
    window->SetBackgColor(UIColor(253, 253, 255, 255));
    window->SetBorderColor(UIColor(20, 126, 255, 255));
    window->SetShadowColor(UIColor(20, 126, 255, 155));
    window->SetRadius(10);

    window->Show();
    //window->MessageLoop();
}