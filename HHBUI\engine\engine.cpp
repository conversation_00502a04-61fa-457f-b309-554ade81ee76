﻿#include "pch.h"
#include "engine.h"
#include "common/winapi.h"
#include "common/Exception.h"
#include <immintrin.h>
#include <algorithm>
#include <thread>

using namespace std::chrono;

namespace HHBUI {
	// 现代化的Windows版本检测
	bool IsWin10OrLater() noexcept {
		try {
			HMODULE ntdll = LoadLibraryW(L"ntdll.dll");
			if (!ntdll) return false;

			using RtlGetVersionProc = void(WINAPI*)(DWORD*, DWORD*, DWORD*);
			auto proc = reinterpret_cast<RtlGetVersionProc>(
				GetProcAddress(ntdll, "RtlGetNtVersionNumbers"));

			if (proc) {
				DWORD major, minor, build;
				proc(&major, &minor, &build);
				FreeLibrary(ntdll);
				return (major >= 10);
			}

			FreeLibrary(ntdll);
			return false;
		}
		catch (...) {
			return false;
		}
	}

	// 设置DPI感知
	void SetupDpiAwareness() noexcept {
		try {
			if (UIWinApi::ToList.dwMajorVersion) {
				SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
			} else {
				SetProcessDPIAware();
			}
		}
		catch (...) {
			// 忽略DPI设置错误
		}
	}
}

// 向后兼容的旧函数
BOOL IsWin10System()
{
	return HHBUI::IsWin10OrLater() ? TRUE : FALSE;
}
namespace HHBUI {
	// DpiManager类实现
	DpiManager::DpiInfo DpiManager::GetSystemDpiInfo() noexcept {
		DpiInfo info;

		try {
			// 使用RAII管理DC
			struct DcGuard {
				HDC dc;
				DcGuard() : dc(::GetDC(nullptr)) {}
				~DcGuard() { if (dc) ::ReleaseDC(nullptr, dc); }
				operator HDC() const { return dc; }
			} dc_guard;

			if (dc_guard.dc) {
				info.raw_dpi_x = static_cast<float>(GetDeviceCaps(dc_guard.dc, LOGPIXELSX));
				info.raw_dpi_y = static_cast<float>(GetDeviceCaps(dc_guard.dc, LOGPIXELSY));

				info.scale_x = info.raw_dpi_x / USER_DEFAULT_SCREEN_DPI;
				info.scale_y = info.raw_dpi_y / USER_DEFAULT_SCREEN_DPI;

				info.is_high_dpi = (info.scale_x > 1.0f || info.scale_y > 1.0f);
			}
		}
		catch (...) {
			// 返回默认值
		}

		return info;
	}

	float DpiManager::CalculateOptimalScale(float user_scale) noexcept {
		auto dpi_info = GetSystemDpiInfo();
		float scale = user_scale > 0.0f ? user_scale : dpi_info.scale_x;

		// 限制最大缩放比例
		constexpr float MAX_SCALE = 2.0f;
		if (scale >= MAX_SCALE) {
			scale = 1.25f;
		}

		// 量化到0.25的倍数
		constexpr float QUANTIZE_STEP = 0.25f;
		const float remainder = std::fmod(scale, QUANTIZE_STEP);
		if (remainder != 0.0f) {
			scale = std::round(scale / QUANTIZE_STEP) * QUANTIZE_STEP;
		}

		// 确保最小缩放比例
		constexpr float MIN_SCALE = 0.75f;
		return std::max(scale, MIN_SCALE);
	}

	float DpiManager::ScaleValue(float value, float scale) noexcept {
		if (scale <= 1.0f) return value;
		return std::round(value * scale);
	}

	float DpiManager::UnscaleValue(float scaled_value, float scale) noexcept {
		if (scale <= 1.0f) return scaled_value;
		return scaled_value / scale;
	}

	// DPI设置函数
	UIEngine::InitStatus SetupDpiScaling(float user_scale) noexcept {
		try {
			auto dpi_info = DpiManager::GetSystemDpiInfo();
			float optimal_scale = DpiManager::CalculateOptimalScale(user_scale);

			UIWinApi::ToList.CapsdpiX = dpi_info.raw_dpi_x;
			UIWinApi::ToList.CapsdpiY = dpi_info.raw_dpi_y;
			UIWinApi::ToList.drawing_default_dpi = optimal_scale;

			OptimizedScaling::UpdateScale(optimal_scale);
			EngineState::SetDpiScale(optimal_scale);

			return {UIEngine::InitResult::Success, L"DPI缩放设置成功", S_OK};
		}
		catch (...) {
			return {UIEngine::InitResult::DpiSetupFailed, L"DPI缩放设置失败", E_FAIL};
		}
	}
}

// 向后兼容的DpiManager函数
void DpiManager(FLOAT Scaledpi, FLOAT &logPixelsY, FLOAT &logPixelsX)
{
	auto dpi_info = HHBUI::DpiManager::GetSystemDpiInfo();
	logPixelsY = dpi_info.scale_y;
	logPixelsX = HHBUI::DpiManager::CalculateOptimalScale(Scaledpi);

	HHBUI::UIWinApi::ToList.CapsdpiX = dpi_info.raw_dpi_x;
	HHBUI::UIWinApi::ToList.CapsdpiY = dpi_info.raw_dpi_y;
}
HRESULT HHBUI::UIEngine::Init(info_Init* info)
{
	return_if_false(UIWinApi::ToList.engine_instance == 0, {}, S_FALSE);
	throw_if_failed(CoInitialize(nullptr), L"COM初始化失败");
	UIWinApi::ToList.dwMajorVersion = IsWin10System();
	if (UIWinApi::ToList.dwMajorVersion)
		SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);//win7不支持
	else
		SetProcessDPIAware();
	try
	{
		int device = -1;
		HINSTANCE hInstance = NULL;
		BOOL default_font = FALSE;
		//获取系统默认字体
		UIWinApi::ToList.drawing_default_fontLogFont = new LOGFONTW();
		INT default_font_Size = 14; FLOAT logPixelsY = 0.f;
		SystemParametersInfoW(SPI_GETICONTITLELOGFONT, sizeof(LOGFONTW), UIWinApi::ToList.drawing_default_fontLogFont, false);
		if (info)
		{
			device = info->device;
			DpiManager(info->dwScaledpi, logPixelsY, UIWinApi::ToList.drawing_default_dpi);

			//如果提供了默认字体信息就使用
			if (info->default_font_Face != NULL || info->default_font_Size != 0 || info->default_font_Style != 0)
			{
				if (info->default_font_Face == NULL)
					info->default_font_Face = UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName;
				else
				{
					size_t i = lstrlenW(info->default_font_Face);
					if (i > 0)
					{
						RtlMoveMemory((LPVOID)UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName, info->default_font_Face, i * 2 + 2);
					}
				}

				if (info->default_font_Size == NULL)
					default_font_Size = 15;
				else
					default_font_Size = info->default_font_Size;

				default_font = TRUE;
			}
			if (info->hInstance)
				hInstance = info->hInstance;
			UIWinApi::ToList.dwDebug = info->dwDebug;
		}
		else
		{
			UIWinApi::ToList.dwDebug = FALSE;
			DpiManager(0, logPixelsY, UIWinApi::ToList.drawing_default_dpi);
		}
		UIWinApi::ToList.drawing_default_fontLogFont->lfHeight = -UIEngine::fScale(default_font_Size);

		if (!hInstance)
			hInstance = GetModuleHandleW(NULL);
		if (FAILED(UIWinApi::Init(hInstance)))
			return S_FALSE;
		if (FAILED(UIDrawContext::Init(device)))
			return S_FALSE;
	
		UIWinApi::ToList.default_font = new UIFont();
		if (hInstance != NULL)
		{
			TCHAR szFilePath[MAX_PATH + 1];
			GetModuleFileName(hInstance, szFilePath, MAX_PATH); //文件路径;文件可以是*.exe、*.dll、*.ico
			UIWinApi::ToList.hIcon = ExtractIconW(hInstance, szFilePath, 0);
			UIWinApi::ToList.hIconsm = ExtractIconW(hInstance, szFilePath, 0);
			UIWinApi::ToList.engine_instance = hInstance;
		}
	
		UIWnd::RegClass(L"Hhbui.WindowClass.UI", 0, 0);

		return S_OK;
	}
	catch_default({});

}
HRESULT HHBUI::UIEngine::UnInit()
{
	handle_if_false(UIWinApi::ToList.engine_instance != NULL, EE_NOREADY, L"引擎尚未初始化");
	try
	{
		delete UIWinApi::ToList.default_font;
		UIWinApi::UnInit();
		UIDrawContext::UnInit();
		CoUninitialize();
		UIWinApi::ToList.engine_instance = NULL;
		if (UIWinApi::ToList.hIcon)
			DestroyIcon(UIWinApi::ToList.hIcon);
		if (UIWinApi::ToList.hIconsm)
			DestroyIcon(UIWinApi::ToList.hIconsm);
		return S_OK;
	}
	catch_default({});
}


BOOL HHBUI::UIEngine::QueryDebug()
{
	return UIWinApi::ToList.dwDebug;
}
BOOL TOAPI HHBUI::UIEngine::QueryInit()
{
	return UIWinApi::ToList.engine_instance != 0;
}
FLOAT TOAPI HHBUI::UIEngine::fScale(FLOAT n)
{
	if (UIWinApi::ToList.drawing_default_dpi > 1.f) {
		n = round(n * UIWinApi::ToList.drawing_default_dpi);
	}
	return n;
}
FLOAT TOAPI HHBUI::UIEngine::GetDefaultScale()
{
	return UIWinApi::ToList.drawing_default_dpi;
}
FLOAT TOAPI HHBUI::UIEngine::GetTime()
{
	static auto start_time = steady_clock::now();
	auto now = steady_clock::now();
	auto elapsed = duration_cast<duration<float>>(now - start_time);
	return elapsed.count();
}
LPCWSTR TOAPI HHBUI::UIEngine::GetVersion()
{
	return HHBUI_VERSION;
}

namespace HHBUI {
	// EngineLogger实现
	void EngineLogger::SetDebugMode(bool enabled) noexcept {
		std::lock_guard<std::mutex> lock(log_mutex_);
		debug_enabled_ = enabled;
	}

	void EngineLogger::LogError(const ErrorDetail& detail) noexcept {
		try {
			std::lock_guard<std::mutex> lock(log_mutex_);
			error_history_.push_back(detail);

			constexpr size_t MAX_HISTORY = 100;
			if (error_history_.size() > MAX_HISTORY) {
				error_history_.erase(error_history_.begin());
			}

			if (debug_enabled_) {
				OutputErrorToDebug(detail);
			}
		}
		catch (...) {
			// 日志系统不应该抛出异常
		}
	}

	void EngineLogger::Log(LogLevel level, const std::wstring& message) noexcept {
		if (!debug_enabled_ && level < LogLevel::Warning) return;

		try {
			std::lock_guard<std::mutex> lock(log_mutex_);
			std::wstring log_message = FormatLogMessage(level, message);
			OutputDebugStringW(log_message.c_str());
		}
		catch (...) {
			// 忽略日志错误
		}
	}

	std::vector<ErrorDetail> EngineLogger::GetErrorHistory() noexcept {
		std::lock_guard<std::mutex> lock(log_mutex_);
		return error_history_;
	}

	void EngineLogger::ClearErrorHistory() noexcept {
		std::lock_guard<std::mutex> lock(log_mutex_);
		error_history_.clear();
	}

	void EngineLogger::OutputErrorToDebug(const ErrorDetail& detail) noexcept {
		try {
			std::wstring message = L"[HHBUI Error] " + detail.component +
								 L"::" + detail.operation + L" - " + detail.description;
			if (detail.hr_code != S_OK) {
				message += L" (HRESULT: 0x" +
						  std::to_wstring(static_cast<unsigned long>(detail.hr_code)) + L")";
			}
			message += L"\n";
			OutputDebugStringW(message.c_str());
		}
		catch (...) {
			// 忽略格式化错误
		}
	}

	std::wstring EngineLogger::FormatLogMessage(LogLevel level, const std::wstring& message) noexcept {
		try {
			const wchar_t* level_str = L"UNKNOWN";
			switch (level) {
				case LogLevel::Debug: level_str = L"DEBUG"; break;
				case LogLevel::Info: level_str = L"INFO"; break;
				case LogLevel::Warning: level_str = L"WARNING"; break;
				case LogLevel::Error: level_str = L"ERROR"; break;
				case LogLevel::Critical: level_str = L"CRITICAL"; break;
			}

			return L"[HHBUI " + std::wstring(level_str) + L"] " + message + L"\n";
		}
		catch (...) {
			return L"[HHBUI] " + message + L"\n";
		}
	}

	// OptimizedScaling实现
	void OptimizedScaling::ScaleValues(float* values, size_t count) noexcept {
		if (!needs_scaling_.load(std::memory_order_relaxed) || !values) {
			return;
		}

		const float scale = GetCachedScale();
		if (scale <= 1.0f) return;

		ScaleValuesScalar(values, count, scale);
	}

	void OptimizedScaling::UpdateScale(float new_scale) noexcept {
		cached_scale_ = new_scale;
		scale_cached_ = true;
		needs_scaling_.store(new_scale > 1.0f, std::memory_order_relaxed);
	}

	float OptimizedScaling::GetCachedScale() noexcept {
		if (scale_cached_) {
			return cached_scale_;
		}

		cached_scale_ = UIWinApi::ToList.drawing_default_dpi;
		scale_cached_ = true;
		return cached_scale_;
	}

	void OptimizedScaling::ScaleValuesScalar(float* values, size_t count, float scale) noexcept {
		for (size_t i = 0; i < count; ++i) {
			values[i] = std::round(values[i] * scale);
		}
	}

	// HighResolutionTimer实现
	float HighResolutionTimer::GetElapsedTime() noexcept {
		if (!initialized_.exchange(true, std::memory_order_relaxed)) {
			start_time_ = std::chrono::steady_clock::now();
		}

		const auto now = std::chrono::steady_clock::now();
		const auto elapsed = std::chrono::duration_cast<
			std::chrono::duration<float>>(now - start_time_);
		return elapsed.count();
	}

	void HighResolutionTimer::Reset() noexcept {
		start_time_ = std::chrono::steady_clock::now();
		initialized_.store(true, std::memory_order_relaxed);
	}

	// EngineState实现
	void EngineState::SetInitialized(bool state) noexcept {
		initialized_.store(state, std::memory_order_relaxed);
	}

	void EngineState::SetDebugMode(bool state) noexcept {
		debug_mode_.store(state, std::memory_order_relaxed);
	}

	void EngineState::SetDpiScale(float scale) noexcept {
		dpi_scale_.store(scale, std::memory_order_relaxed);
	}

	void EngineState::SetCurrentDevice(RenderDevice device) noexcept {
		current_device_.store(device, std::memory_order_relaxed);
	}

	bool EngineState::IsInitialized() noexcept {
		return initialized_.load(std::memory_order_relaxed);
	}

	bool EngineState::IsDebugMode() noexcept {
		return debug_mode_.load(std::memory_order_relaxed);
	}

	float EngineState::GetDpiScale() noexcept {
		return dpi_scale_.load(std::memory_order_relaxed);
	}

	RenderDevice EngineState::GetCurrentDevice() noexcept {
		return current_device_.load(std::memory_order_relaxed);
	}

	// 现代化UIEngine方法实现
	UIEngine::InitStatus UIEngine::Initialize(const EngineConfig* config) noexcept {
		try {
			const EngineConfig default_config;
			const EngineConfig& cfg = config ? *config : default_config;

			return InitializeInternal(cfg);
		}
		catch (const std::exception& e) {
			std::string error_str = e.what();
			std::wstring error_wstr(error_str.begin(), error_str.end());
			return {InitResult::UnknownError,
				   L"初始化过程中发生异常: " + error_wstr, E_FAIL};
		}
		catch (...) {
			return {InitResult::UnknownError, L"初始化过程中发生未知异常", E_FAIL};
		}
	}

	UIEngine::InitStatus UIEngine::InitializeInternal(const EngineConfig& config) noexcept {
		InitStep current_step = InitStep::CheckState;

		try {
			// 1. 检查当前状态
			current_step = InitStep::CheckState;
			if (UIWinApi::ToList.engine_instance != nullptr) {
				return {InitResult::AlreadyInitialized, L"引擎已经初始化", S_FALSE};
			}

			// 2. COM初始化
			current_step = InitStep::ComInitialize;
			HRESULT hr = CoInitialize(nullptr);
			if (FAILED(hr)) {
				return {InitResult::ComInitFailed, L"COM初始化失败", hr};
			}

			// 3. 系统版本检测
			current_step = InitStep::SystemDetection;
			UIWinApi::ToList.dwMajorVersion = IsWin10OrLater();
			SetupDpiAwareness();

			// 4. DPI设置
			current_step = InitStep::DpiSetup;
			auto dpi_result = SetupDpiScaling(config.dpi_scale);
			if (!dpi_result.is_success()) {
				return dpi_result;
			}

			// 5. WinAPI初始化
			current_step = InitStep::WinApiInit;
			HINSTANCE instance = config.instance ? config.instance : GetModuleHandleW(nullptr);
			hr = UIWinApi::Init(instance);
			if (FAILED(hr)) {
				return {InitResult::WinApiInitFailed, L"WinAPI初始化失败", hr};
			}

			// 6. 绘制上下文初始化
			current_step = InitStep::DrawContextInit;
			hr = UIDrawContext::Init(static_cast<int>(config.device));
			if (FAILED(hr)) {
				return {InitResult::DrawContextInitFailed, L"绘制上下文初始化失败", hr};
			}

			// 7. 字体设置
			current_step = InitStep::FontSetup;
			UIWinApi::ToList.drawing_default_fontLogFont = new LOGFONTW();
			SystemParametersInfoW(SPI_GETICONTITLELOGFONT, sizeof(LOGFONTW),
								 UIWinApi::ToList.drawing_default_fontLogFont, FALSE);

			// 应用字体配置
			if (!config.font.face_name.empty()) {
				const size_t max_len = std::min(config.font.face_name.length(),
											   static_cast<size_t>(LF_FACESIZE - 1));
				std::wmemcpy(UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName,
							config.font.face_name.c_str(), max_len);
				UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName[max_len] = L'\0';
			}

			if (config.font.size > 0) {
				UIWinApi::ToList.drawing_default_fontLogFont->lfHeight =
					-static_cast<LONG>(OptimizedScaling::ScaleValue(static_cast<float>(config.font.size)));
			}

			UIWinApi::ToList.default_font = new UIFont();

			// 8. 资源设置
			current_step = InitStep::ResourceSetup;
			if (instance) {
				TCHAR szFilePath[MAX_PATH + 1];
				if (GetModuleFileName(instance, szFilePath, MAX_PATH) > 0) {
					UIWinApi::ToList.hIcon = ExtractIconW(instance, szFilePath, 0);
					UIWinApi::ToList.hIconsm = ExtractIconW(instance, szFilePath, 0);
				}
			}

			// 9. 窗口类注册
			current_step = InitStep::WindowClassReg;
			if (!UIWnd::RegClass(L"Hhbui.WindowClass.UI", 0, 0)) {
				return {InitResult::WindowClassRegFailed, L"窗口类注册失败", E_FAIL};
			}

			// 10. 设置引擎状态
			UIWinApi::ToList.dwDebug = config.debug_mode;
			UIWinApi::ToList.engine_instance = instance;

			EngineState::SetInitialized(true);
			EngineState::SetDebugMode(config.debug_mode);
			EngineState::SetCurrentDevice(config.device);
			EngineLogger::SetDebugMode(config.debug_mode);

			EngineLogger::Log(EngineLogger::LogLevel::Info, L"HHBUI引擎初始化成功");
			return {InitResult::Success, L"引擎初始化成功", S_OK};
		}
		catch (const Exception& e) {
			CleanupResources();
			return {InitResult::UnknownError,
				   L"初始化步骤 " + std::to_wstring(static_cast<int>(current_step)) +
				   L" 失败: " + e.message(), e.status()};
		}
		catch (...) {
			CleanupResources();
			return {InitResult::UnknownError,
				   L"初始化步骤 " + std::to_wstring(static_cast<int>(current_step)) + L" 发生未知错误",
				   E_FAIL};
		}
	}

	UIEngine::InitStatus UIEngine::Shutdown() noexcept {
		try {
			if (UIWinApi::ToList.engine_instance == nullptr) {
				return {InitResult::Success, L"引擎未初始化，无需反初始化", S_OK};
			}

			EngineLogger::Log(EngineLogger::LogLevel::Info, L"开始引擎反初始化");

			CleanupResources();
			UIWinApi::UnInit();
			UIDrawContext::UnInit();
			CoUninitialize();

			UIWinApi::ToList.engine_instance = nullptr;
			EngineState::SetInitialized(false);
			EngineState::SetDebugMode(false);

			EngineLogger::Log(EngineLogger::LogLevel::Info, L"引擎反初始化完成");
			return {InitResult::Success, L"引擎反初始化成功", S_OK};
		}
		catch (const Exception& e) {
			return {InitResult::UnknownError, L"反初始化失败: " + e.message(), e.status()};
		}
		catch (...) {
			return {InitResult::UnknownError, L"反初始化过程中发生未知错误", E_FAIL};
		}
	}

	void UIEngine::CleanupResources() noexcept {
		try {
			if (UIWinApi::ToList.default_font) {
				delete UIWinApi::ToList.default_font;
				UIWinApi::ToList.default_font = nullptr;
			}

			if (UIWinApi::ToList.drawing_default_fontLogFont) {
				delete UIWinApi::ToList.drawing_default_fontLogFont;
				UIWinApi::ToList.drawing_default_fontLogFont = nullptr;
			}

			if (UIWinApi::ToList.hIcon) {
				DestroyIcon(UIWinApi::ToList.hIcon);
				UIWinApi::ToList.hIcon = nullptr;
			}

			if (UIWinApi::ToList.hIconsm) {
				DestroyIcon(UIWinApi::ToList.hIconsm);
				UIWinApi::ToList.hIconsm = nullptr;
			}
		}
		catch (...) {
			// 忽略清理过程中的异常
		}
	}

	EngineConfig UIEngine::ConvertLegacyConfig(const info_Init* info) noexcept {
		EngineConfig config;

		if (info) {
			config.device = static_cast<RenderDevice>(info->device);
			config.instance = info->hInstance;
			config.dpi_scale = info->dwScaledpi;
			config.debug_mode = info->dwDebug != FALSE;

			if (info->default_font_Face || info->default_font_Size || info->default_font_Style) {
				if (info->default_font_Face) {
					config.font.face_name = info->default_font_Face;
				}
				if (info->default_font_Size > 0) {
					config.font.size = info->default_font_Size;
				}
				config.font.style = static_cast<FontStyle>(info->default_font_Style);
			}
		}

		return config;
	}

	// 现代化的查询和工具函数
	bool UIEngine::IsDebugMode() noexcept {
		return EngineState::IsDebugMode();
	}

	bool UIEngine::IsInitialized() noexcept {
		return EngineState::IsInitialized();
	}

	float UIEngine::ScaleValue(float value) noexcept {
		return OptimizedScaling::ScaleValue(value);
	}

	float UIEngine::GetDpiScale() noexcept {
		return EngineState::GetDpiScale();
	}

	float UIEngine::GetElapsedTime() noexcept {
		return HighResolutionTimer::GetElapsedTime();
	}

	std::wstring_view UIEngine::GetVersion() noexcept {
		return HHBUI_VERSION;
	}

	void UIEngine::ScaleValues(float* values, size_t count) noexcept {
		OptimizedScaling::ScaleValues(values, count);
	}

	UIEngine::EngineInfo UIEngine::GetEngineInfo() noexcept {
		return {
			.initialized = EngineState::IsInitialized(),
			.debug_mode = EngineState::IsDebugMode(),
			.dpi_scale = EngineState::GetDpiScale(),
			.current_device = EngineState::GetCurrentDevice(),
			.version = std::wstring(HHBUI_VERSION),
			.elapsed_time = HighResolutionTimer::GetElapsedTime()
		};
	}

	std::vector<ErrorDetail> UIEngine::GetErrorHistory() noexcept {
		return EngineLogger::GetErrorHistory();
	}

	void UIEngine::ClearErrorHistory() noexcept {
		EngineLogger::ClearErrorHistory();
	}

	// 向后兼容的新API到旧API的桥接
	HRESULT UIEngine::Init(info_Init* info) {
		EngineConfig config = ConvertLegacyConfig(info);
		auto result = Initialize(&config);
		return result.is_success() ? S_OK : result.hr_code;
	}

	HRESULT UIEngine::UnInit() {
		auto result = Shutdown();
		return result.is_success() ? S_OK : result.hr_code;
	}

	BOOL UIEngine::QueryDebug() {
		return IsDebugMode() ? TRUE : FALSE;
	}

	BOOL UIEngine::QueryInit() {
		return IsInitialized() ? TRUE : FALSE;
	}

	FLOAT UIEngine::fScale(FLOAT n) {
		return ScaleValue(n);
	}

	FLOAT UIEngine::GetDefaultScale() {
		return GetDpiScale();
	}

	FLOAT UIEngine::GetTime() {
		return GetElapsedTime();
	}

	LPCWSTR UIEngine::GetVersion_Legacy() {
		return HHBUI_VERSION;
	}
}