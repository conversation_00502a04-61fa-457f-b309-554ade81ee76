#include "pch.h"
#include "engine.h"
#include "common/winapi.h"
#include "common/Exception.h"

BOOL IsWin10System()
{
	//string与CString转换
	//string sPath = (LPCSTR)(CStringA)(strPath);
	std::string vname;
	// 先判断是否为win8.1或win10
	typedef void(__stdcall* NTPROC)(DWORD*, DWORD*, DWORD*);
	HINSTANCE hinst = LoadLibrary(L"ntdll.dll");
	DWORD dwMajor, dwMinor = 1, dwBuildNumber;
	if (hinst)
	{
		NTPROC proc = (NTPROC)GetProcAddress(hinst, "RtlGetNtVersionNumbers");
		proc(&dwMajor, &dwMinor, &dwBuildNumber);

		if (dwMajor == 10 && dwMinor == 0)
		{
			return TRUE;
		}
	}
	return FALSE;
}
void DpiManager(FLOAT Scaledpi, FLOAT &logPixelsY, FLOAT &logPixelsX)
{
	HDC hdcMeasure = ::GetDC(NULL);
	HHBUI::UIWinApi::ToList.CapsdpiX = (FLOAT)GetDeviceCaps(hdcMeasure, LOGPIXELSX);
	HHBUI::UIWinApi::ToList.CapsdpiY = (FLOAT)GetDeviceCaps(hdcMeasure, LOGPIXELSY);
	logPixelsY = HHBUI::UIWinApi::ToList.CapsdpiY / USER_DEFAULT_SCREEN_DPI;
	logPixelsX = HHBUI::UIWinApi::ToList.CapsdpiX / USER_DEFAULT_SCREEN_DPI;
	::ReleaseDC(NULL, hdcMeasure);
	if (Scaledpi != 0)
		logPixelsX = Scaledpi;
	if (logPixelsX >= 2)
		logPixelsX = 1.25;
	/*取模，以消除随机缩放比例导致的渲染误差*/
	const auto fmod = std::fmodf(logPixelsX, 0.25f);
	if (fmod != 0.f)
		logPixelsX -= fmod;
}
HRESULT HHBUI::UIEngine::Init(info_Init* info)
{
	return_if_false(UIWinApi::ToList.engine_instance == 0, {}, S_FALSE);
	throw_if_failed(CoInitialize(nullptr), L"COM初始化失败");
	UIWinApi::ToList.dwMajorVersion = IsWin10System();
	if (UIWinApi::ToList.dwMajorVersion)
		SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);//win7不支持
	else
		SetProcessDPIAware();
	try
	{
		int device = -1;
		HINSTANCE hInstance = NULL;
		BOOL default_font = FALSE;
		//获取系统默认字体
		UIWinApi::ToList.drawing_default_fontLogFont = new LOGFONTW();
		INT default_font_Size = 14; FLOAT logPixelsY = 0.f;
		SystemParametersInfoW(SPI_GETICONTITLELOGFONT, sizeof(LOGFONTW), UIWinApi::ToList.drawing_default_fontLogFont, false);
		if (info)
		{
			device = info->device;
			DpiManager(info->dwScaledpi, logPixelsY, UIWinApi::ToList.drawing_default_dpi);

			//如果提供了默认字体信息就使用
			if (info->default_font_Face != NULL || info->default_font_Size != 0 || info->default_font_Style != 0)
			{
				if (info->default_font_Face == NULL)
					info->default_font_Face = UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName;
				else
				{
					size_t i = lstrlenW(info->default_font_Face);
					if (i > 0)
					{
						RtlMoveMemory((LPVOID)UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName, info->default_font_Face, i * 2 + 2);
					}
				}

				if (info->default_font_Size == NULL)
					default_font_Size = 15;
				else
					default_font_Size = info->default_font_Size;

				default_font = TRUE;
			}
			if (info->hInstance)
				hInstance = info->hInstance;
			UIWinApi::ToList.dwDebug = info->dwDebug;
		}
		else
		{
			UIWinApi::ToList.dwDebug = FALSE;
			DpiManager(0, logPixelsY, UIWinApi::ToList.drawing_default_dpi);
		}
		UIWinApi::ToList.drawing_default_fontLogFont->lfHeight = -UIEngine::fScale(default_font_Size);

		if (!hInstance)
			hInstance = GetModuleHandleW(NULL);
		if (FAILED(UIWinApi::Init(hInstance)))
			return S_FALSE;
		if (FAILED(UIDrawContext::Init(device)))
			return S_FALSE;
	
		UIWinApi::ToList.default_font = new UIFont();
		if (hInstance != NULL)
		{
			TCHAR szFilePath[MAX_PATH + 1];
			GetModuleFileName(hInstance, szFilePath, MAX_PATH); //文件路径;文件可以是*.exe、*.dll、*.ico
			UIWinApi::ToList.hIcon = ExtractIconW(hInstance, szFilePath, 0);
			UIWinApi::ToList.hIconsm = ExtractIconW(hInstance, szFilePath, 0);
			UIWinApi::ToList.engine_instance = hInstance;
		}
	
		UIWnd::RegClass(L"Hhbui.WindowClass.UI", 0, 0);

		return S_OK;
	}
	catch_default({});

}
HRESULT HHBUI::UIEngine::UnInit()
{
	handle_if_false(UIWinApi::ToList.engine_instance != NULL, EE_NOREADY, L"引擎尚未初始化");
	try
	{
		delete UIWinApi::ToList.default_font;
		UIWinApi::UnInit();
		UIDrawContext::UnInit();
		CoUninitialize();
		UIWinApi::ToList.engine_instance = NULL;
		if (UIWinApi::ToList.hIcon)
			DestroyIcon(UIWinApi::ToList.hIcon);
		if (UIWinApi::ToList.hIconsm)
			DestroyIcon(UIWinApi::ToList.hIconsm);
		return S_OK;
	}
	catch_default({});
}


BOOL HHBUI::UIEngine::QueryDebug()
{
	return UIWinApi::ToList.dwDebug;
}
BOOL TOAPI HHBUI::UIEngine::QueryInit()
{
	return UIWinApi::ToList.engine_instance != 0;
}
FLOAT TOAPI HHBUI::UIEngine::fScale(FLOAT n)
{
	if (UIWinApi::ToList.drawing_default_dpi > 1.f) {
		n = round(n * UIWinApi::ToList.drawing_default_dpi);
	}
	return n;
}
FLOAT TOAPI HHBUI::UIEngine::GetDefaultScale()
{
	return UIWinApi::ToList.drawing_default_dpi;
}
FLOAT TOAPI HHBUI::UIEngine::GetTime()
{
	static auto start_time = steady_clock::now();
	auto now = steady_clock::now();
	auto elapsed = duration_cast<duration<float>>(now - start_time);
	return elapsed.count();
}
LPCWSTR TOAPI HHBUI::UIEngine::GetVersion()
{
	return HHBUI_VERSION;
}