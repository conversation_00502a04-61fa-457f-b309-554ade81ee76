﻿#pragma once
namespace HHBUI
{
	enum ObjSegmentedEvent {
		WMM_SMT_ITEMCHANGED = -161,		//选中项被改变（wParam为选中项索引）
	};

	/*
	* 颜色设置：
	* color_text_normal = 常规文本颜色
	* color_text_hover = 选中项文本颜色
	* color_text_ban = 禁用项文本颜色
	* color_text_shadow = 选中项背景颜色
	* color_focus = 点燃项背景颜色
	* color_background = 控件背景颜色
	*/
	class TOAPI UISegmented : public UIControl
	{
	public:
		UISegmented() = default;
		UISegmented(UIBase* hParent, INT x, INT y, INT w, INT h, INT nID = 0);

		/**
		 * @brief 添加选择项（项目背景圆角度跟随控件圆角度）
		 * @param lpText 项名
		 * @param icon 图标（图像请勿自行销毁）
		 * @param nIndex 添加位置，<0则添加到末尾（默认）
		 * @return 成功返回添加项的索引，失败返回-1
		 */
		INT AddItem(LPCWSTR title, UIImage* icon = nullptr, INT nIndex = -1);
		//设置选择项名
		BOOL SetItemTitle(INT nIndex, LPCWSTR title);
		//获取选择项名
		LPCWSTR GetItemTitle(INT nIndex);
		//设置选择项图标（图像请勿自行销毁）
		BOOL SetItemIcon(INT nIndex, UIImage* icon);
		//获取选择项图标（图标不存在则返回空指针；使用完需手动释放）
		UIImage* GetItemIcon(INT nIndex);
		//设置选择项禁用
		BOOL SetItemBan(INT nIndex, BOOL ban);
		//获取选择项是否禁用
		BOOL GetItemIsBan(INT nIndex);
		//删除选择项
		BOOL DelItem(INT nIndex);
		//清空选择项
		void ClearItems();

		//设置选择项（使用此命令将无视选择项禁用）
		BOOL SetItemSelected(INT nIndex);
		//获取选择项（索引从0开始，当前无选中返回-1）
        INT GetItemSelected();
		//获取项目数量
		INT GetItemCount();
		//判断选择项是否存在
		BOOL GetItemExist(INT nIndex);
		
		//设置选择项宽度（仅显示模式为2（固定）时生效）（横向时为宽度，竖向时为高度）
		void SetItemWidth(INT w);
		/**
		 * @brief 设置显示模式
		 * @param mode 0=自适应（默认），1=填充，2=固定
		 */
		void SetDisplayMode(INT mode = 0);
		/**
		 * @brief 设置图标位置（图片图标无法随标题改变颜色，效果可能不佳）
		 * @param pos 0=不显示，1=上方，2=左侧
		 */
		void SetIconPos(INT pos = 0);
		//设置是否横向排列（true=横向，false=竖向）
		void SetIsHorizontal(BOOL fHorizontal = true);

		//更新
		void Update();

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;

		void calu_data();

		struct item_info {
			LPCWSTR title = nullptr;
            UIImage* icon = nullptr;
            BOOL ban = false;

			ExRectF rc{  };
			ExRectF irc{  };
			ExRectF trc{  };
		};

		struct p_info {
			int w = 0, h = 0, iw = 60;
			int ihov = -1, isel = -1;	//热点项、选中项

			bool fhor = true;			//是否横向

			int dmode = 0;				//显示模式（0=自适应，1=填充，2=固定）
			int ipos = 0;				//图标位置（0=不显示，1=上方，2=左侧）

			std::vector<item_info> list;

			UIBrush* brGen = nullptr;
		}p_data;
	};
}
