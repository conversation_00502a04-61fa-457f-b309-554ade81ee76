﻿#pragma once
namespace HHBUI
{
	/// 区域合并模式
	EXENUM(RegionCombineMode)
	{
		Copy = 0,				///< 区域合并模式：复制
			Union = 1,				///< 区域合并模式：合并
			Intersect = 2,			///< 区域合并模式：相交
			Exclude = 3,			///< 区域合并模式：排除
			Xor = 4,				///< 区域合并模式：异或
	};
	class TOAPI UIRegion
	{
	public:
		//创建空白区域
		UIRegion();
		//创建矩形或椭圆区域
		UIRegion(FLOAT left, FLOAT top, FLOAT right, FLOAT bottom, BOOL is_Ellipse);
		//创建圆形区域
		UIRegion(FLOAT left, FLOAT top, FLOAT right, FLOAT bottom, FLOAT radius_left_top, FLOAT radius_right_top,
			FLOAT radius_right_bottom, FLOAT radius_left_bottom);
		UIRegion(FLOAT left, FLOAT top, FLOAT right, FLOAT bottom, FLOAT radiusX, FLOAT radiusY, BOOL is_Radius);
		//创建路径区域
		UIRegion(UIPath *hPath, const ExMatrix* tranform = nullptr);
		//创建自区域
		UIRegion(UIRegion *region, const ExMatrix* tranform = nullptr);
		~UIRegion();


		HRESULT CombineWithRect(float left, float top, float right, float bottom,
			RegionCombineMode mode, const ExMatrix* tranform = nullptr);
		HRESULT CombineWithPath(UIPath *path, RegionCombineMode mode,
			const ExMatrix* tranform = nullptr);
		HRESULT CombineWithRegion(UIRegion *region, RegionCombineMode mode,
			const ExMatrix* tranform = nullptr);

		HRESULT HitTest(float x, float y) const;
		HRESULT GetBounds(ExRectF* r_bounds_rect) const;
		//取描述
		LPVOID GetContext() const;
	private:
		static ID2D1Geometry* Combine(ID2D1Geometry* geometry1, ID2D1Geometry* geometry2,
			RegionCombineMode mode, const ExMatrix* tranform_matrix);
		ID2D1Geometry* m_geometry = nullptr;
		ExRectF m_size{};
		friend class UICanvas;
		friend class UIWnd;
	};

}

