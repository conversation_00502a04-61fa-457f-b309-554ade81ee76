﻿#pragma once
namespace HHBUI
{
	/// 字体风格
	EXENUM(FontStyle)
	{
	     	Normal = 0x0000,			///< 字体风格：正常
			Bold = 0x0001,				///< 字体风格：粗体
			Italic = 0x0002,			///< 字体风格：斜体
			UnderLine = 0x0004,			///< 字体风格：下划线
			StrikeOut = 0x0008,			///< 字体风格：删除线

			CustomWeight = 0x0100,		///< 字体风格：自定义粗细 <此时style高16位表示粗细,取值一般在0-1000,可参考FW_开头的常量>
	};
	class TOAPI UIFont
	{
	public:
		//创建默认字体
		UIFont(LOGFONTW* info = nullptr);
		//创建字体自字体族
		UIFont(LPCWSTR lpwzFontFace, INT dwFontSize = 0, DWORD dwFontStyle = FontStyle::Normal);
		~UIFont();
		//加载字体文件自内存
		static BOOL LoadFromMem(LPVOID data, size_t size, LPCWSTR lpwzFontFace);
		//字体取描述表
		LPVOID GetContext(INT index);
		//字体取尺寸
		INT GetSize();
		//获取逻辑字体
		BOOL GetLogFont(LOGFONTW* lpLogFont);
		//获取默认逻辑字体
		BOOL GetDefaultLogFontA(LOGFONTA* lpLogFont);
		BOOL GetDefaultLogFontW(LOGFONTW* lpLogFont);
	protected:
		LPVOID m_context = nullptr;
	};
}

