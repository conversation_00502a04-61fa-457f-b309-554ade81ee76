﻿#include "hhbui.h"
#include "control/page.h"

using namespace HHBUI;
LRESULT CALLBACK OnButton_table_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	if (nCode == WMM_CLICK)
	{
		auto table = (UITable*)window->FindUIView(L"2000");
		if (nID == 2001)
		{
			INT Index = table->GetSelect();
			table->DeleteItem(Index);
		}
		else if (nID == 2002)
		{
			table->DeleteAllItem();
		}
		else if (nID == 2003)
		{
			table->DeleteColumn(table->GetColumnCount());
		}
		else if (nID == 2004)
		{
			table->DeleteAllColumn();
		}
		else if (nID == 2005)
		{
			table->SetErtInfo(3, L"我是修改的标题", 160, 0, -1, UIColor(0, 108, 190, 255));
			table->Update();
		}
		else if (nID == 2006)
		{
			INT Index = table->GetSelect();
			table->SetItem(Index, 3, L"我是修改的标题", UIColor(0, 108, 190, 255));
			table->Redraw();
		}
		else if (nID == 2007)
		{
			tableItem* item = nullptr; BOOL fCheck = 0;
			INT Index = table->GetSelect();
			table->GetItem(Index, 4, &item);
			table->GetItemCheck(Index, fCheck);
			if (item)
				output(item->wzText, L"是否选中：", fCheck);
		}
		else if (nID == 2008)
		{
			INT iRow_start = 0, iRow_end = 0, iCol_start = 0, iCol_end = 0;
			table->GetSelectIndex(iRow_start, iRow_end, iCol_start, iCol_end);
			table->SetMergeIndex(iRow_start, iRow_end, iCol_start, iCol_end);
			table->Redraw();
			if (iRow_start == -1 && iCol_start == -1)
			{
				window->PopupToast(L"至少选中两个项目才能合并哦！", Toast_warning | Toast_type_center);
			}
		}
		else if (nID == 2009)
		{
			INT iRow_start = 0, iRow_end = 0, iCol_start = 0, iCol_end = 0;
			table->GetSelectIndex(iRow_start, iRow_end, iCol_start, iCol_end);
			table->SetDecomPoseIndex(iRow_start, iRow_end, iCol_start, iCol_end);
			table->Redraw();
			if (iRow_start == -1 && iCol_start == -1)
			{
				window->PopupToast(L"至少选中两个项目才能分解哦！", Toast_warning | Toast_type_center);
			}
		}
		else if (nID == 2010)
		{
			table->SetScrollPos(FALSE, 0, TRUE);
			table->SetSelectIndex(1, 2, 3, 4);
			table->Update();
		}
		else if (nID == 2011)
		{
			INT iRow_start = 0, iRow_end = 0, iCol_start = 0, iCol_end = 0;
			table->GetSelectIndex(iRow_start, iRow_end, iCol_start, iCol_end);
			output(L"iRow_start:", iRow_start, L"iRow_end:", iRow_end, L"iCol_start:", iCol_start, L"iCol_end:", iCol_end);
		}
		// 分页控制相关
		else if (nID == 2020)
		{
			// 启用/禁用分页
			auto pageControl = table->GetPageControl();
			BOOL isPaging = (pageControl != nullptr && pageControl->IsVisible());
			table->EnablePaging(!isPaging, 10);
			
			// 更新按钮文本
			auto btn = (UIButton*)UIView;
			btn->SetText(isPaging ? L"启用分页" : L"禁用分页");
		}
		else if (nID == 2021)
		{
			// 转到第一页
			table->GoToPage(1);
		}
		else if (nID == 2022)
		{
			// 转到最后一页
			table->GoToPage(table->GetTotalPages());
		}
		else if (nID == 2023)
		{
			// 设置每页条数为5
			table->EnablePaging(TRUE, 5);
		}
		else if (nID == 2024)
		{
			// 设置每页条数为10
			table->EnablePaging(TRUE, 10);
		}
		else if (nID == 2025)
		{
			// 设置每页条数为20
			table->EnablePaging(TRUE, 20);
		}
	}
	else if (nCode == WMM_RLVN_CHECK)
	{
		output(wParam, lParam);
	}
	else if (nCode == WMM_RLVN_PAGE_CHANGE)
	{
		// 页码改变事件
		output(L"页码改变:", wParam);
	}
	return S_OK;
}
void testtable(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 900, 530, L"hello Table", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto table = new UITable(window, 50, 50, 600, 400,
		eos_scroll_h | eos_scroll_v | eos_table_drawhorizontalline | eos_table_drawverticalline | eos_table_allowediting | eos_table_allowmultiple, 0, 2000, Middle | Left);
	table->SetColor(color_border, UIColor(194, 195, 201, 255));
	table->SetColor(color_background, UIColor(L"#ebefed"));
	table->SetEvent(WMM_RLVN_CHECK, OnButton_table_Event);
	table->SetEvent(WMM_RLVN_PAGE_CHANGE, OnButton_table_Event);
	table->SetRadius(8, 8, 8, 8);
	table->InsErtInfo(0, NULL, 50, cs_table_checkbox);
	table->InsErtInfo(0, L"索引", 80, cs_table_clickable | cs_table_sortable_number);

	std::wstring xml = LR"(<svg t="1714193884357" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3959" width="20" height="20"><path d="M753.664 257.536a2.122105 2.122105 0 0 0-0.161684 2.546526 3.112421 3.112421 0 0 0 2.586947 1.428211c63.770947 1.569684 127.703579 2.108632 191.797895 1.616842 26.206316-0.269474 42.509474 2.223158 52.88421 26.543158a105.498947 105.498947 0 0 1 8.353685 40.892631c0.175158 43.52-0.451368 79.447579-1.886316 107.789474-1.212632 24.454737-43.115789 25.465263-60.631579 25.6a3.267368 3.267368 0 0 0-3.233684 2.896842 57181.864421 57181.864421 0 0 0-67.301053 522.307369c-2.202947 17.92-13.568 26.967579-34.088421 27.149473-222.315789 1.751579-444.631579 2.694737-666.947368 2.829474-24.724211 0.067368-28.766316-18.795789-31.730527-40.151579a226552.407579 226552.407579 0 0 1-71.612631-516.715789c-0.363789-2.56-1.818947-3.866947-4.378948-3.907369-42.576842-1.010526-50.324211 3.098947-51.267368-43.722105-1.212632-58.118737-1.239579-95.124211-0.067369-111.023158 2.357895-31.056842 24.791579-40.286316 54.096843-40.690526 56.407579-0.808421 112.727579-1.886316 168.96-3.233685 4.581053-0.094316 7.834947-0.808421 9.768421-2.155789 3.860211-2.694737 4.176842-5.726316 0.943158-9.094737a830.181053 830.181053 0 0 1-53.35579-61.978947c-13.608421-17.475368-13.608421-34.741895 0-51.806316a2834.607158 2834.607158 0 0 1 74.172632-89.128421c23.646316-27.216842 64.336842-49.246316 98.155789-28.564211 3.590737 2.196211 14.504421 11.654737 32.741053 28.362106a1317.133474 1317.133474 0 0 1 98.425263 100.109473 4.985263 4.985263 0 0 0 7.545263-0.067368c29.642105-34.088421 60.719158-66.519579 93.237895-97.28C623.925895 26.071579 642.250105 12.8 655.642947 8.272842c44.530526-15.023158 89.397895 16.64 115.738948 52.816842a1161.930105 1161.930105 0 0 1 62.517894 95.191579 10.374737 10.374737 0 0 1-1.212631 12.193684l-79.023158 89.061053z m-197.254737-68.109474a8.96 8.96 0 0 0 0.471579 13.136842l22.837895 19.806316c3.368421 2.910316 8.353684 2.910316 11.722105 0l125.372632-107.385263a8.96 8.96 0 0 0 0.269473-13.338947l-31.595789-29.170527a8.96 8.96 0 0 0-12.39579 0.202106l-116.682105 116.749473z m-136.690526 27.351579a14.551579 14.551579 0 0 0 18.930526-0.336842l11.654737-10.307368a14.551579 14.551579 0 0 0 0.673684-21.086316l-101.591579-102.467368a14.551579 14.551579 0 0 0-20.412631-0.202106l-21.086316 20.412632a14.551579 14.551579 0 0 0 0.808421 21.625263l111.023158 92.362105z m335.205052-60.227368a5.187368 5.187368 0 0 0-7.329684-0.303158L670.336 227.301053a5.187368 5.187368 0 0 0-0.309895 7.329684l0.181895 0.202105a5.187368 5.187368 0 0 0 7.329684 0.303158l77.258105-71.046737a5.187368 5.187368 0 0 0 0.309895-7.329684l-0.181895-0.202105z m-407.44421 78.517895a6.4 6.4 0 0 0-0.754526-9.020632L274.378105 164.917895a6.4 6.4 0 0 0-9.020631 0.761263l-0.168421 0.202105a6.4 6.4 0 0 0 0.754526 9.020632l72.346947 61.136842a6.4 6.4 0 0 0 9.020632-0.754526l0.175158-0.202106z m196.264421 89.835789a3.570526 3.570526 0 0 0-3.577263-3.557053l-68.71579 0.23579a3.570526 3.570526 0 0 0-3.563789 3.584l0.262737 73.835789a3.570526 3.570526 0 0 0 3.584 3.557053l68.715789-0.235789a3.570526 3.570526 0 0 0 3.557053-3.584l-0.262737-73.83579z m385.212632 73.970526a6.467368 6.467368 0 0 0 6.4-6.4l0.875789-57.532631a6.474105 6.474105 0 0 0-6.332632-6.602105l-310.16421-5.658948a6.467368 6.467368 0 0 0-6.602105 6.467369l-0.202106 68.311579a6.467368 6.467368 0 0 0 6.602106 6.467368l309.423158-5.052632z m-529.987369-65.347368a4.917895 4.917895 0 0 0-4.917895-4.917895h-307.334736a4.917895 4.917895 0 0 0-4.917895 4.917895v62.248421c0 2.714947 2.196211 4.917895 4.917895 4.917895h307.334736a4.917895 4.917895 0 0 0 4.917895-4.917895v-62.248421z m3.772632 621.810526l-0.87579-486.602105a4.513684 4.513684 0 0 0-4.446316-4.513684l-256.067368-0.808421c-2.337684 0-3.348211 1.165474-3.031579 3.503158l67.974737 485.793684c0.229053 1.556211 1.549474 2.708211 3.098947 2.694737l177.785263-0.067369c2.290526 0 4.466526 0.512 6.534737 1.549474 2.155789 1.077895 4.217263 1.569684 6.197895 1.482105 1.886316-0.134737 2.829474-1.145263 2.829474-3.031579z m325.254737-2.357894l86.231579 0.808421a2.56 2.56 0 0 0 2.56-2.290527l62.181052-483.098947a3.813053 3.813053 0 0 0-0.936421-2.937263 3.671579 3.671579 0 0 0-2.768842-1.246316l-94.046316 1.077895-167.208421-1.077895a2.802526 2.802526 0 0 0-2.021052 0.808421 2.930526 2.930526 0 0 0-0.87579 2.021053l-1.212632 479.191579c-0.047158 4.810105 2.357895 7.208421 7.208422 7.208421l110.888421-0.471579z m-182.770527-482.896843c0-2.492632-2.021053-4.513684-4.513684-4.513684h-70.332632a4.513684 4.513684 0 0 0-4.513684 4.513684v480.741053a4.513684 4.513684 0 0 0 4.513684 4.513684h70.332632c2.492632 0 4.513684-2.021053 4.513684-4.513684v-480.741053z" fill="#343333" p-id="3960"></path><path d="M556.409263 189.426526l116.682105-116.749473a8.96 8.96 0 0 1 12.39579-0.202106l31.595789 29.170527a8.96 8.96 0 0 1-0.269473 13.338947l-125.372632 107.385263a8.96 8.96 0 0 1-11.722105 0l-22.837895-19.806316a8.96 8.96 0 0 1-0.471579-13.136842zM419.718737 216.778105l-111.023158-92.362105a14.551579 14.551579 0 0 1-0.808421-21.625263l21.086316-20.412632a14.551579 14.551579 0 0 1 20.412631 0.202106l101.591579 102.467368a14.551579 14.551579 0 0 1-0.673684 21.086316l-11.654737 10.307368a14.551579 14.551579 0 0 1-18.930526 0.336842z" fill="#FCC556" p-id="3961"></path><path d="M666.518076 230.807859m3.818407-3.511205l77.26075-71.044902q3.818407-3.511205 7.329612 0.307202l0.1824 0.198359q3.511205 3.818407-0.307202 7.329612l-77.26075 71.044902q-3.818407 3.511205-7.329612-0.307202l-0.1824-0.198359q-3.511205-3.818407 0.307202-7.329612Z" fill="#FCC556" p-id="3962"></path><path d="M269.493906 160.783936m4.888294 4.130929l72.34676 61.137752q4.888295 4.130929 0.757366 9.019224l-0.173934 0.205823q-4.130929 4.888295-9.019224 0.757366l-72.34676-61.137753q-4.888295-4.130929-0.757365-9.019223l0.173934-0.205823q4.130929-4.888295 9.019223-0.757366Z" fill="#FCC556" p-id="3963"></path><path d="M467.880795 321.60125m3.570504-0.012464l68.715371-0.239863q3.570505-0.012463 3.582968 3.558041l0.257735 73.83534q0.012463 3.570505-3.558041 3.582968l-68.715371 0.239863q-3.570505 0.012463-3.582968-3.558041l-0.257735-73.83534q-0.012463-3.570505 3.558041-3.582968Z" fill="#FCC556" p-id="3964"></path><path d="M928.956632 398.874947l-309.423158 5.052632a6.467368 6.467368 0 0 1-6.602106-6.467368l0.202106-68.311579a6.467368 6.467368 0 0 1 6.602105-6.467369l310.16421 5.658948a6.467368 6.467368 0 0 1 6.332632 6.602105l-0.875789 57.532631a6.467368 6.467368 0 0 1-6.4 6.4z" fill="#FF5646" p-id="3965"></path><path d="M81.798737 328.609684m4.917895 0l307.334736 0q4.917895 0 4.917895 4.917895l0 62.248421q0 4.917895-4.917895 4.917895l-307.334736 0q-4.917895 0-4.917895-4.917895l0-62.248421q0-4.917895 4.917895-4.917895Z" fill="#FF5646" p-id="3966"></path><path d="M399.912421 958.369684c-1.980632 0.087579-4.042105-0.404211-6.197895-1.482105a14.416842 14.416842 0 0 0-6.534737-1.549474l-177.785263 0.067369a3.132632 3.132632 0 0 1-3.098947-2.694737l-67.974737-485.793684c-0.316632-2.337684 0.693895-3.503158 3.031579-3.503158l256.067368 0.808421a4.513684 4.513684 0 0 1 4.446316 4.513684l0.87579 486.602105c0 1.886316-0.943158 2.896842-2.829474 3.031579zM781.217684 465.300211c-6.292211 31.393684-11.856842 70.689684-16.707368 117.894736a40728.481684 40728.481684 0 0 0-36.513684 369.785264l-110.888421 0.471578c-4.850526 0-7.255579-2.405053-7.208422-7.208421l1.212632-479.191579a2.930526 2.930526 0 0 1 0.87579-2.027789 2.802526 2.802526 0 0 1 2.021052-0.808421l167.208421 1.077895z" fill="#FEFEFE" p-id="3967"></path><path d="M727.996632 952.980211a40728.481684 40728.481684 0 0 1 36.513684-369.785264c4.850526-47.205053 10.415158-86.501053 16.707368-117.894736l94.046316-1.077895a3.671579 3.671579 0 0 1 2.768842 1.239579 3.813053 3.813053 0 0 1 0.936421 2.937263l-62.181052 483.098947a2.56 2.56 0 0 1-2.56 2.290527l-86.231579-0.808421z" fill="#CBCCCE" p-id="3968"></path><path d="M465.866105 465.569684m4.513684 0l70.332632 0q4.513684 0 4.513684 4.513684l0 480.741053q0 4.513684-4.513684 4.513684l-70.332632 0q-4.513684 0-4.513684-4.513684l0-480.741053q0-4.513684 4.513684-4.513684Z" fill="#FF5646" p-id="3969"></path></svg>)";

	table->InsErtInfo(0, L"标题", 100, cs_table_clickable, -1, {}, {}, {}, new UIImage(xml.c_str(), TRUE, 20, 20));
	table->InsErtInfo(0, L"可排序(字母)", 120, cs_table_clickable | cs_table_sortable_ab);
	table->InsErtInfo(0, L"可排序(数值)", 120, cs_table_clickable | cs_table_sortable_number);
	table->InsErtInfo(0, L"其它", 100, cs_table_clickable);
	for (INT i = 1; i <= 200; i++)
	{
		auto iRow = table->InsErtItem(0, (i % 3 == 0 ? rs_table_checkboxok : rs_table_checkbox));
		table->SetItem(iRow, 2, std::to_wstring(i).c_str());
		table->SetItem(iRow, 3, L"项目标题");
		table->SetItem(iRow, 4, generate_random_letters(8).c_str());
		table->SetItem(iRow, 5, std::to_wstring(randomf(1, 1500)).c_str());
	}

	table->Update();

	auto btn1 = new UIButton(window, 670, 50, 100, 30, L"删除选中", 0, 0, 2001);
	btn1->SetStyle(fill, primary);
	btn1->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn2 = new UIButton(window, 670, 100, 100, 30, L"删除全部", 0, 0, 2002);
	btn2->SetStyle(fill, primary);
	btn2->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn3 = new UIButton(window, 670, 150, 100, 30, L"删除一列", 0, 0, 2003);
	btn3->SetStyle(fill, primary);
	btn3->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn4 = new UIButton(window, 670, 200, 100, 30, L"删除全部列", 0, 0, 2004);
	btn4->SetStyle(fill, primary);
	btn4->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn5 = new UIButton(window, 670, 250, 100, 30, L"置列标题", 0, 0, 2005);
	btn5->SetStyle(fill, primary);
	btn5->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn6 = new UIButton(window, 670, 300, 100, 30, L"置选中项目", 0, 0, 2006);
	btn6->SetStyle(fill, primary);
	btn6->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn7 = new UIButton(window, 670, 350, 100, 30, L"取选中项目", 0, 0, 2007);
	btn7->SetStyle(fill, primary);
	btn7->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn8 = new UIButton(window, 670, 400, 100, 30, L"合并单元格", 0, 0, 2008);
	btn8->SetStyle(fill, primary);
	btn8->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn9 = new UIButton(window, 670, 450, 100, 30, L"分解单元格", 0, 0, 2009);
	btn9->SetStyle(fill, primary);
	btn9->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn10 = new UIButton(window, 780, 50, 100, 30, L"多选单元格", 0, 0, 2010);
	btn10->SetStyle(fill, primary);
	btn10->SetEvent(WMM_CLICK, OnButton_table_Event);
	auto btn11 = new UIButton(window, 780, 100, 100, 30, L"取多选索引", 0, 0, 2011);
	btn11->SetStyle(fill, primary);
	btn11->SetEvent(WMM_CLICK, OnButton_table_Event);

	// 添加分页功能相关的按钮
	auto btnPaging = new UIButton(window, 780, 150, 100, 30, L"启用分页", 0, 0, 2020);
	btnPaging->SetStyle(fill, primary);
	btnPaging->SetEvent(WMM_CLICK, OnButton_table_Event);
	
	auto btnFirstPage = new UIButton(window, 780, 200, 100, 30, L"第一页", 0, 0, 2021);
	btnFirstPage->SetStyle(fill, primary);
	btnFirstPage->SetEvent(WMM_CLICK, OnButton_table_Event);
	
	auto btnLastPage = new UIButton(window, 780, 250, 100, 30, L"最后一页", 0, 0, 2022);
	btnLastPage->SetStyle(fill, primary);
	btnLastPage->SetEvent(WMM_CLICK, OnButton_table_Event);
	
	auto btnPageSize5 = new UIButton(window, 780, 300, 100, 30, L"每页5条", 0, 0, 2023);
	btnPageSize5->SetStyle(fill, primary);
	btnPageSize5->SetEvent(WMM_CLICK, OnButton_table_Event);
	
	auto btnPageSize10 = new UIButton(window, 780, 350, 100, 30, L"每页10条", 0, 0, 2024);
	btnPageSize10->SetStyle(fill, primary);
	btnPageSize10->SetEvent(WMM_CLICK, OnButton_table_Event);
	
	auto btnPageSize20 = new UIButton(window, 780, 400, 100, 30, L"每页20条", 0, 0, 2025);
	btnPageSize20->SetStyle(fill, primary);
	btnPageSize20->SetEvent(WMM_CLICK, OnButton_table_Event);

	window->Show();
	//window->MessageLoop();
}