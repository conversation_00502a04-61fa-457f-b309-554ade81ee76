﻿#include "hhbui.h"

using namespace HHBUI;

LRESULT CALLBACK OnButton_free_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (nCode == WMM_LUP)//一定要用弹起消息
	{
		if (nID == 1001)
		{
			delete obj;
			return S_FALSE;
		}
		else if (nID == 1002)
		{
			delete window;
			return S_FALSE;
		}
	}
	return S_OK;
}
//控件消息回调
LRESULT CALLBACK OnViewMsg_free_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (uMsg == WM_LBUTTONUP)//一定要用弹起消息
	{
		if (nID == 1003)
		{
			delete obj;
			return S_FALSE;
		}
		else if (nID == 1004)
		{
			delete window;
			return S_FALSE;
		}
	}

	return S_OK;
}
void testfree(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 700, 300, L"hello free", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);

	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto btn1 = new UIButton(window->GetInstance(), 20, 70, 120, 70, L"事件方式销毁", 0, 0, 1001);
	btn1->SetEvent(WMM_LUP, OnButton_free_Event);

	auto btn2 = new UIButton(window->GetInstance(), 200, 70, 120, 70, L"事件方式\n销毁窗口", 0, 0, 1002);
	btn2->SetEvent(WMM_LUP, OnButton_free_Event);

	auto btn3 = new UIButton(window->GetInstance(), 380, 70, 120, 70, L"回调方式销毁", 0, 0, 1003);
	btn3->SetMsgProc(OnViewMsg_free_Proc);

	auto btn4 = new UIButton(window->GetInstance(), 560, 70, 120, 70, L"回调方式\n销毁窗口", 0, 0, 1004);
	btn4->SetMsgProc(OnViewMsg_free_Proc);
	window->Show();
	//window->MessageLoop();
}