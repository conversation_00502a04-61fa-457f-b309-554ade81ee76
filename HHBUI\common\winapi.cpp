﻿#include "pch.h"
#include "winapi.h"
#include "common/Exception.h"
#ifndef ASSERT
#define ASSERT(expr)  _ASSERTE(expr)
#endif
namespace HHBUI
{
	HRESULT UIWinApi::Init(HINSTANCE hInstance)
	{
		ToList.ntdll = LoadLibraryW(L"ntdll.dll");
		handle_if_false(ToList.ntdll, EE_LOST_NECESSARY, L"ntdll.dll加载失败");

		ToList.RtlComputeCrc32 = (_RtlComputeCrc32Proc)GetProcAddress(ToList.ntdll, "RtlComputeCrc32");
		handle_if_false(ToList.RtlComputeCrc32, EE_LOST_NECESSARY, L"RtlComputeCrc32加载失败");

		ToList.user32 = LoadLibraryW(L"user32.dll");
		handle_if_false(ToList.user32, EE_LOST_NECESSARY, L"user32.dll加载失败");

		ToList.Msftedit = LoadLibraryW(L"Msftedit.dll");
		handle_if_false(ToList.Msftedit, EE_LOST_NECESSARY, L"Msftedit.dll加载失败");

		//尝试获取UpdateLayeredWindowIndirect函数
		ToList.UpdateLayeredWindowIndirect = (_UpdateLayeredWindowIndirectProc)GetProcAddress(ToList.user32, "UpdateLayeredWindowIndirect");

		ToList.dwMessage = RegisterWindowMessageW(L"HHBUI_WNDCLASS");
		ToList.hHookMsgBox = SetWindowsHookExW(WH_CBT, (HOOKPROC)HHBUI::UIhook::hook_api_proc, hInstance, GetCurrentThreadId());
		ToList.hMenuEdit = LoadMenuW(ToList.user32, MAKEINTRESOURCEW(1));
		ToList.hMenuVS = LoadMenuW(ToList.user32, MAKEINTRESOURCE(80));
		ToList.hMenuHS = LoadMenuW(ToList.user32, MAKEINTRESOURCE(64));
		return S_OK;
	}
	void UIWinApi::UnInit()
	{
		UnhookWindowsHookEx(ToList.hHookMsgBox);
		SAFE_FREE(ToList.user32, FreeLibrary);
		SAFE_FREE(ToList.ntdll, FreeLibrary);
		SAFE_FREE(ToList.Msftedit, FreeLibrary);
		DestroyMenu(ToList.hMenuEdit);
		DestroyMenu(ToList.hMenuVS);
		DestroyMenu(ToList.hMenuHS);
		ToList.RtlComputeCrc32 = nullptr;
		ToList.UpdateLayeredWindowIndirect = nullptr;
	}
	void UIWinApi::GetWndScreenRect(HWND hWnd, RECT& rcMonitor, RECT& rcDesk)
	{
		ASSERT(::IsWindow(hWnd));
		ASSERT((GetWindowStyle(hWnd) & WS_CHILD) == 0);

		HWND hWndCenter = ::GetWindowOwner(hWnd);
		if (hWndCenter != NULL)
			hWnd = hWndCenter;

		MONITORINFO oMonitor = {};
		oMonitor.cbSize = sizeof(oMonitor);
		::GetMonitorInfoW(::MonitorFromWindow(hWnd, MONITOR_DEFAULTTONEAREST), &oMonitor);
		rcDesk = oMonitor.rcWork;

		if (hWndCenter == NULL)
			rcMonitor = oMonitor.rcMonitor;
		else
			::GetWindowRect(hWndCenter, &rcMonitor);
	}
	void UIWinApi::GetWndScreenRectEx(HWND hWnd, RECT& rcMonitor, RECT& rcDesk)
	{
		HMONITOR hMonitor = MonitorFromWindow(hWnd, MONITOR_DEFAULTTOPRIMARY);
		if (hMonitor != 0)
		{
			MONITORINFO info = {};
			info.cbSize = sizeof(info);
			if (GetMonitorInfoW(hMonitor, &info))
			{
				rcMonitor = info.rcMonitor;
				rcDesk = info.rcWork;
			}
		}
	}
}

