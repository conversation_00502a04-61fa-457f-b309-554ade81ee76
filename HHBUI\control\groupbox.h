﻿#pragma once
namespace HHBUI
{
	class TOAPI UIGroupbox : public UIControl
	{
	public:
		UIGroupbox() = default;
		UIGroupbox(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
	
		//置边框颜色
		void SetCrBorder(UIColor normal);
		//置背景颜色
		void SetCrBkg(UIColor normal);
		//置线宽
		void SetStrokeWidth(FLOAT fWidth);
		//置圆角度
		void SetRadius(FLOAT radius);
		//置文本偏移
		void SetTextOffset(FLOAT Offset);


	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;

		struct groupbox_s
		{
			FLOAT radius = 0, textoffset = 0, strokewidth = 1.f;
			UIColor clr[2] = { UIColor(194,195,201,255),{} };
		}p_data;
	};
}
