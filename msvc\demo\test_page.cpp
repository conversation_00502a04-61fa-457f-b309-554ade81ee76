﻿#include "hhbui.h"
using namespace HHBUI;
UIPage *m_page[10];

void UpdatePageStyle(info_page_style style)
{
	for (INT i = 0; i < 10; i++)
	{
		m_page[i]->SetStyle(style);
		m_page[i]->Redraw();
	}
}
LRESULT CALLBACK OnPageEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	if (nCode == WMM_PAGE_SELCHANGER)
	{
		output(lParam);
	}
	else if (nCode == WMM_CHECK)
	{
		if (wParam == 1)
		{
			if (nID == 2000)
			{
				UpdatePageStyle(e_page_left);
			}
			else if (nID == 2001)
			{
				UpdatePageStyle(e_page_center);
			}
			else if (nID == 2002)
			{
				UpdatePageStyle(e_page_right);
			}
		}
	}
	return S_OK;
}
void testpage(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 940, 630, L"hello Page", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);
	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto bstatic = new UIStatic(window, 20, 50, 150, 32, L"设置方向：", 0, 0, 0, Middle | SingleLine);
	auto bCheck1 = new UICheck(window, 110, 55, 120, 20, L"靠左边", 2000);
	bCheck1->SetEvent(WMM_CHECK, OnPageEvent);
	bCheck1->SetRadio(TRUE);
	bCheck1->SetColor(color_text_normal, UIColor(0, 0, 0, 255));
	bCheck1->SetState(checked);

	auto bCheck2 = new UICheck(window, 190, 55, 120, 20, L"靠中间", 2001);
	bCheck2->SetEvent(WMM_CHECK, OnPageEvent);
	bCheck2->SetRadio(TRUE);
	bCheck2->SetColor(color_text_normal, UIColor(0, 0, 0, 255));
	auto bCheck3 = new UICheck(window, 280, 55, 120, 20, L"靠右边", 2002);
	bCheck3->SetEvent(WMM_CHECK, OnPageEvent);
	bCheck3->SetRadio(TRUE);
	bCheck3->SetColor(color_text_normal, UIColor(0, 0, 0, 255));

	auto bstatic1 = new UIStatic(window, 20, 100, 50, 32, L"默认：", 0, 0, 0, Middle | SingleLine);
	m_page[0] = new UIPage(window, 110, 100, 800, 32, L"", eos_paging | eos_paging_showtips | eos_paging_showjump | eos_paging_pnarrow);
	m_page[0]->SetCount(98785007, 20);
	m_page[0]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[0]->SetCurrent(4);

	auto bstatic2 = new UIStatic(window, 20, 150, 50, 24, L"DIY：", 0, 0, 0, Middle | SingleLine);
	m_page[1] = new UIPage(window, 110, 150, 800, 24, L"", eos_paging | eos_paging_showtips | eos_paging_showjump | eos_paging_pnatext);
	m_page[1]->SetCount(5000, 20);
	m_page[1]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[1]->SetItemText(L"回上一页", L"下一页更精彩", L"GO");
	m_page[1]->SetCurrent(20);
	m_page[1]->SetInterval(4);

	auto bstatic3 = new UIStatic(window, 20, 200, 150, 24, L"网易云：", 0, 0, 0, Middle | SingleLine);
	m_page[2] = new UIPage(window, 110, 200, 800, 24, L"", eos_paging);
	m_page[2]->SetCount(1000, 20);
	m_page[2]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[2]->SetCrbkg(UIColor(255, 255, 255, 255), UIColor(245, 245, 245, 255), UIColor(236, 65, 65, 255), UIColor(255, 255, 255, 255));
	m_page[2]->SetCrBorder(UIColor(229, 229, 229, 255), UIColor(220, 220, 220, 255), UIColor(255, 255, 255, 255), UIColor(229, 229, 229, 255));
	m_page[2]->SetCrText(UIColor(78, 78, 78, 255), UIColor(78, 78, 78, 255), UIColor(255, 255, 255, 255), UIColor(204, 204, 204, 255));
	m_page[2]->SetCrEllipsis(UIColor(65, 65, 65, 200), UIColor(24, 144, 255, 255));
	m_page[2]->SetCurrent(18);
	m_page[2]->SetInterval(4);

	auto bstatic4 = new UIStatic(window, 20, 250, 150, 40, L"喜马拉雅：", 0, 0, 0, Middle | SingleLine);
	m_page[3] = new UIPage(window, 110, 250, 800, 40, L"", eos_paging | eos_paging_showtips);
	m_page[3]->SetCount(1000, 20);
	m_page[3]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[3]->SetCrbkg(UIColor(255, 255, 255, 255), UIColor(248, 100, 66, 255), UIColor(248, 100, 66, 255), UIColor(255, 255, 255, 255));
	m_page[3]->SetCrBorder(UIColor(232, 232, 232, 255), UIColor(248, 100, 66, 255), UIColor(248, 100, 66, 255), UIColor(229, 229, 229, 255));
	m_page[3]->SetCrText(UIColor(163, 172, 201, 255), UIColor(255, 255, 255, 255), UIColor(255, 255, 255, 255), UIColor(204, 204, 204, 255));
	m_page[3]->SetCrEllipsis(UIColor(65, 65, 65, 200), UIColor(248, 100, 66, 255));
	m_page[3]->SetCurrent(8);
	m_page[3]->SetInterval(0);
	m_page[3]->SetBtnRound(0);

	auto bstatic5 = new UIStatic(window, 20, 300, 150, 36, L"百度风格：", 0, 0, 0, Middle | SingleLine);
	m_page[4] = new UIPage(window, 110, 300, 800, 36, L"", eos_paging | eos_paging_pnatext);
	m_page[4]->SetCount(1000, 20);
	m_page[4]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[4]->SetCrbkg(UIColor(255, 255, 255, 255), UIColor(78, 110, 242, 255), UIColor(78, 110, 242, 255), UIColor(255, 255, 255, 255));
	m_page[4]->SetCrBorder(UIColor(255, 255, 255, 255), UIColor(78, 110, 242, 255), UIColor(78, 110, 242, 255), UIColor(255, 255, 255, 255));
	m_page[4]->SetCrText(UIColor(57, 81, 196, 255), UIColor(255, 255, 255, 255), UIColor(255, 255, 255, 255), UIColor(204, 204, 204, 255));
	m_page[4]->SetCrEllipsis(UIColor(65, 65, 65, 200), UIColor(78, 110, 242, 255));
	m_page[4]->SetInterval(12);


	auto bstatic6 = new UIStatic(window, 20, 350, 150, 32, L"鹅厂风格：", 0, 0, 0, Middle | SingleLine);
	m_page[5] = new UIPage(window, 110, 350, 800, 32, L"", eos_paging | eos_paging_pnatext);
	m_page[5]->SetCount(1000, 20);
	m_page[5]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[5]->SetCrbkg(UIColor(246, 248, 250, 255), UIColor(246, 248, 250, 255), UIColor(255, 96, 34, 255), UIColor(246, 248, 250, 255));
	m_page[5]->SetCrBorder(UIColor(246, 248, 250, 255), UIColor(246, 248, 250, 255), UIColor(255, 96, 34, 255), UIColor(246, 248, 250, 255));
	m_page[5]->SetCrText(UIColor(0, 0, 40, 255), UIColor(254, 96, 34, 255), UIColor(255, 255, 255, 255), UIColor(162, 162, 182, 255));
	m_page[5]->SetCrEllipsis(UIColor(151, 151, 151, 200), UIColor(254, 96, 34, 255));
	m_page[5]->SetCurrent(28);
	m_page[5]->SetInterval(10);

	auto bstatic7 = new UIStatic(window, 20, 400, 150, 35, L"爱奇艺风格：", 0, 0, 0, Middle | SingleLine);
	m_page[6] = new UIPage(window, 110, 400, 800, 35, L"", eos_paging | eos_paging_pnatext);
	m_page[6]->SetCount(1000, 20);
	m_page[6]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[6]->SetCrbkg(UIColor(248, 248, 248, 255), UIColor(232, 252, 234, 255), UIColor(0, 190, 6, 255), UIColor(248, 248, 248, 255));
	m_page[6]->SetCrBorder(UIColor(248, 248, 248, 255), UIColor(232, 252, 234, 255), UIColor(0, 190, 6, 255), UIColor(248, 248, 248, 255));
	m_page[6]->SetCrText(UIColor(31, 31, 31, 255), UIColor(0, 196, 153, 255), UIColor(248, 248, 248, 255), UIColor(201, 201, 210, 255));
	m_page[6]->SetCrEllipsis(UIColor(201, 201, 210, 200), UIColor(0, 190, 6, 255));
	m_page[6]->SetCurrent(32);
	m_page[6]->SetBtnRound(2);
	m_page[6]->SetInterval(10);

	auto bstatic8 = new UIStatic(window, 20, 450, 150, 34, L"B站风格：", 0, 0, 0, Middle | SingleLine);
	m_page[7] = new UIPage(window, 110, 450, 800, 34, L"", eos_paging | eos_paging_pnatext);
	m_page[7]->SetCount(1000, 20);
	m_page[7]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[7]->SetCrbkg(UIColor(255, 255, 255, 255), UIColor(227, 229, 231, 255), UIColor(0, 174, 236, 255), UIColor(255, 255, 255, 255));
	m_page[7]->SetCrBorder(UIColor(255, 255, 255, 255), UIColor(227, 229, 231, 255), UIColor(0, 174, 236, 255), UIColor(255, 255, 255, 255));
	m_page[7]->SetCrText(UIColor(47, 47, 47, 255), UIColor(47, 47, 47, 255), UIColor(255, 255, 255, 255), UIColor(201, 204, 208, 255));
	m_page[7]->SetCrEllipsis(UIColor(201, 201, 210, 200), UIColor(0, 174, 236, 255));
	m_page[7]->SetCurrent(40);
	m_page[7]->SetBtnRound(4);

	auto bstatic9 = new UIStatic(window, 20, 500, 150, 34, L"优酷风格：", 0, 0, 0, Middle | SingleLine);
	m_page[8] = new UIPage(window, 110, 500, 800, 34, L"", eos_paging | eos_paging_pnatext);
	m_page[8]->SetCount(1000, 20);
	m_page[8]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[8]->SetCrbkg(UIColor(37, 37, 43, 255), UIColor(37, 37, 43, 255), UIColor(3, 200, 212, 255), UIColor(37, 37, 43, 255));
	m_page[8]->SetCrBorder(UIColor(37, 37, 43, 255), UIColor(37, 37, 43, 255), UIColor(3, 200, 212, 255), UIColor(37, 37, 43, 255));
	m_page[8]->SetCrText(UIColor(227, 227, 228, 255), UIColor(97, 246, 255, 255), UIColor(222, 248, 249, 255), UIColor(120, 120, 124, 255));
	m_page[8]->SetCrEllipsis(UIColor(215, 227, 228, 200), UIColor(91, 226, 234, 255));
	m_page[8]->SetCurrent(15);
	m_page[8]->SetBtnRound(6);

	auto bstatic10 = new UIStatic(window, 20, 550, 150, 30, L"圆角风格：", 0, 0, 0, Middle | SingleLine);
	m_page[9] = new UIPage(window, 110, 550, 800, 30, L"", eos_paging | eos_paging_pnatext);
	m_page[9]->SetCount(1000, 20);
	m_page[9]->SetEvent(WMM_PAGE_SELCHANGER, OnPageEvent);
	m_page[9]->SetCurrent(30);
	m_page[9]->SetBtnRound(15);


	window->Show();
	//window->MessageLoop();
}