﻿#pragma once
namespace HHBUI
{
	// 前向声明UIPage类
	class UIPage;
	
	enum TableinStyle
	{
		//事件报表
		WMM_RLVN_COLUMNCLICK = 97000,                //表头被单击
		WMM_RLVN_DRAW_TR = 97001,                //绘制表行
		WMM_RLVN_DRAW_TD = 97002,                //绘制表项
		WMM_RLVN_CHECK = 97003,                //检查框点击
		WMM_RLVN_DELETE_ITEM = 97004,                //当删除表项
		WMM_RLVN_CENTCHANGE = 97005,                //编辑内容改变 wParam所在列 lParam所在行
		WMM_RLVN_PAGE_CHANGE = 97006,                //页码改变 wParam为当前页码

		//报表表行风格
		rs_table_checkbox = 64,                 //检查框默认状态
		rs_table_checkboxok = 128,                 //检查框为选中状态
		rs_table_halfselect = 256,                 //检查框为半选中状态
		//项目风格
		es_table_disableed = 1,                 //禁止编辑
		//表格风格
		eos_table_pageable = 0x20000000,            //启用分页
	};
	
	class TOAPI UITable_Head : public UIControl
	{
	public:
		UITable_Head() = default;
		UITable_Head(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
		void Check(size_t iCol, BOOL fCheck);
		void Update();

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		size_t hittest(INT x, INT y, BOOL fJustHit, INT* rHitBlock);

		struct table_head_s
		{
			UIBrush* hBrush = nullptr;

		}s_data;
	};
	struct tableItem
	{
		LPCWSTR wzText;
		UIColor crText;      //项目文本颜色
		UIColor crbk;        //项目背景颜色
		LPARAM lParam;    //项目附加参数
		UIImage* nImageIndex; //项目图片索引
		DWORD dwStyle;     //项目风格
		BOOL dwMerge;      //合并
		BOOL dwMergebottom;//是否到合并底部
		BOOL dwMergeright;//是否到合并右边
		UINT imgWidth;
		UINT imgHeight;
	};
	class TOAPI UITable : public UIListView
	{
	public:
		UITable() = default;
		UITable(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1)
			: UIListView(hParent, x, y, width, height, L"form-table", dwStyle, dwStyleEx, nID, dwTextFormat) {
			OnCreate();
		}
		//插入列信息
		INT InsErtInfo(INT nInsertIndex, LPCWSTR pwzText, UINT nWidth, DWORD dwStyle = 0, DWORD dwTextFormat = -1,
			UIColor crText = {}, UIColor crbk = {}, UIColor crbk_hover = {}, UIImage* nImage = nullptr, bool draw = false);
		//置列信息
		void SetErtInfo(INT iCol, LPCWSTR pwzText, UINT nWidth = 0, DWORD dwStyle = 0, DWORD dwTextFormat = -1,
			UIColor crText = {}, UIColor crbk = {}, UIColor crbk_hover = {}, UIImage* nImage = nullptr, bool draw = false);
		//插入表项
		INT InsErtItem(INT nInsertIndex, DWORD dwStyle);
		//置表项
		void SetItem(INT iRow, INT iCol, LPCWSTR pwzText, UIColor crText = {}, UIColor crbk = {}, UIImage* nImage = nullptr, DWORD dwStyle = 0, LPARAM lParam = 0);
		/*
		* @brief 获取表项
		* @param iRow 行索引
		* @param iCol 列索引
		*/
		void GetItem(INT iRow, INT iCol, tableItem** item);
		//更新
		void Update();
		/*
		* @brief 排序
		* @param iCol 列索引
		* @param fDesc 是否倒序
		*/
		void Sort(INT iCol, BOOL fDesc);
		//设置选择框颜色
		void SetCrSelect(UIColor cr);
		/*
		* @brief 获取多选
		* @param iRow_start 起始行
		* @param iRow_end 终止行
		* @param iCol_start 起始列
		* @param iCol_end 终止列
		*/
		void GetSelectIndex(INT& iRow_start, INT& iRow_end, INT& iCol_start, INT& iCol_end);
		/*
		* @brief 设置多选
		* @param iRow_start 起始行
		* @param iRow_end 终止行
		* @param iCol_start 起始列
		* @param iCol_end 终止列
		*/
		void SetSelectIndex(INT iRow_start, INT iRow_end, INT iCol_start, INT iCol_end);
		/*
		* @brief 合并单元格、合并后以第一单元内容形式表现 其它单元数据会释放
		* @param iRow_start 起始行
		* @param iRow_end 终止行
		* @param iCol_start 起始列
		* @param iCol_end 终止列
		*/
		void SetMergeIndex(INT iRow_start, INT iRow_end, INT iCol_start, INT iCol_end);
		/*
		* @brief 分解单元格、分解表格中指定的已经组合的单元格，行列参数必须指向被组合单元格的第一个单元格
		* @param iRow_start 起始行
		* @param iRow_end 终止行
		* @param iCol_start 起始列
		* @param iCol_end 终止列
		*/
		void SetDecomPoseIndex(INT iRow_start, INT iRow_end, INT iCol_start, INT iCol_end);
		/*删除一个列表项目
		* @param iRow - 行索引
		* @param draw - 是否立即绘制
		*/
		BOOL DeleteItem(INT iRow, bool draw = true);
		/*删除所有列表项目*/
		void DeleteAllItem();
		/*删除列
		* @param iCol - 列索引
		* @param draw - 是否立即绘制
		*/
		void DeleteColumn(INT iCol, bool draw = true);
		/*删除全部列*/
		void DeleteAllColumn();
		//取列总数
		INT GetColumnCount();
		//取项目选中状态
		void GetItemCheck(size_t iRow, BOOL& fCheck);
		//置项目选中状态
		void SetItemCheck(size_t iRow, BOOL fCheck);
		//置表头高度
		void SetHeadHeight(INT ndheight);

		// 分页功能相关方法
		/*
		* @brief 启用分页
		* @param enable 是否启用分页
		* @param pageSize 每页显示的行数
		* @param x 分页控件x坐标
		* @param y 分页控件y坐标
		* @param width 分页控件宽度
		* @param height 分页控件高度
		*/
		void EnablePaging(BOOL enable, INT pageSize = 10, INT x = 0, INT y = 0, INT width = 0, INT height = 30);
		
		/*
		* @brief 获取分页控件
		* @return 分页控件指针
		*/
		UIPage* GetPageControl();
		
		/*
		* @brief 刷新分页
		*/
		void RefreshPaging();
		
		/*
		* @brief 跳转到指定页
		* @param page 页码
		*/
		void GoToPage(INT page);
		
		/*
		* @brief 获取总页数
		* @return 总页数
		*/
		INT GetTotalPages();

	protected:
		EXMETHOD LRESULT OnPsProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD BOOL OnPsCustomDraw(INT iItem, ps_customdraw ps) override;
		EXMETHOD BOOL OnPsDraw(ps_context ps) override;
		void HandleCellMerging(const LPVOID pTC, const LPVOID pTD, const LPVOID pTR, ExRectF rcTD, const ps_customdraw ps, INT iItem, INT iCol);
		void OnCreate();
		void OnUpdate(INT nWidth, INT nHeight);
		bool IndexCheck(INT index);
		LPVOID GetInfo(INT iRow, INT iCol);
		INT GetHitcol(INT x);
		void arr_del(size_t nIndex);
		static bool CompareByIRow(LPVOID a, LPVOID b, size_t iCol, BOOL fDesc, INT bSortablenType);
		static void Edit_Killfous(LPVOID p, UIEdit* edit, WPARAM wParam, LPARAM lParam);
		static LRESULT CALLBACK OnEdit_Enter_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam);
		static LRESULT CALLBACK OnPage_Change_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam);
		
		struct table_s
		{
			std::vector<LPVOID>* itemList = nullptr;
			UIBrush* hBrush = nullptr;
			UIPath* dpath = nullptr;
			UITable_Head* objhead = nullptr;
			UIEdit* objedit = nullptr;
			INT itemwidth = 0;//项目宽度
			INT itemheight = 0;//项目高度
			LPVOID tcinfo = nullptr;//列信息
			INT headheight = 0;//表头高度
			UINT fDesc_iCol = 0;//当前排序的列
			INT linewidth = 0;
			UIColor linecolour = {};
			UIColor select = { 0,108,190,255 };
			BOOL down_move = FALSE;
			INT iCol_move = 0;
			INT iRow_move = 0;
			INT iRow_start = 0;    //起始行
			INT iRow_end = 0;      //终止行
			INT iCol_start = 0;    //起始列
			INT iCol_end = 0;     //终止列
			INT top_start = 0;
			INT left_start = 0;
			INT indexhit = 0;//表头命中ID
			INT ctcs = 0;//列数
			INT nOffsetXOld = 0;
			INT nOffsetYOld = 0;
			BOOL itemcheckbox = FALSE;
			BOOL IsCtrl = FALSE;
			UIColor Color[2] = { UIColor(L"#c2c3c9"),UIColor(L"#cccedb") };
			
			// 分页相关
			UIPage* page = nullptr;
			BOOL isPaging = FALSE;
			INT pageSize = 10;
			INT currentPage = 1;
			INT pageX = 0;       // 分页控件X坐标
			INT pageY = 0;       // 分页控件Y坐标
			INT pageWidth = 0;   // 分页控件宽度
			INT pageHeight = 0;  // 分页控件高度
			std::vector<LPVOID>* allItems = nullptr; // 存储所有数据，用于分页
		}s_data;
		friend class UITable_Head;
	};
}
