﻿/**
 * @file res_pool.h
 * @brief 资源池管理
 */
#pragma once
#include "atom.h"
#include "engine/object_api.h"
namespace HHBUI
{

	interface IExResPool;

	EXENUM(ExResPoolItemFlags)
	{
		None = 0x0000,
		Eternal = 0x0001,
	};

	/// 资源池初始化项目回调函数类型
	typedef HRESULT(CALLBACK* ExResPoolInitItemProc)(IExResPool* pool, EXATOM key,
		const void* data, WPARAM wparam, LPARAM lparam, DWORD flags, void* r_res);

	/// 资源池释放项目回调函数类型
	typedef HRESULT(CALLBACK* ExResPoolFreeItemProc)(IExResPool* pool, EXATOM key,
		DWORD flags, void* res);

	/// 资源池枚举项目回调函数类型
	typedef HRESULT(CALLBACK* ExResPoolEnumItemProc)(IExResPool* pool, EXATOM key,
		DWORD flags, void* res, LPARAM lparam);

	////////////////////////

	EXINTERFACE("DCF4F67A-2D44-4D4C-9338-F0822DEB32CB") IExResPool : public IObject
	{
		EXMETHOD uint32_t GetItemCount() const PURE;
		EXMETHOD bool HasItem(EXATOM key) const PURE;
		EXMETHOD HRESULT FindKeyByPtr(void* res, EXATOM* r_key) const PURE;
		EXMETHOD HRESULT UseOrCreateItem(EXATOM key,  const void* data, WPARAM wparam, LPARAM lparam,
			DWORD flags, void** r_res) PURE;
		EXMETHOD HRESULT UseItem(EXATOM key, void** r_res) PURE;
		EXMETHOD HRESULT UnUseItem(EXATOM key) PURE;
		EXMETHOD HRESULT UnUseItemByPtr(void* res) PURE;
		EXMETHOD HRESULT EnumItems(ExResPoolEnumItemProc enum_proc, LPARAM lparam) PURE;
	};

	///////////////////////

	HRESULT ExResPoolCreate(size_t item_size, ExResPoolInitItemProc init_item_proc,
		ExResPoolFreeItemProc free_item_proc, IExResPool** r_pool);

}
