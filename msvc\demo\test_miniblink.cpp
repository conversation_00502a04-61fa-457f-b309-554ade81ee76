﻿#include "hhbui.h"

using namespace HHB<PERSON>;
UIMiniBlink* m_miniblink;
LRESULT CALLBACK OnMiniBlink_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARA<PERSON> wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	if (nCode == WMM_CLICK)
	{
		auto edit1 = (UIEdit*)window->FindUIView(L"1001");
		m_miniblink->LoadURLW(edit1->GetText());
	}
	else if (nCode == WMM_MB_URLCHANGED)
	{
		auto edit1 = (UIEdit*)window->FindUIView(L"1001");
		edit1->SetText((LPCWSTR)lParam);
	}
	else if (nCode == WMM_MB_TITLECHANGED)
	{
		window->SetText((LPCWSTR)lParam, TRUE);
	}
	return S_OK;
}
void testminiblink(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 700, 500, L"hello MiniBlink", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_BTN_HELP | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto edit1 = new UIEdit(window, 20, 70, 120, 33, L"https://www.baidu.com", 0, 0, 1001, SingleLine | Center | Middle);
	//edit1->SetColor(color_background, UIColor(47, 64, 86, 255));
	edit1->SetColor(color_border, UIColor(0, 108, 190, 255));
	edit1->SetColor(color_text_normal, UIColor(0, 0, 0, 255));
	edit1->SetColorCaret(UIColor(0, 0, 0, 255));
	edit1->SetRadius(8, 8, 8, 8);
	edit1->Lock(5, 40, 80, -1);

	auto btn = new UIButton(window, 0, 70, 100, 36, L"跳转到...", 0, eos_ex_autosize);
	btn->SetStyle(fill, success);
	btn->SetEvent(WMM_CLICK, OnMiniBlink_Event);
	btn->Lock(-1, 40, 5, -1);

	m_miniblink = new UIMiniBlink(window, 0, 0, 700, 500);
	m_miniblink->SetColor(color_background, UIColor(0, 0, 0, 220));
	m_miniblink->SetColor(color_text_normal, UIColor(255, 255, 255));
	m_miniblink->SetRadius(10, 10, 10, 10);
	m_miniblink->Lock(0, 80, 0, 0);
	m_miniblink->SetWkeDllPath(LR"(C:\Users\<USER>\Documents\HHBUI++\HHBUI\ThirdParty\miniblink\miniblink_4975_x64.dll)");
	m_miniblink->SetEvent(WMM_MB_URLCHANGED, OnMiniBlink_Event);
	m_miniblink->SetEvent(WMM_MB_TITLECHANGED, OnMiniBlink_Event);

	window->Show();
	//window->MessageLoop();
}