﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="demo.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="resource_loader.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="demo.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="resource_loader.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_animation.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_button.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_check.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_colorpicker.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_combobox.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_edit.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_free.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_groupbox.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_hotkey.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_imagebox.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_layout.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_list.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_menu.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_page.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_progress.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_slider.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_static.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_tabs.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_treeview.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_table.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_loading.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_knobs.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_datebox.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_toast.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_emoji.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="demo_xml.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_tour.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_miniblink.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_chart.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_timeline.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_segmented.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_modal.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_splashscreen.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_login.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_window_snap.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
</Project>