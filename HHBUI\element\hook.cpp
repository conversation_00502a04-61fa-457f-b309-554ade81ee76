﻿#include "pch.h"
#include "hook.h"
#include <common/winapi.h>

LPVOID HHBUI::UIhook::Thunkwindow(HWND hWnd, ThunkPROC pfnProc, LPVOID dwData)
{
#ifdef _WIN64
    CHAR shellcode[] = "\x48\xB9\x00\x00\x00\x00\x00\x00\x00\x00\x48\xB8\x00\x00\x00\x00\x00\x00\x00\x00\xFF\xE0";
    /*
16970E90000 - 48 B9 0000000000000000 - mov rcx,0000000000000000
16970E9000A - 48 B8 0000000000000000 - mov rax,0000000000000000
16970E90014 - FF E0                 - jmp rax
        */
#else
    CHAR shellcode[] = "\xC7\x44\x24\x04\x00\x00\xC4\x0E\xE9\x73\x47\x09\xF2";
    /*
00970000 - C7 44 24 04 0000C40E  - mov [esp+04],0EC40000
00970008 - E9 734709F2           - jmp F2A04780
        */
#endif

    size_t len = sizeof(shellcode) + sizeof(HK_THUNK_DATA);
    HK_THUNK_DATA* lpData = (HK_THUNK_DATA*)VirtualAlloc(0, len, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);

    if (lpData != NULL)
    {
        lpData->hWnd = hWnd;
        lpData->Proc = (WNDPROC)GetWindowLongPtr(hWnd, GWLP_WNDPROC);
        lpData->dwData = dwData;

        CHAR* lpCode = (CHAR*)(lpData + 1); // lpData + 1 是指向结构体末尾
        RtlMoveMemory(lpCode, shellcode, sizeof(shellcode));
#ifdef _WIN64
        * ((PUINT64)(lpCode + 2)) = (UINT64)lpData;
        *((PUINT64)(lpCode + 12)) = (UINT64)pfnProc;
#else
        * ((PUINT32)(lpCode + 4)) = (UINT32)lpData;
        *((PUINT32)(lpCode + 9)) = (UINT32)((UINT32)pfnProc - (UINT32)lpCode - 13);
#endif // _WIN64

        FlushInstructionCache(GetCurrentProcess(), lpData, len);
        SetWindowLongPtrW(hWnd, GWLP_WNDPROC, (size_t)lpCode);
    }
    return lpData;
}

LRESULT HHBUI::UIhook::hook_api_proc(INT code, WPARAM wParam, LPARAM lParam)
{
    if (code == HCBT_CREATEWND)
    {
        return hook_api_oncreate(code, (HWND)wParam, lParam);
    }
    return CallNextHookEx(UIWinApi::ToList.hHookMsgBox, code, wParam, lParam);
}

LRESULT HHBUI::UIhook::hook_api_oncreate(INT code, HWND hWnd, LPARAM lParam)
{
    auto lpcs = ((CBT_CREATEWND*)lParam)->lpcs;
    if (lpcs != nullptr) {
        auto hParent = lpcs->hwndParent;
        
        // 检查是否为对话框类
        if (IS_INTRESOURCE(lpcs->lpszClass) && 
            (ATOM)LOWORD((ULONG_PTR)lpcs->lpszClass) == ATOM_DIALOG)
        {
            auto hParentUI = HHBUI::UIWnd::GetFromUIWnd(hParent);
            if (hParentUI)
            {
                auto pWnd = hParentUI;
                mbp_s* pMsg = pWnd->m_data.lpMsgParams;
                pWnd->m_data.lpMsgParams = 0;
                if (pMsg != 0)
                {
                    HCURSOR hCursor = LoadCursorW(nullptr, IDC_ARROW);
                    SetClassLongPtrW(hWnd, GCLP_HCURSOR, (LONG_PTR)hCursor);
                    INT style = UISTYLE_TITLE | UISTYLE_BTN_CLOSE | UISTYLE_MOVEABLE | EWS_MESSAGEBOX;

                    if ((pMsg->dwFlags & styleex_windowicon) != 0)
                    {
                        style = style | UISTYLE_HASICON;
                    }
                    if ((pMsg->dwFlags & styleex_noinheritbkg) != 0)
                    {
                        style = style | UISTYLE_NOINHERITBKG;
                    }
                    if ((pMsg->dwFlags & styleex_noshadow) != 0)
                    {
                        style = style | UISTYLE_NOSHADOW;
                    }
                    auto window = new UIWnd(hWnd, style, (size_t)pMsg, pMsg->lpfnNotifyCallback);
                }
            }
        }
        // 检查是否为菜单类
        else if (IS_INTRESOURCE(lpcs->lpszClass) && 
                (ATOM)LOWORD((ULONG_PTR)lpcs->lpszClass) == ATOM_MENU)
        {
            Thunkwindow(hWnd, hook_api_menu_proc, 0);
        }
    }
    return CallNextHookEx(UIWinApi::ToList.hHookMsgBox, code, (WPARAM)hWnd, lParam);
}

void HHBUI::UIhook::hook_api_msgbox_drawinfo(UIWnd* pWnd, UICanvas* cvBkg, INT width, INT height)
{
    mbp_s* pMsg = pWnd->m_data.lpMsgParams;
    if (pMsg != 0)
    {
        INT wType = pMsg->uType & 240;

        INT l = UIEngine::fScale(20);
        INT t = UIEngine::fScale(15);
        auto objCaption = (UIControl*)pWnd->m_data.objCaption.load();
        if (objCaption)
        {
            ExRectF tmp{};
            objCaption->GetRect(tmp);
            t += tmp.bottom;
        }
        else
        {
            t += UIEngine::fScale(32);
        }
        if (wType != 0)
        {
            WCHAR value[10];
            _itow_s(wType, value, 10, 10);
            INT cmsg = _wtoi(value);
            auto str = L"msg" + std::to_wstring(cmsg);
            //画图标
            if (cmsg == 16)
            {
            }
            else if (cmsg == 32)
            {
            }
            else if (cmsg == 48)
            {
            }
            else if (cmsg == 64)
            {
            }
            else if (cmsg == 128)
            {

            }
        }

        std::wstring pText = pMsg->lpText;
        if (!pText.empty())
        {
            if (pMsg->crText.empty())
            {
                pMsg->crText = UIColor(0, 0, 0, 230);
            }
            if (!pMsg->crText.empty())
            {
                cvBkg->DrawTextByColor(UIWinApi::ToList.default_font, pText.c_str(), DT_NOPREFIX | DT_WORDBREAK | DT_EDITCONTROL, l, t, width - l, height, pMsg->crText);
            }
        }
    }
}
std::wstring _wnd_gettitle(HWND hWnd) //OK
{
    auto len = SendMessageW(hWnd, WM_GETTEXTLENGTH, 0, 0);
    len = (len + 1) * 2;
    std::wstring ret;
    ret.resize(len);
    SendMessageW(hWnd, WM_GETTEXT, len, (LPARAM)(ret.data()));
    return ret;
}
void HHBUI::UIhook::hook_api_msgbox_initdialog(HWND hWnd, UIWnd* pWnd, WPARAM wParam, LPARAM lParam)
{
    mbp_s* pMsg = pWnd->m_data.lpMsgParams;
    if (pMsg == 0)
        return;
    auto lpwzCheckbox = pMsg->lpCheckBox;
    auto lpCheckboxChecked = pMsg->lpCheckBoxChecked;
    auto uType = pMsg->uType;
    auto pfnCallback = pMsg->lpfnNotifyCallback;
    auto hWndChild = GetWindow(hWnd, GW_CHILD);
    RECT rcText = { 0 };
    std::vector<std::wstring> aryText;
    std::vector<INT> aryID;
    size_t iDef = 0;
    while (hWndChild != 0)
    {
        auto i = GetWindowLongPtrW(hWndChild, GWL_ID);
        if (i == 65535) //内容
        {
            GetWindowRect(hWndChild, &rcText);
        }
        else if (i == 20) //图标
        {
        }
        else
        {
            aryText.push_back(_wnd_gettitle(hWndChild));
            aryID.push_back(i);
            if ((GetWindowLongPtrW(hWndChild, GWL_STYLE) & BS_DEFPUSHBUTTON) != 0)
                iDef = i;
        }
        DestroyWindow(hWndChild);
        hWndChild = GetWindow(hWnd, GW_CHILD);
    }
    auto n = aryID.size();
    const int padding1 = UIEngine::fScale(10);
    const int padding2 = UIEngine::fScale(80);
    const int padding3 = UIEngine::fScale(5);
    const int total_padding = padding1 * 2 + padding2 * (INT)n + padding3 * (INT)n;
    int maxWidth = total_padding;

    auto hCanvas = pWnd->m_data.canvas_bkg.load();
    INT width = rcText.right - rcText.left;
    FLOAT w = NULL;
    FLOAT h = NULL;
    FLOAT widthCheckbox = NULL;
    FLOAT heightCheckbox = NULL;
    if (hCanvas->BeginDraw() == S_OK)
    {
        if (lpwzCheckbox)
        {
            hCanvas->CalcTextSize(UIWinApi::ToList.default_font, lpwzCheckbox, DT_LEFT | DT_SINGLELINE, 0, 0, &widthCheckbox, &heightCheckbox);
            widthCheckbox = widthCheckbox + 16 + 8;
            maxWidth += UIEngine::fScale(widthCheckbox);
        }
        hCanvas->CalcTextSize(UIWinApi::ToList.default_font, pMsg->lpText, DT_NOPREFIX | DT_WORDBREAK | DT_EDITCONTROL, width, rcText.bottom - rcText.top, &w, &h);

        if (w > width)
            width = w;
        hCanvas->EndDraw();
    }
    width += UIEngine::fScale(20 * 2);
    WCHAR bin[10];
    _itow_s(MB_ICONERROR, bin, 10, 10);

    INT cmsg = _wtoi(bin);
    if (cmsg == 16)
    {
    }
    else if (cmsg == 32)
    {
    }
    else if (cmsg == 48)
    {
    }
    else if (cmsg == 64)
    {
    }
    else if (cmsg == 128)
    {
    }
    if ((uType & 240) != 0)
    {
        width += UIEngine::fScale(35);
    }
    if (width > maxWidth)
        maxWidth = width;
    INT height = 35;
    INT maxHeight = h;
    if (maxHeight < height)
        maxHeight = height;

    const int GRID_LEFT = 2;
    const int GRID_TOP = 31;
    const int GRID_BOTTOM = 36;
    const int MIN_HEIGHT = 140;
    const int MIN_WIDTH = 220;
    const auto DPI = UIWinApi::ToList.drawing_default_dpi;

    RECT GRID = { GRID_LEFT, GRID_TOP, GRID_LEFT, GRID_BOTTOM };

    // 计算最大高度和宽度
    maxHeight = GRID.top + GRID.bottom + maxHeight + 15 * 2;
    maxHeight = std::max(maxHeight, (INT)UIEngine::fScale(MIN_HEIGHT));

    maxWidth = std::max(maxWidth, (INT)UIEngine::fScale(MIN_WIDTH));

    RECT rcWindow;
    GetWindowRect(hWnd, &rcWindow);
    INT windowWidth = rcWindow.right - rcWindow.left;
    INT windowHeight = rcWindow.bottom - rcWindow.top;

    MoveWindow(hWnd, rcWindow.left - (maxWidth - windowWidth) / 2, rcWindow.top - (maxHeight - windowHeight) / 2, maxWidth, maxHeight, FALSE);

    if (pfnCallback != nullptr)
    {
        pfnCallback(hWnd, pWnd, 0, 0, WMM_INTDLG, 0, 0);
    }

    GetWindowRect(hWnd, &rcWindow);
    maxWidth = rcWindow.right - rcWindow.left - pWnd->GetSideSize();
    maxHeight = rcWindow.bottom - rcWindow.top - pWnd->GetSideSize();

    INT top = (maxHeight / DPI) - GRID.bottom + (GRID.bottom - height) / 2.0f;
    INT left = (maxWidth / DPI) - 80.0f - pWnd->GetSideSize();


    for (size_t i = aryID.size(); i-- > 0;)
    {
        auto objTmp = new UIButton(pWnd, left, top - UIEngine::fScale(5), 80, height, aryText[i].c_str(), eos_textoffset, 0, aryID[i], TextFormat::Prefix | TextFormat::Center | TextFormat::Middle);
        objTmp->m_data.dwFlags |= EOF_BMSGBOXCONTROL;//给按钮设置这是信息框标志
        if (aryID[i] == iDef)
            objTmp->SetFocus();
        if (pMsg->dwButfig)//有自定义配置
        {
            auto dwButfig = (info_button_config*)pMsg->dwButfig;
            objTmp->SetCrText(dwButfig->crText[0], dwButfig->crText[1], dwButfig->crText[2]);
            objTmp->SetCrBkg(dwButfig->crBkg[0], dwButfig->crBkg[1], dwButfig->crBkg[2]);
            objTmp->SetCrBorder(dwButfig->crBorder[0], dwButfig->crBorder[1], dwButfig->crBorder[2]);
            objTmp->SetImgBkg(dwButfig->imgBkg[0], dwButfig->imgBkg[1], dwButfig->imgBkg[2]);
        }
        else if (i == 0)
        {
            objTmp->SetStyle(fill, primary);
        }
        left = left - 85;
    }
    pMsg->lpCheckBoxObj = nullptr;
    if (lpwzCheckbox)
    {
        auto objTmp = new UICheck(pWnd, 6, top, widthCheckbox, 20, lpwzCheckbox);
        if (__get_int(lpCheckboxChecked, 0) != 0)
        {
            objTmp->SetState(checked);
        }
        pMsg->lpCheckBoxObj = objTmp;
    }
    auto crText = UIColor(30, 30, 30, 255);
    INT wType = uType & 7;

    if (((pMsg->dwFlags & styleex_noinheritbkg) != 0))
    {
        UIColor crBackground = pMsg->crBackground;
        if (crBackground.empty())
            crBackground = UIColor(255, 255, 255, 255);
        pWnd->SetBackgColor(crBackground);
        if (!pMsg->crText.empty())
            crText = pMsg->crText;
    }
    info_objcaption Info{};
    Info.crTitle = crText;
    pWnd->SetCaptionInfo(&Info);
    if (pMsg->radius)
        pWnd->SetRadius(10);
    pWnd->SetShadowColor(pMsg->crshadow);

    auto objCaption = (UIControl*)pWnd->m_data.objCaption.load();
    if (wType == MB_ABORTRETRYIGNORE || wType == MB_YESNO)
    {
        auto objst = (UIControl*)objCaption->FindUIView(std::to_wstring(UISTYLE_BTN_CLOSE).c_str());
        if (objst)
            objst->Enable(FALSE);
    }
    pWnd->CenterFrom();

    pWnd->Show();
}

void HHBUI::UIhook::hook_api_menu_initdialog(HWND hWnd)
{
    auto hMenu = (HMENU)SendMessageW(hWnd, MN_GETHMENU, 0, 0);
    if (hMenu != 0)
    {
        MENUINFO mi{};
        mi.cbSize = sizeof(MENUINFO);
        mi.fMask = MIM_MENUDATA;
        GetMenuInfo(hMenu, &mi);

        UIWnd* pWnd = (UIWnd*)mi.dwMenuData;
        if (pWnd)
        {
            menu_s* lpMenuParams = pWnd->m_data.lpMenuParams;
            SetWindowLongPtrW(hWnd, GWL_EXSTYLE, WS_EX_TOPMOST | WS_EX_TOOLWINDOW | WS_EX_LAYERED);
            SetClassLongPtrW(hWnd, GCL_STYLE, CS_VREDRAW | CS_HREDRAW | CS_DBLCLKS);
            INT dwStyle = EWS_MENU | UISTYLE_NOINHERITBKG | UISTYLE_ESCEXIT | UISTYLE_FULLSCREEN;

            auto window = new UIWnd(hWnd, dwStyle, (size_t)pWnd, lpMenuParams->pfnCallback);
            window->m_data.pMenuHostWnd = pWnd;
            window->m_data.lpMenuParams = lpMenuParams;
            window->m_data.hMenuPopup = hMenu;
            window->m_data.dwFlags |= EWF_INTED;
            if (lpMenuParams->IsRadius)
                window->SetRadius(10);
            if (lpMenuParams->crBackground.empty())
                lpMenuParams->crBackground = UIColor(255, 255, 255, 255);
            window->SetBackgColor(lpMenuParams->crBackground);
            window->SetShadowColor(lpMenuParams->crshadow);
            window->SetBorderColor(lpMenuParams->crBorder);
            window->SetlParam((size_t)&lpMenuParams->crItemHot);
        }

    }
}

LRESULT HHBUI::UIhook::hook_api_menu_proc(HK_THUNK_DATA* pData, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    HWND hWnd = pData->hWnd;
    WNDPROC pOld = pData->Proc;
    UIWnd* pWnd = (UIWnd*)pData->dwData;
    if (uMsg == WM_DESTROY)
    {
        SetWindowLongPtrW(hWnd, GWLP_WNDPROC, (size_t)pOld);
        VirtualFree(pData, 0, MEM_RELEASE);
    }
    else if (uMsg == 482) //MN_SIZEWINDOW
    {
        hook_api_menu_initdialog(hWnd);
    }
    return CallWindowProcW(pOld, hWnd, uMsg, wParam, lParam);
}


