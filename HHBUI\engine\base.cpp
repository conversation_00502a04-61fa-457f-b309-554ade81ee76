﻿#include "pch.h"
#include "base.h"
#include <chrono>
#include <thread>
float HHBUI::UIFPSCounter::CalcFPS()
{
	steady_clock::time_point curTime = steady_clock::now();
	auto duration = duration_cast<microseconds>(curTime - lastTime);

	// 将微秒转换为秒
	double duration_s = duration.count() / 1000000.0;

	// 每一帧更新帧计数
	++frameCount;

	if (duration_s >= 1.0) // 每秒计算一次FPS
	{
		fps = frameCount / duration_s;
		frameCount = 0;
		lastTime = curTime; // 更新上次的时间
	}

	return fps;
}
void HHBUI::UIFPSCounter::SetMaxFPS(float fps)
{
	if (fps < 0.f)
		fpstime = -1.f;
	else {
		fpstime = fps;
		m_fpsLimit = duration_cast<steady_clock::duration>(duration<double>{ 1.f / fps });
	}
}
float HHBUI::UIFPSCounter::GetMaxFPS()
{
	if (fpstime == -1.f)
		return -1.f;
	return fpstime;
}
void HHBUI::UIFPSCounter::LimitFPS()
{
	if (fpstime != -1.0f)
	{
		auto time_in_seconds = time_point_cast<seconds>(steady_clock::now());
		++frame_count_per_second;
		if (time_in_seconds > prev_time_in_seconds)
		{
			frame_count_per_second = 0;
			prev_time_in_seconds = time_in_seconds;
		}

		// This part keeps the frame rate.
		std::this_thread::sleep_until(m_EndFrame);
		m_BeginFrame = m_EndFrame;
		m_EndFrame = m_BeginFrame + m_fpsLimit;
	}
}

HHBUI::UIRenderThread::UIRenderThread()
{
	m_stop = true;
}

HHBUI::UIRenderThread::~UIRenderThread()
{
	Stop();
}

void HHBUI::UIRenderThread::Start(bool pause)
{
	if (m_stop) {
		m_pause = pause;
		m_stop = false;
		m_thread = new std::thread(&UIRenderThread::Thread, this);
	}
}

void HHBUI::UIRenderThread::Pause()
{
	m_pause = true;
}

void HHBUI::UIRenderThread::Resume()
{
	m_pause = false;
	m_condition.notify_one();
}

void HHBUI::UIRenderThread::Stop()
{
	if (m_thread)
	{
		m_stop = true;
		m_pause = false;
		m_condition.notify_all();
		m_thread->join();
		delete m_thread;
		m_thread = nullptr;
	}
}

void HHBUI::UIRenderThread::Thread()
{
	while (!m_stop)
	{
		//线程暂停
		if (m_pause)
		{
			std::unique_lock<std::mutex>lock(m_mutex);
			//等待通知
			while (m_pause)
				m_condition.wait(lock);
		}
		RenderThread();
	}
}
