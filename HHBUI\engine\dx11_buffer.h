/**
 * @file dx11_buffer.h
 * @brief DX11缓冲区管理器
 */
/**
** =====================================================================================
**
**       文件名称: dx11_buffer.h
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】DirectX11缓冲区管理系统 - 高性能GPU缓冲区管理框架 （声明文件）
**
**       主要功能:
**       - 高性能DirectX11缓冲区创建与管理
**       - 智能GPU内存分配与优化
**       - 多类型缓冲区支持（顶点、索引、常量等）
**       - 动态缓冲区与静态缓冲区管理
**       - 缓冲区映射与数据更新操作
**       - 缓冲区池化与资源复用
**       - 缓冲区性能监控与统计
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - COM接口规范与智能指针管理
**       - 异常安全保证与错误恢复机制
**       - 高性能GPU内存管理算法
**       - 多线程安全的资源管理
**       - 智能缓冲区池化与复用机制
**       - 实时性能监控与调试诊断
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 创建DirectX11缓冲区管理系统
**                             2. 实现高性能GPU内存管理
**                             3. 添加智能缓冲区池化机制
**                             4. 支持多类型缓冲区管理
**                             5. 集成动态数据更新功能
**                             6. 添加性能监控与调试
**                             7. 确保线程安全与异常安全
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "common/unknown_impl.hpp"
#include <d3d11.h>
#include <wrl.h>
#include <vector>
#include <memory>

namespace HHBUI
{
	/// 顶点结构定义 - DX11缓冲区专用
	struct DX11Vertex
	{
		float position[3];    // 位置
		float texcoord[2];    // 纹理坐标
		float color[4];       // 颜色
	};

	/// 2D UI顶点结构
	struct UIVertex2D
	{
		float position[2];    // 2D位置
		float texcoord[2];    // 纹理坐标
		float color[4];       // 颜色
	};

	/// 常量缓冲区结构
	struct UIConstantBuffer
	{
		float transform[16];  // 4x4变换矩阵
		float viewport[2];    // 视口大小
		float padding[2];     // 填充
	};

	/// 文本渲染常量缓冲区
	struct UITextConstantBuffer
	{
		float text_color[4];     // 文本颜色
		float outline_color[4];  // 轮廓颜色
		float outline_width;     // 轮廓宽度
		float padding[3];        // 填充
	};

	/// 图像效果常量缓冲区
	struct UIImageEffectConstantBuffer
	{
		float tint_color[4];     // 色调颜色
		float brightness;        // 亮度
		float contrast;          // 对比度
		float saturation;        // 饱和度
		float gamma;             // 伽马值
	};

	/// DX11缓冲区实现类
	class UIDx11Buffer : public ExUnknownImpl<IBuffer>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IBuffer);
		EX_DECLEAR_INTERFACE_2(IID_IRenderObject);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIDx11Buffer(ID3D11Device* device, ID3D11DeviceContext* context);
		virtual ~UIDx11Buffer();

		// IRenderObject接口实现
		EXMETHOD RenderType GetRenderType() const override { return RenderType::D3D_ONLY; }
		EXMETHOD const RenderStats& GetRenderStats() const override { return m_stats; }
		EXMETHOD void ResetRenderStats() override { m_stats = RenderStats(); }

		// IBuffer接口实现
		EXMETHOD HRESULT Create(BufferType type, uint32_t size, const void* initial_data = nullptr, 
			bool dynamic = false, bool cpu_access = false) override;
		EXMETHOD HRESULT UpdateData(const void* data, uint32_t size, uint32_t offset = 0) override;
		EXMETHOD HRESULT Map(void** mapped_data, bool read_only = false) override;
		EXMETHOD void Unmap() override;
		EXMETHOD HRESULT Bind(uint32_t slot) override;
		EXMETHOD uint32_t GetSize() const override { return m_size; }
		EXMETHOD BufferType GetBufferType() const override { return m_buffer_type; }

		// 获取原生D3D11缓冲区
		ID3D11Buffer* GetNativeBuffer() const { return m_buffer.Get(); }

	private:
		D3D11_USAGE GetD3DUsage() const;
		UINT GetD3DBindFlags() const;
		UINT GetD3DCPUAccessFlags() const;

	private:
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;
		Microsoft::WRL::ComPtr<ID3D11Buffer> m_buffer;
		
		BufferType m_buffer_type;
		uint32_t m_size;
		bool m_dynamic;
		bool m_cpu_access;
		bool m_is_mapped;
		RenderStats m_stats;
	};

	/// 缓冲区管理器
	class UIBufferManager
	{
	public:
		UIBufferManager(ID3D11Device* device, ID3D11DeviceContext* context);
		~UIBufferManager();

		/// 创建缓冲区
		HRESULT CreateBuffer(BufferType type, IBuffer** buffer);

		/// 创建顶点缓冲区
		HRESULT CreateVertexBuffer(const void* vertices, uint32_t vertex_count, 
			uint32_t vertex_size, bool dynamic, IBuffer** buffer);

		/// 创建索引缓冲区
		HRESULT CreateIndexBuffer(const uint32_t* indices, uint32_t index_count, 
			bool dynamic, IBuffer** buffer);

		/// 创建常量缓冲区
		HRESULT CreateConstantBuffer(uint32_t size, bool dynamic, IBuffer** buffer);

		/// 创建预定义的UI常量缓冲区
		HRESULT CreateUIConstantBuffer(IBuffer** buffer);
		HRESULT CreateTextConstantBuffer(IBuffer** buffer);
		HRESULT CreateImageEffectConstantBuffer(IBuffer** buffer);

		/// 更新常量缓冲区
		HRESULT UpdateUIConstantBuffer(IBuffer* buffer, const UIConstantBuffer& data);
		HRESULT UpdateTextConstantBuffer(IBuffer* buffer, const UITextConstantBuffer& data);
		HRESULT UpdateImageEffectConstantBuffer(IBuffer* buffer, const UIImageEffectConstantBuffer& data);

		/// 获取默认的顶点缓冲区（全屏四边形）
		IBuffer* GetFullscreenQuadVertexBuffer();

		/// 获取默认的索引缓冲区（四边形索引）
		IBuffer* GetQuadIndexBuffer();

		/// 清理资源
		void Cleanup();

	private:
		HRESULT CreateDefaultBuffers();

	private:
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;
		
		// 默认缓冲区
		Microsoft::WRL::ComPtr<IBuffer> m_fullscreen_quad_vb;
		Microsoft::WRL::ComPtr<IBuffer> m_quad_index_buffer;
		
		// 缓冲区缓存
		std::vector<std::unique_ptr<UIDx11Buffer>> m_buffers;
	};

	/// 输入布局管理器
	class UIInputLayoutManager
	{
	public:
		UIInputLayoutManager(ID3D11Device* device);
		~UIInputLayoutManager();

		/// 创建标准UI顶点输入布局
		HRESULT CreateUIVertexInputLayout(ID3DBlob* vertex_shader_bytecode, 
			ID3D11InputLayout** input_layout);

		/// 创建2D UI顶点输入布局
		HRESULT CreateUI2DVertexInputLayout(ID3DBlob* vertex_shader_bytecode, 
			ID3D11InputLayout** input_layout);

		/// 获取默认的UI输入布局
		ID3D11InputLayout* GetDefaultUIInputLayout();

		/// 清理资源
		void Cleanup();

	private:
		HRESULT CreateDefaultInputLayouts();

	private:
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11InputLayout> m_default_ui_input_layout;
		Microsoft::WRL::ComPtr<ID3D11InputLayout> m_ui_2d_input_layout;
	};
}
