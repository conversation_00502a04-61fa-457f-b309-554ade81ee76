﻿#include "pch.h"
#include "knobs.h"
#include <wrl.h>

#define KNOBS_PI 3.14159265358979323846f
HHBUI::UIKnobs::UIKnobs(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-knobs", lpszName, dwStyle, dwStyleEx, nID, dwTextFormat);

    p_data.hBrush = new UIBrush();
    p_data.active = UIColor(78, 78, 78, 255);
    p_data.hovered = UIColor(178, 178, 78, 255);
    p_data.base = UIColor(67, 93, 21, 255);
}

void HHBUI::UIKnobs::SetKnob(float p_value, float v_min, float v_max, float speed, LPCWSTR format, int variant, int flags, float steps, int step_size, float angle_min, float angle_max)
{
    SetKnobInternal(p_value, v_min, v_max, speed, format, variant, flags, steps, step_size, angle_min, angle_max);
}

void HHBUI::UIKnobs::SetKnobInt(int p_value, int v_min, int v_max, float speed, LPCWSTR format, int variant, int flags, float steps, int step_size, float angle_min, float angle_max)
{
    SetKnobInternal(p_value, v_min, v_max, speed, format, variant, flags, steps, step_size, angle_min, angle_max);
}

float HHBUI::UIKnobs::GetValue()
{
    return p_data.p_value;
}

void HHBUI::UIKnobs::SetValue(float p_value)
{
    p_data.p_value = p_value;
    Redraw();
}

void HHBUI::UIKnobs::SetColorV(UIColor base, UIColor hovered, UIColor active)
{
    p_data.active = base;
    p_data.hovered = hovered;
    p_data.base = active;
    Redraw();
}

LRESULT HHBUI::UIKnobs::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_DESTROY)
	{
        delete p_data.hBrush;
	}
    else if (uMsg == WM_LBUTTONDOWN)
    {
        POINT pt;
        GetCursorPos(&pt);
        ScreenToClient(hWnd, &pt);
        bool drag_vertical =
            !(p_data.flags & KnobFlags_DragHorizontal) &&
            (p_data.flags & KnobFlags_DragVertical || fabsf(pt.y) > fabsf(pt.x));
        if (drag_vertical)
            p_data.start_pos = pt.x;
        else
            p_data.start_pos = pt.y;

        p_data.start_value = p_data.p_value;

        SetState(state_down, FALSE);
        Redraw();
    }
    else if (uMsg == WM_LBUTTONUP)
    {
        SetState(state_down, TRUE);
        Redraw();
    }
    else if (uMsg == WM_MOUSEHOVER)
    {
        SetState(state_hover, FALSE);
        Redraw();
    }
    else if (uMsg == WM_MOUSELEAVE)
    {
        SetState(state_hover, TRUE);
        Redraw();
    }
    else if (uMsg == WM_MOUSEMOVE)
    {
        if ((m_data.dwState & state_down) != 0)
        {
            POINT pt;
            GetCursorPos(&pt);
            ScreenToClient(hWnd, &pt);

            float s = 0.f;
            bool drag_vertical =
                !(p_data.flags & KnobFlags_DragHorizontal) &&
                (p_data.flags & KnobFlags_DragVertical || fabsf(pt.y) > fabsf(pt.x));
            if (drag_vertical)
                s = (pt.x - p_data.start_pos) * p_data.speed;
            else
                s = (pt.y - p_data.start_pos) * p_data.speed;

            float value = p_data.start_value + s * p_data.steps;
            p_data.p_value = std::clamp(value, p_data.v_min, p_data.v_max);
            DispatchNotify(WMM_KNOB_VALUE, 0, p_data.p_value);
            Redraw();
        }
    }
    else if (uMsg == WM_MOUSEWHEEL)
    {
        if ((m_data.dwState & state_hover) != 0)
        {
            int wheel = GET_WHEEL_DELTA_WPARAM(wParam);
            float value = 0.f, s = 0.f;
            if (wheel > 0) {
                value += p_data.speed;
            }
            else {
                value -= p_data.speed;
            }
            value = p_data.p_value + value * p_data.steps;

            p_data.p_value = std::clamp(value, p_data.v_min, p_data.v_max);
            DispatchNotify(WMM_KNOB_VALUE, 0, p_data.p_value);
            Redraw();
        }

    }

	return S_OK;
}
void HHBUI::UIKnobs::OnPaintProc(ps_context ps)
{
    float is_radius = 0.0, is_angle_min = 0.0, is_angle_max = 0.0, is_t = 0.0, is_angle = 0.0, is_angle_cos = 0.0, is_angle_sin = 0.0;
    bool is_active = false, is_hovered = false;
    D2D1_POINT_2F is_center{};
    knob_with_drag(ps, is_radius, is_center, is_active, is_hovered, is_angle_min, is_angle_max, is_t, is_angle, is_angle_cos, is_angle_sin);

    UIColor GetSecondaryColor = GetSecondaryColorSet(is_active, is_hovered);
    UIColor GetPrimaryColor = GetPrimaryColorSet(is_active, is_hovered, false);
    UIColor GetTrackColorSet = GetPrimaryColorSet(is_active, is_hovered, true);


    switch (p_data.variant) {
    case KnobVariant_Tick: {
        draw_circle(ps, 0.85f, true, 32, is_radius, is_center, GetSecondaryColor);
        draw_tick(ps, 0.5f, 0.85f, 0.08f, is_angle, is_radius, is_center, GetPrimaryColor);
        break;
    }
    case KnobVariant_Dot: {
        draw_circle(ps, 0.85f, true, 32, is_radius, is_center, GetSecondaryColor);
        draw_dot(ps, 0.12f, 0.6f, is_angle, true, 12, is_radius, is_center, GetPrimaryColor);
        break;
    }

    case KnobVariant_Wiper: {
        draw_circle(ps, 0.7f, true, 32, is_radius, is_center, GetSecondaryColor);
        draw_arc(ps, 0.8f, 0.41f, is_angle_min, is_angle_max, is_radius, is_center, GetTrackColorSet);

        if (is_t > 0.01f) {
            draw_arc(ps, 0.8f, 0.43f, is_angle_min, is_angle, is_radius, is_center, GetPrimaryColor);
        }
        break;
    }
    case KnobVariant_WiperOnly: {
        draw_arc(ps, 0.8f, 0.41f, is_angle_min, is_angle_max, is_radius, is_center, GetTrackColorSet);

        if (is_t > 0.01) {
            draw_arc(ps, 0.8f, 0.43f, is_angle_min, is_angle, is_radius, is_center, GetPrimaryColor);
        }
        break;
    }
    case KnobVariant_WiperDot: {
        draw_circle(ps, 0.6f, true, 32, is_radius, is_center, GetSecondaryColor);
        draw_arc(ps, 0.85f, 0.41f, is_angle_min, is_angle_max, is_radius, is_center, GetTrackColorSet);
        draw_dot(ps, 0.1f, 0.85f, is_angle, true, 12, is_radius, is_center, GetPrimaryColor);
        break;
    }
    case KnobVariant_Stepped: {
        for (auto n = 0.f; n < p_data.step_size; n++) {
            auto a = n / (p_data.step_size - 1);
            auto angle = is_angle_min + (is_angle_max - is_angle_min) * a;
            draw_tick(ps, 0.7f, 0.9f, 0.04f, angle, is_radius, is_center, GetPrimaryColor);
        }

        draw_circle(ps, 0.6f, true, 32, is_radius, is_center, GetSecondaryColor);
        draw_dot(ps, 0.12f, 0.4f, is_angle, true, 12, is_radius, is_center, GetPrimaryColor);
        break;
    }
    case KnobVariant_Space: {
        draw_circle(ps, 0.3f - is_t * 0.1f, true, 16, is_radius, is_center, GetSecondaryColor);

        if (is_t > 0.01f) {
            draw_arc(ps, 0.4f, 0.15f, is_angle_min - 1.0f, is_angle - 1.0f, is_radius, is_center, GetPrimaryColor);
            draw_arc(ps, 0.6f, 0.15f, is_angle_min + 1.0f, is_angle + 1.0f, is_radius, is_center, GetPrimaryColor);
            draw_arc(ps, 0.8f, 0.15f, is_angle_min + 3.0f, is_angle + 3.0f, is_radius, is_center, GetPrimaryColor);
        }
        break;
    }
    }
}

HHBUI::UIColor HHBUI::UIKnobs::GetSecondaryColorSet(bool is_active, bool is_hovered)
{
    UIColor color = is_active ? p_data.hovered : (is_hovered ? p_data.base : p_data.active);
    color.SetR(color.GetR() * 0.5f);
    color.SetG(color.GetG() * 0.5f);
    color.SetB(color.GetB() * 0.5f);
    return color;
}

HHBUI::UIColor HHBUI::UIKnobs::GetPrimaryColorSet(bool is_active, bool is_hovered, bool is_a)
{
    UIColor color = is_active ? p_data.hovered : (is_hovered ? p_data.base : p_data.active);
    if (is_a)
        color.SetA(0.8f);
    return color;
}

void HHBUI::UIKnobs::draw_dot(ps_context ps, float size, float radius, float angle, bool filled, int segments, float is_radius, D2D1_POINT_2F is_center, UIColor color)
{
    auto dot_size = size * is_radius;
    auto dot_radius = radius * is_radius;
    p_data.hBrush->SetColor(color);

    float x = is_center.x + cosf(angle) * dot_radius;
    float y = is_center.y + sinf(angle) * dot_radius;

    auto ellipse = D2D1::Ellipse(D2D1::Point2F(x, y), dot_size, dot_size);
    ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
}

void HHBUI::UIKnobs::draw_tick(ps_context ps, float start, float end, float width, float angle, float is_radius, D2D1_POINT_2F is_center, UIColor color)
{
    auto tick_start = start * is_radius;
    auto tick_end = end * is_radius;
    auto angle_cos = cosf(angle);
    auto angle_sin = sinf(angle);
    p_data.hBrush->SetColor(color);

    D2D1_POINT_2F tickStart = D2D1::Point2F(is_center.x + angle_cos * tick_start, is_center.y + angle_sin * tick_start);
    D2D1_POINT_2F tickEnd = D2D1::Point2F(is_center.x + angle_cos * tick_end, is_center.y + angle_sin * tick_end);

    ps.hCanvas->DrawLine(p_data.hBrush, tickStart, tickEnd, width * is_radius);
}

void HHBUI::UIKnobs::draw_circle(ps_context ps, float size, bool filled, int segments, float is_radius, D2D1_POINT_2F is_center, UIColor color)
{
    auto circle_radius = size * is_radius;
    p_data.hBrush->SetColor(color);
    auto ellipse = D2D1::Ellipse(is_center, circle_radius, circle_radius);
    ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
}

void HHBUI::UIKnobs::draw_arc(ps_context ps, float radius, float size, float start_angle, float end_angle, float is_radius, D2D1_POINT_2F is_center, UIColor color)
{
    auto track_radius = radius * is_radius;
    auto track_size = size * is_radius * 0.5f + 0.0001f;

    draw_arc_i(ps, track_radius, start_angle, end_angle, track_size, is_center, color);
}
inline float FormatAngle(float angle)
{
    angle = fmod(angle, 360.0F);
    if (angle < 0) angle += 360.0F;
    return angle;
}
void HHBUI::UIKnobs::draw_arc_i(ps_context ps, float radius, float start_angle, float end_angle, float thickness, D2D1_POINT_2F is_center, UIColor color)
{
    p_data.hBrush->SetColor(color);

    float start_angle_rad = FormatAngle(start_angle);
    float end_angle_rad = FormatAngle(end_angle);

    // Create a PathGeometry to store the arc
    Microsoft::WRL::ComPtr<ID2D1PathGeometry> pPathGeometry;
    Microsoft::WRL::ComPtr<ID2D1GeometrySink> pSink;

    HRESULT hr = UIDrawContext::ToList.d2d_factory->CreatePathGeometry(&pPathGeometry);
    if (FAILED(hr)) return;

    hr = pPathGeometry->Open(&pSink);
    if (FAILED(hr)) return;
    D2D1_ARC_SIZE arcSize = (fabs(end_angle_rad - start_angle_rad) > M_PI) ? D2D1_ARC_SIZE_LARGE : D2D1_ARC_SIZE_SMALL;
    // Create an arc segment
    D2D1_ARC_SEGMENT arcSegment = D2D1::ArcSegment(
        D2D1::Point2F(is_center.x + radius * cos(end_angle_rad), is_center.y + radius * sin(end_angle_rad)), // Arc end point
        D2D1::SizeF(radius, radius), // The size of the ellipse
        0.0f, // Rotation (0 degrees)
        (end_angle_rad > start_angle_rad) ? D2D1_SWEEP_DIRECTION_CLOCKWISE : D2D1_SWEEP_DIRECTION_COUNTER_CLOCKWISE, // Sweep direction
        arcSize // Arc start point
    );

    pSink->BeginFigure(D2D1::Point2F(is_center.x + radius * cos(start_angle_rad), is_center.y + radius * sin(start_angle_rad)), D2D1_FIGURE_BEGIN_FILLED);
    pSink->AddArc(arcSegment);
    pSink->EndFigure(D2D1_FIGURE_END_OPEN);
    pSink->Close();

    UIDrawContext::ToList.d2d_dc->DrawGeometry(pPathGeometry.Get(), (ID2D1Brush*)p_data.hBrush->GetContext(), thickness);
}

void HHBUI::UIKnobs::knob_with_drag(ps_context ps, float& is_radius, D2D1_POINT_2F& is_center, bool& is_active, bool& is_hovered, float& is_angle_min, float& is_angle_max,
    float& is_t, float& is_angle, float& is_angle_cos, float& is_angle_sin)
{
    auto speed = p_data.speed == 0 ? (p_data.v_max - p_data.v_min) / 250.f : p_data.speed;
    // Draw title
    if (!(p_data.flags & KnobFlags_NoTitle)) {
        auto title = GetText();
        if (title) {
            UIColor color_text;
            GetColor(color_text_normal, color_text);
            ps.hCanvas->DrawTextByColor(ps.hFont, title, ps.dwTextFormat, ps.rcText.left, ps.rcText.top, ps.uWidth, 20, color_text);
        }
    }

    is_radius = ps.uWidth * 0.5f;
    is_t = (p_data.p_value - p_data.v_min) / (p_data.v_max - p_data.v_min);
    is_angle_min = p_data.angle_min < 0 ? KNOBS_PI * 0.75f : p_data.angle_min;
    is_angle_max = p_data.angle_max < 0 ? KNOBS_PI * 2.25f : p_data.angle_max;

    is_center = { is_radius, ps.uHeight - is_radius };
    is_active = (ps.dwState & state_down) != 0;
    is_hovered = (ps.dwState & state_hover) != 0;
    is_angle = is_angle_min + (is_angle_max - is_angle_min) * is_t;
    is_angle_cos = cosf(is_angle);
    is_angle_sin = sinf(is_angle);

    // Draw input
    if (!(p_data.flags & KnobFlags_NoInput)) {
        auto title = vstring::format(p_data.format, p_data.p_value);
        UIColor color_text;
        GetColor(color_text_normal, color_text);
        ps.hCanvas->DrawTextByColor(ps.hFont, title.c_str(), ps.dwTextFormat, ps.rcText.left, ps.rcText.top + 20, ps.uWidth, ps.rcText.top + 40, color_text);
    }
}

