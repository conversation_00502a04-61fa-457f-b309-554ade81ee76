﻿#include "hhbui.h"
using namespace HHBUI;

void testlayout(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 500, 300, L"hello Layout", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE | UISTYLE_NOCAPTIONTOPMOST, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto bstatic = new UIStatic(window, 20, 10, 200, 30, L"我是绝对布局，跟随父对象调整尺寸~");
	bstatic->SetColor(color_background, UIColor(230, 231, 232, 255));
	bstatic->SetColor(color_border, UIColor(194, 195, 201, 255));

	window->Layout_Init(elt_absolute);
	window->Layout_Absolute_Setedge(bstatic, elcp_absolute_left, elcp_absolute_type_px, 50);
	window->Layout_Absolute_Setedge(bstatic, elcp_absolute_top, elcp_absolute_type_px, 70);
	window->Layout_Absolute_Setedge(bstatic, elcp_absolute_right, elcp_absolute_type_px, 50);
	window->Layout_Absolute_Setedge(bstatic, elcp_absolute_bottom, elcp_absolute_type_px, 50);

	auto bstatic1 = new UIStatic(bstatic, 20, 70, 200, 30, L"我跟随父对象调整位置~");
	bstatic1->SetColor(color_background, UIColor(230, 231, 232, 255));
	bstatic1->SetColor(color_border, UIColor(20, 126, 255, 255));
	bstatic1->SetRadius(13, 13, 13, 13);
	bstatic1->Lock(-1, -1, 10, 10);

	auto btn1 = new UIStatic(window, 20, 70, 120, 50, L"我创建在底层", 0, eos_ex_bottom, 0, Center | Bottom);
	btn1->SetColor(color_background, UIColor(252, 164, 84, 255));
	btn1->SetRadius(0, 10, 0, 0);
	window->Layout_Absolute_Setedge(btn1, elcp_absolute_right, elcp_absolute_type_px, 0);
	window->Layout_Absolute_Setedge(btn1, elcp_absolute_top, elcp_absolute_type_px, 0);

	auto btn2 = new UIButton(window, 70, 70, 100, 36, L"Button");
	btn2->SetStyle(fill, info);
	window->Layout_Absolute_Setedge(btn2, elcp_absolute_right, elcp_absolute_type_px, 200);
	window->Layout_Absolute_Setedge(btn2, elcp_absolute_top, elcp_absolute_type_px, 10);

	auto btn3 = new UIButton(bstatic, 0, 0, 100, 36, L"Button2",0, eos_ex_composited);
	btn3->SetStyle(fill, info);
	//window->Layout_Absolute_Setedge(btn3, elcp_absolute_left, elcp_absolute_type_px, 10);
	//window->Layout_Absolute_Setedge(btn3, elcp_absolute_top, elcp_absolute_type_px, 30);


	window->Show();
	//window->MessageLoop();
}