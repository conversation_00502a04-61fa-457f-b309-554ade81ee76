﻿#include "hhbui.h"

using namespace HHBUI;

void CALLBACK OnAnimationEvent_btn(LPVOID UIView, INT nIndex, BOOL nIsEnd, BOOL nInit, DOUBL<PERSON> nProgress, DOUBLE nCurrentX, D<PERSON><PERSON><PERSON><PERSON> nCurrentY, LONG_PTR param1, LONG_PTR param2, LONG_PTR param3, LONG_PTR param4)
{
	auto obj = (UIControl*)UIView;
	if (obj)
	{
		if (nInit)
			obj->Show(nInit);
		obj->Move(nCurrentX, nCurrentY, CW_USEDEFAULT, CW_USEDEFAULT);
	}
}

void testbutton(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 800, 500, L"hello Button", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX | UISTYLE_BTN_MENU |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_BTN_SETTING | UISTYLE_BTN_SKIN | UISTYLE_BTN_HELP | UISTYLE_MOVEABLE, hWnd);

	info_objcaption Info{};
	Info.crBkg = UIColor(78, 73, 16, 155);
	Info.crTitle = UIColor(255, 255, 255, 255);
	Info.crbutton_normal = UIColor(255, 255, 255, 255);
	Info.crbutton_hover = UIColor(255, 255, 255, 255);
	Info.crbutton_down = UIColor(200, 200, 200, 255);
	Info.dwTextFormat = Center | Middle | EndEllipsis;
	window->SetCaptionInfo(&Info);
	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);
	//window->SetAlpha(100);


	auto btn = new UIButton(window, 70, 70, 100, 36, L"Normal", eos_hidden);
	UIAnimation::Start(btn, (800 - 100) / 2, 70, 0, 70, AniEffect::Quadratic_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);

	auto btn1 = new UIButton(window, 180, 70, 100, 36, L"primary", eos_hidden);
	btn1->SetStyle(fill, primary);
	UIAnimation::Start(btn1, (800 - 100) / 2, 180, 0, 70, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);

	auto btn2 = new UIButton(window, 290, 70, 100, 36, L"success", eos_hidden);
	btn2->SetStyle(fill, success);
	auto badge = new UIBadge(btn2);
	badge->SetColor(UIColor(255, 255, 255, 255), UIColor(0, 0, 0, 255));
	badge->SetNumber(100);
	UIAnimation::Start(btn2, (800 - 100) / 2, 290, 0, 70, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);

	auto btn3 = new UIButton(window, 400, 70, 100, 36, L"info", eos_hidden);
	btn3->SetStyle(fill, info);
	UIAnimation::Start(btn3, (800 - 100) / 2, 400, 0, 70, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);

	auto btn4 = new UIButton(window, 510, 70, 100, 36, L"warning", eos_hidden);
	btn4->SetStyle(fill, warning);
	UIAnimation::Start(btn4, (800 - 100) / 2, 510, 0, 70, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);

	auto btn5 = new UIButton(window, 620, 70, 100, 36, L"danger", eos_hidden);
	btn5->SetStyle(fill, danger);
	UIAnimation::Start(btn5, (800 - 100) / 2, 620, 0, 70, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);

	///
	auto img1 = new UIImage(L"icons\\1.png"), img2 = new UIImage(L"icons\\2.png"), img3 = new UIImage(L"icons\\3.png"), img4 = new UIImage(L"icons\\4.png"), img5 = new UIImage(L"icons\\5.png"), img6 = new UIImage(L"icons\\6.png");

	auto btn_1 = new UIButton(window, 70, 130, 100, 36, L"Normal", eos_hidden);
	btn_1->SetIcon(img1);

	auto btn_2 = new UIButton(window, 180, 130, 100, 36, L"primary", eos_hidden);
	btn_2->SetStyle(plain, primary);
	
	auto btn_3 = new UIButton(window, 290, 130, 100, 36, L"success", eos_hidden);
	btn_3->SetStyle(plain, success);

	auto btn_4 = new UIButton(window, 400, 130, 100, 36, L"info", eos_hidden);
	btn_4->SetStyle(plain, info);
	
	auto btn_5 = new UIButton(window, 510, 130, 100, 36, L"warning", eos_hidden);
	btn_5->SetStyle(plain, warning);
	
	auto btn_6 = new UIButton(window, 620, 130, 100, 36, L"danger", eos_hidden);
	btn_6->SetStyle(plain, danger);

	UIAnimation::Start(btn_6, (800 - 100) / 2, 620, 0, 130, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_5, (800 - 100) / 2, 510, 0, 130, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_4, (800 - 100) / 2, 400, 0, 130, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_3, (800 - 100) / 2, 290, 0, 130, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_2, (800 - 100) / 2, 180, 0, 130, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_1, (800 - 100) / 2, 70, 0, 130, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);

	///
	auto btn_7 = new UIButton(window, 70, 190, 100, 36, L"Normal", eos_hidden);
	btn_7->SetRadius(18);
	UIAnimation::Start(btn_7, (800 - 100) / 2, 70, 190, 190, AniEffect::Sinusoidal_In, 10, FALSE, 1, FALSE, TRUE, OnAnimationEvent_btn);
	auto badge1 = new UIBadge(btn_7);
	badge1->SetNumber(100);

	auto btn_8 = new UIButton(window, 180, 190, 100, 36, L"primary", eos_hidden);
	btn_8->SetStyle(fill, primary);
	btn_8->SetRadius(18);
	btn_8->SetIcon(img2);
	UIAnimation::Start(btn_8, (800 - 100) / 2, 180, 190, 190, AniEffect::Sinusoidal_In, 10, FALSE, 1, FALSE, TRUE, OnAnimationEvent_btn);
	auto badge2 = new UIBadge(btn_8);
	badge2->SetNumber(50);


	auto btn_9 = new UIButton(window, 290, 190, 100, 36, L"success", eos_hidden);
	btn_9->SetStyle(fill, success);
	btn_9->SetRadius(18);
	btn_9->SetIcon(img3);
	UIAnimation::Start(btn_9, (800 - 100) / 2, 290, 190, 190, AniEffect::Sinusoidal_In, 10, FALSE, 1, FALSE, TRUE, OnAnimationEvent_btn);

	auto badge3 = new UIBadge(btn_9);
	badge3->SetBadgeType(UIBadge::badge_type::content);//设置文本类型
	badge3->SetText(L"new");

	auto btn_10 = new UIButton(window, 400, 190, 100, 36, L"info", eos_hidden);
	btn_10->SetStyle(fill, info);
	btn_10->SetRadius(18);
	btn_10->SetIcon(img4);
	UIAnimation::Start(btn_10, (800 - 100) / 2, 400, 190, 190, AniEffect::Sinusoidal_In, 10, FALSE, 1, FALSE, TRUE, OnAnimationEvent_btn);
	auto badge4 = new UIBadge(btn_10);
	badge4->SetBadgeType(UIBadge::badge_type::dot);

	auto btn_11 = new UIButton(window, 510, 190, 100, 36, L"warning", eos_hidden);
	btn_11->SetStyle(fill, warning);
	btn_11->SetRadius(18);
	btn_11->SetIcon(img5);
	UIAnimation::Start(btn_11, (800 - 100) / 2, 510, 190, 190, AniEffect::Sinusoidal_In, 10, FALSE, 1, FALSE, TRUE, OnAnimationEvent_btn);
	auto badge5 = new UIBadge(btn_11);
	badge5->SetColor(UIColor(22, 186, 170, 255), {});
	badge5->SetBadgeType(UIBadge::badge_type::dot);

	auto btn_12 = new UIButton(window, 620, 190, 100, 36, L"danger", eos_hidden);
	btn_12->SetStyle(fill, danger);
	btn_12->SetRadius(18);
	btn_12->SetIcon(img6);
	UIAnimation::Start(btn_12, (800 - 100) / 2, 620, 190, 190, AniEffect::Sinusoidal_In, 10, FALSE, 1, FALSE, TRUE, OnAnimationEvent_btn);


	///图标模式
	auto btn_13 = new UIButton(window, 70, 250, 36, 36, L"", eos_hidden);
	btn_13->SetStyle(circle);
	btn_13->SetRadius(18);
	btn_13->SetIcon(img1);

	auto btn_14 = new UIButton(window, 130, 250, 36, 36, L"", eos_hidden);
	btn_14->SetStyle(circle, primary);
	btn_14->SetRadius(18);
	btn_14->SetIcon(img2);

	auto btn_15 = new UIButton(window, 190, 250, 36, 36, L"", eos_hidden);
	btn_15->SetStyle(circle, success);
	btn_15->SetRadius(18);
	btn_15->SetIcon(img3);

	auto btn_16 = new UIButton(window, 250, 250, 36, 36, L"", eos_hidden);
	btn_16->SetStyle(circle, info);
	btn_16->SetRadius(18);
	btn_16->SetIcon(img4);

	auto btn_17 = new UIButton(window, 310, 250, 36, 36, L"", eos_hidden);
	btn_17->SetStyle(circle, warning);
	btn_17->SetRadius(18);
	btn_17->SetIcon(img5);

	auto btn_18 = new UIButton(window, 370, 250, 36, 36, L"", eos_hidden);
	btn_18->SetStyle(circle, danger);
	btn_18->SetRadius(18);
	btn_18->SetIcon(img6);

	UIAnimation::Start(btn_18, 800, 370, 500, 250, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_17, 800, 310, 500, 250, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_16, 800, 250, 500, 250, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_15, 800, 190, 500, 250, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_14, 800, 130, 500, 250, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);
	UIAnimation::Start(btn_13, 800, 70, 500, 250, AniEffect::Sinusoidal_In, 10, FALSE, 1, TRUE, TRUE, OnAnimationEvent_btn);





	auto btn_19 = new UIButton(window, 70, 300, 170, 70, L"我带模糊", 0, eos_ex_composited);
	btn_19->SetColor(color_background, UIColor(255, 255, 255, 130));
	btn_19->SetColor(color_border,UIColor(130, 130, 130, 255));
	btn_19->SetStyle(nobkg, nostyle);
	btn_19->SetBlur(3.5, TRUE);


	LPVOID imgdata;
	size_t retSize = 0;
	UIreadFile(LR"(icons\\IMG_0783.JPG)", imgdata, retSize);
	window->SetBackgImage(imgdata, retSize, 0, 0, bir_default, 0, bif_disablescale);
	window->SetBlur(10.f, TRUE);

	window->Show();
}
