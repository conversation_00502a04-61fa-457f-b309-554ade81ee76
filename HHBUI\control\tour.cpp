﻿#include "pch.h"
#include "tour.h"

HHBUI::UITour::UITour(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT nID, INT dwStyle, INT dwStyleEx, INT textFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-tour", lpszName, dwStyle, dwStyleEx, nID, textFormat);
	p_data.pArray = new UIarray();
}

void HHBUI::UITour::AddFrame(LPCWSTR tip, BOOL bUnion, ExRectF rc1, ExRectF rc2, ExRectF rc3, ExRectF rc4)
{
	auto pItemInfo = new FRAME();
	pItemInfo->tip = tip;
	pItemInfo->bUnion = bUnion;
	pItemInfo->rc[0] = rc1.fScale(UIEngine::GetDefaultScale());
	pItemInfo->rc[1] = rc2.fScale(UIEngine::GetDefaultScale());
	pItemInfo->rc[2] = rc3.fScale(UIEngine::GetDefaultScale());
	pItemInfo->rc[3] = rc4.fScale(UIEngine::GetDefaultScale());
	p_data.pArray->insert((size_t)pItemInfo);
}

void HHBUI::UITour::Step(int n)
{
	p_data.nStep += n;
	if (p_data.nStep >= 0 && p_data.nStep < (int)p_data.pArray->size()) {
		Redraw();
	}
	else {
		SendMsg(WM_DESTROY);
		//DispatchNotify(WMM_TOUR_DESTROY, 0, 0);
	}
}

LRESULT HHBUI::UITour::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_DESTROY)
	{
		delete p_data.pArray;
	}
	else if (uMsg == WM_MOUSEWHEEL)
	{
		int zDelta = GET_WHEEL_DELTA_WPARAM(wParam);
		Step(zDelta < 0 ? 1 : -1);
		return S_FALSE;
	}
	else if (uMsg == WM_KEYDOWN)
	{
		int nAdd = 0;
		switch (wParam)
		{
		case VK_ESCAPE:
			//DestroyWindow();
			return S_FALSE;
		case VK_LEFT:
		case VK_UP:
		case VK_PRIOR:
			nAdd = -1;
			break;
		case VK_RIGHT:
		case VK_DOWN:
		case VK_NEXT:
		case VK_SPACE:
		case VK_RETURN:
			nAdd = 1;
			break;
		case VK_HOME:
			nAdd = -p_data.nStep;
			break;
		case VK_END:
			nAdd = (INT)p_data.pArray->size() - p_data.nStep - 1;
			break;
		default:
			break;
		}
		if (nAdd)
		{
			Step(nAdd);
			return S_FALSE;
		}
	}
	else if (uMsg == WM_EX_LCLICK)
	{
		Step(1);
		return S_FALSE;
	}
	else if (uMsg == WM_EX_RCLICK)
	{
		Step(-1);
		return S_FALSE;
	}
	return S_OK;
}

void HHBUI::UITour::OnPaintProc(ps_context ps)
{
	//p_data.hBrush->SetColor();
	
	if (p_data.nStep < (int)p_data.pArray->size()) {
		std::vector<ExRectF> rcItems;
		auto item = (FRAME*)p_data.pArray->get(p_data.nStep + 1);
		for (int i = 0; i < 4; ++i) { 
			if (item->rc[i].left != 0 || item->rc[i].top != 0 || item->rc[i].right != 0 || item->rc[i].bottom != 0)
				rcItems.push_back(item->rc[i]);
		
		}

		//判断是不是联合
		if (item->bUnion) {
			ExRectF rcUnion;
			for (auto& e : rcItems) {
				rcUnion.UnionRect(rcUnion, e);
			}
			rcItems.resize(1);
			rcItems[0] = rcUnion;
		}

		for (auto& e : rcItems) {
			ps.hCanvas->SetClipRect(e.left, e.top, e.right, e.bottom);
			ps.hCanvas->Clear();
			ps.hCanvas->ResetClip();
		}
	

		vstring strTip; std::wstring teit = L"UI向导";
		auto wstr = GetText();
		if (wstr)
			teit = wstr;

		std::wstring title = L"%s\r\n\r\n< " + teit + L" %d/%d >";
		auto st = strTip.format(title.c_str(), item->tip, p_data.nStep + 1, (int)p_data.pArray->size());
		UIColor color_text;
		GetColor(color_text_normal, color_text);

		RECT rcOrigon = { 0,(LONG)ps.uHeight / 2,(LONG)ps.uWidth,(LONG)ps.uHeight };
		ps.hCanvas->DrawTextByColor(ps.hFont, st.c_str(), DT_CENTER | DT_WORDBREAK | DT_END_ELLIPSIS, rcOrigon.left, rcOrigon.top, rcOrigon.right, rcOrigon.bottom, color_text);
	}

}

