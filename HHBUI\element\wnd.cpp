﻿#include "pch.h"
#include "wnd.h"
#include <common/winapi.h>
#include <CommCtrl.h>
#include <atomic>
#include <common/Exception.h>
#include <common/memory.h>
#include <ThirdParty/pugixml/pugixml.hpp>
#include <engine/animation.h>
#include <algorithm>
#include <cmath>
#include <thread>
#include <chrono>
#pragma comment(lib, "imm32.lib")
#ifdef _WIN32
#pragma comment(lib, "Msimg32.lib")
#endif // _WIN32
#ifndef ASSERT
#define ASSERT(expr)  _ASSERTE(expr)
#endif

BOOL _wnd_addstyle(HWND hWnd, INT dwStyle, BOOL bExStyle)
{
    auto ret = GetWindowLongPtrW(hWnd, bExStyle ? GWL_EXSTYLE : GWL_STYLE);
    if ((ret & dwStyle) == 0)
    {
        SetWindowLongPtrW(hWnd, bExStyle ? GWL_EXSTYLE : GWL_STYLE, ret | dwStyle);
        return TRUE;
    }
    return FALSE;
}
BOOL _wnd_delstyle(HWND hWnd, INT dwStyle, BOOL bExStyle)
{
    auto ret = GetWindowLongPtrW(hWnd, bExStyle ? GWL_EXSTYLE : GWL_STYLE);
    if ((ret & dwStyle) != 0)
    {
        ret &= ~dwStyle;
        SetWindowLongPtrW(hWnd, bExStyle ? GWL_EXSTYLE : GWL_STYLE, ret);
        return TRUE;
    }
    return FALSE;
}
BOOL _wnd_querystyle(HWND hWnd, INT dwStyle, BOOL bExStyle)
{
    return (GetWindowLongPtrW(hWnd, bExStyle ? GWL_EXSTYLE : GWL_STYLE) & dwStyle) != 0;
}

LRESULT CALLBACK _wnd_defwindowprocW(HWND hWnd, UINT Msg, WPARAM wParam, LPARAM lParam)
{
    return DefWindowProcW(hWnd, Msg, wParam, lParam);
}
INT _wnd_wm_nchittest_sizebox(INT width, INT height, INT x, INT y, INT nOffset)
{
    INT ret = 0;
    if (y < nOffset) //鼠标在顶部
    {
        if (x < nOffset) //鼠标在左边
        {
            ret = HTTOPLEFT;
        }
        else if (x > width - nOffset) //鼠标在右边
        {
            ret = HTTOPRIGHT;
        }
        else
        {
            ret = HTTOP;
        }
    }
    else
    {
        if (y > height - nOffset) //鼠标在底部
        {
            if (x < nOffset) //鼠标在左边
            {
                ret = HTBOTTOMLEFT;
            }
            else if (x > width - nOffset) //鼠标在右边
            {
                ret = HTBOTTOMRIGHT;
            }
            else
            {
                ret = HTBOTTOM;
            }
        }
        else
        {
            if (x < nOffset) //鼠标在左边
            {
                if (y < nOffset) //鼠标在上面
                {
                    ret = HTTOPLEFT;
                }
                else
                {
                    if (y > height - nOffset) //鼠标在下面
                    {
                        ret = HTBOTTOMLEFT;
                    }
                    else
                    {
                        ret = HTLEFT;
                    }
                }
            }
            else
            {
                //鼠标在右边
                if (y < nOffset) //鼠标在上面
                {
                    ret = HTTOPRIGHT;
                }
                else
                {
                    if (y > height - nOffset) //鼠标在下面
                    {
                        ret = HTBOTTOMRIGHT;
                    }
                    else
                    {
                        ret = HTRIGHT;
                    }
                }
            }
        }
    }
    return ret;
}
HHBUI::UIWnd::~UIWnd()
{
    // 清理窗口吸附和布局管理功能
    wm_snap_cleanup();

    if (m_UIWindow)
        SendMsg(WM_CLOSE);
}
HHBUI::UIWnd::UIWnd(INT x, INT y, INT width, INT height, LPCWSTR lpwzWindowName, INT dwStyle, INT dwStyleEx, DWORD dwUIStyle, HWND hWndParent, LPCWSTR lpwzClassName, LPARAM lParam, MsgPROC lpfnMsgProc)
{
    wm_createuiwnd(x, y, width, height, lpwzWindowName, dwStyle, dwStyleEx, dwUIStyle, hWndParent, lpwzClassName, lParam, lpfnMsgProc);
}
HHBUI::UIWnd::UIWnd(HWND hWnd, DWORD dwUIStyle, LPARAM lParam, MsgPROC lpfnMsgProc)
{
    wm_build_load(hWnd, dwUIStyle, lParam, lpfnMsgProc);
}
BOOL HHBUI::UIWnd::wm_createuiwnd(INT x, INT y, INT width, INT height, LPCWSTR lpwzWindowName, INT dwStyle, INT dwStyleEx, DWORD dwUIStyle, HWND hWndParent, LPCWSTR lpwzClassName, LPARAM lParam, MsgPROC lpfnMsgProc)
{
    auto WindowClassName = (lpwzClassName == NULL ? L"Hhbui.WindowClass.UI" : lpwzClassName);
    if (dwStyle == 0)
    {
        dwStyle = WS_OVERLAPPEDWINDOW;
    }
    if (dwStyleEx == 0)
    {
        dwStyleEx = WS_EX_WINDOWEDGE | WS_EX_CLIENTEDGE;
    }
    dwStyleEx = dwStyleEx | WS_EX_LAYERED;
    HINSTANCE hInst = UIWinApi::ToList.engine_instance;

    if (IsWindow(hWndParent))
        hInst = (HINSTANCE)GetWindowLongPtrW(hWndParent, GWLP_HINSTANCE);

    HWND hWnd = CreateWindowExW(dwStyleEx, WindowClassName, lpwzWindowName, dwStyle, x, y, width, height, hWndParent, NULL, hInst, NULL);
    if (hWnd != 0)
    {
        SendMessageW(hWnd, 128, 0, (LPARAM)UIWinApi::ToList.hIconsm);
        SendMessageW(hWnd, 128, 1, (LPARAM)UIWinApi::ToList.hIcon);
        return wm_build_load(hWnd, dwUIStyle, lParam, lpfnMsgProc);
    }
    return FALSE;
}
BOOL HHBUI::UIWnd::CreateUIFromXML(HWND hWndParent, UIZip *hRes, LPCWSTR lpLayoutXml, MsgPROC lpfnMsgProc)
{
    BOOL ret = FALSE;
    pugi::xml_document Xml;
    if (hRes)
    {
        DWORD unsize = 0;
        LPVOID buffer;
        if (hRes->ReadSource(lpLayoutXml, &buffer, &unsize))//当为资源包的时候lpLayoutXml为路径
            ret = Xml.load_buffer(buffer, unsize);
    }
    else
    {
        //ret = Xml.load_buffer(lpLayoutXml, wcslen(lpLayoutXml));
        ret = Xml.load_string(lpLayoutXml);
        /*
        LPVOID tmp = H_MemAlloc(dwLen);
        RtlMoveMemory(tmp, lpLayoutXml, dwLen);
        unicodeStr = u2w((char*)tmp);
        H_MemFree(tmp);'*/
    }
    HWND hWnd = 0;
    pugi::xml_node root = Xml.child(L"UIWnd");
    if (!root.empty())
    {
        std::wstring value = root.attribute(L"Frame").value();
        auto rect = vstring::ToRect(value);
        std::wstring classname = root.attribute(L"Class-Name").as_string();
        if (!classname.empty())
        {
            RegClass(classname.c_str());
        }
        else
        {
            classname = L"Hhbui.WindowClass.UI";
        }
        if (wm_createuiwnd(rect.left, rect.top, rect.right, rect.bottom, root.attribute(L"Title").as_string(), root.attribute(L"Style").as_int(),
            root.attribute(L"StyleEx").as_int(), root.attribute(L"UIStyle").as_int(), hWndParent, classname.c_str(), root.attribute(L"lParam").as_int(), lpfnMsgProc))
        {
            info_objcaption Info{};
            std::wstring value = root.attribute(L"Bkg-Color").as_string();
            INT valueInt = 0;
            float valueFloat = 0.f;
            if(!value.empty())
                SetBackgColor(UIColor(value.c_str()));
            value = root.attribute(L"Frame-Color").as_string();
            if (!value.empty())
                SetBorderColor(UIColor(value.c_str()));
            value = root.attribute(L"Shadow-Color").as_string();
            if (!value.empty())
                SetShadowColor(UIColor(value.c_str()));

            value = root.attribute(L"Bkg-Image").as_string();
            if (!value.empty())
            {
                DWORD unsize = 0;
                LPVOID buffer = nullptr;
                if (hRes && hRes->ReadSource(value.c_str(), &buffer, &unsize))
                    SetBackgImage(buffer, unsize);
                else
                    SetBackgImage(value.c_str());
            }
            value = root.attribute(L"Title-Color").as_string();
            if (!value.empty())
                Info.crTitle = UIColor(value.c_str());
            value = root.attribute(L"Title-Bkg-Color").as_string();
            if (!value.empty())
                Info.crBkg = UIColor(value.c_str());
            value = root.attribute(L"Title-BtnNormal-Color").as_string();
            if (!value.empty())
                Info.crbutton_normal = UIColor(value.c_str());
            value = root.attribute(L"Title-BtnHot-Color").as_string();
            if (!value.empty())
                Info.crbutton_hover = UIColor(value.c_str());
            value = root.attribute(L"Title-BtnDown-Color").as_string();
            if (!value.empty())
                Info.crbutton_down = UIColor(value.c_str());
            valueInt = root.attribute(L"Title-Format").as_int();
            if (valueInt != 0)
                Info.dwTextFormat = valueInt;

            SetCaptionInfo(&Info);
            valueFloat = root.attribute(L"Blur").as_float();
            if (valueFloat != 0.f)
                SetBlur(valueFloat, TRUE);
            valueInt = root.attribute(L"Radius").as_int();
            if (valueInt != 0)
                SetRadius(valueInt);
            valueInt = root.attribute(L"Alpha").as_int();
            if (valueInt != 0)
                SetAlpha(valueInt);
            valueInt = root.attribute(L"Layout").as_int();
            if (valueInt != 0)
            {
                Layout_Init(valueInt);
            }

            wm_xml_load_init(hRes, reinterpret_cast<void*>(&root), 0, this);



            ret = TRUE;
        }
    }
    else
    {
        ExStatusHandle(EE_IO, __CALLINFO__, L"不是有效的窗口布局文件！");
    }
    return ret;
}
BOOL HHBUI::UIWnd::SaveUIFromXmlFile(LPCTSTR pszSavePath)
{
    pugi::xml_document doc;
    auto root = doc.append_child(L"form-window");








    return 0;
}
BOOL HHBUI::UIWnd::wm_build_load(HWND hWnd, DWORD dwUIStyle, LPARAM lParam, MsgPROC lpfnMsgProc)
{
    if (m_UIWindow || IsWindow(m_data.hWnd)) return false;
    RECT rcWindow{ 0 };
    GetWindowRect(hWnd, &rcWindow);
    m_data.size_Border = 1;
    m_data.size_shadow = 10;
    if (!FLAGS_CHECK(m_data.dwUIStyle, UISTYLE_NOSHADOW))
        m_data.size_tmp = m_data.size_shadow;
    else
        m_data.size_tmp = m_data.size_Border;

    SIZE size{};
    if (!FLAGS_CHECK(dwUIStyle, EWS_MESSAGEBOX) && !FLAGS_CHECK(dwUIStyle, EWS_COMBOWINDOW))
    {
        size.cx = rcWindow.right - rcWindow.left + m_data.size_tmp;
        size.cy = rcWindow.bottom - rcWindow.top + m_data.size_tmp;
        INT offsetX = 0;
        INT offsetY = 0;
        size.cx = UIEngine::fScale(size.cx);
        size.cy = UIEngine::fScale(size.cy);
        offsetX = (size.cx - rcWindow.right - rcWindow.left) / 2;
        offsetY = (size.cy - rcWindow.bottom - rcWindow.top) / 2;
        MoveWindow(hWnd, rcWindow.left - offsetX, rcWindow.top - offsetY, size.cx, size.cy, FALSE);
    }
    m_data.hWnd = hWnd;
    HWND hWndParent = GetWindowOwner(hWnd);
    if (FLAGS_CHECK(dwUIStyle, EWS_MESSAGEBOX))
        hWndParent = ((mbp_s*)lParam)->hWnd;
    else if ((dwUIStyle & UISTYLE_CENTERWINDOW) == UISTYLE_CENTERWINDOW)
        CenterFrom();

    GetWindowRect(hWnd, &rcWindow);
    size.cx = rcWindow.right - rcWindow.left + m_data.size_tmp;
    size.cy = rcWindow.bottom - rcWindow.top + m_data.size_tmp;
    INT dwFlags = 0;
    if (_wnd_querystyle(hWnd, WS_EX_LAYERED, TRUE) || FLAGS_CHECK(dwUIStyle, EWS_MESSAGEBOX))
    {
        dwFlags = EWF_BLAYERED;
    }
    if (hWndParent != 0)
    {
        m_data.hParent = (UIWnd*)SendMessageW(hWndParent, UIWinApi::ToList.dwMessage, 0, MAKELONG(EMT_DUI, 0));
        if (!FLAGS_CHECK(dwUIStyle, EWS_MESSAGEBOX) && !FLAGS_CHECK(dwUIStyle, EWS_MENU) && !FLAGS_CHECK(dwUIStyle, EWS_COMBOWINDOW))
        {
            EnableWindow(hWndParent, FALSE);
            BringWindowToTop(hWnd);
        }
    }
    auto atomName = GetClassLongPtrW(hWnd, GCW_ATOM);
    if (atomName == ATOM_DIALOG)
        dwFlags = dwFlags | EWF_BMODAL | EWF_ACTIVE;
    m_data.dwUIStyle = dwUIStyle;
    m_data.hImc = ImmGetContext(hWnd);
    m_data.hWndParent = hWndParent;
    m_data.size = { (float)rcWindow.left,(float)rcWindow.top,(float)size.cx ,(float)size.cy };
    m_data.dwFlags = dwFlags | EWF_BLEAVESENT;
    m_data.pfnMsgProc = lpfnMsgProc;
    m_data.alpha = 255;
    m_data.size_caption = { 100,40 };
    m_data.margin_caption = { 0.f,0.f,0.f,0.f };
    m_data.radius = 0;
    m_data.radius_r = 0;
    m_UIView = nullptr;
    m_UIWindow = this;
    if (FLAGS_CHECK(dwUIStyle, EWS_MESSAGEBOX))
    {
        m_data.lpMsgParams = (mbp_s*)lParam;
    }
    else
    {
        m_data.lParam = lParam;
    }
    m_data.hPath_radius = new UIPath();
    m_data.crshadow = std::make_unique <UIBrush>(UIColor(238, 238, 242, 255));
    m_data.crBorder = std::make_unique <UIBrush>(UIColor(245, 245, 245, 255));
    m_data.crbkg = std::make_unique <UIBrush>(UIColor(255, 255, 255, 255));
    //sysshadow
    auto style = GetClassLongPtrW(hWnd, GCL_STYLE);
    if ((style & CS_DROPSHADOW) != 0)
    {
        SetClassLongPtrW(hWnd, GCL_STYLE, (style & ~CS_DROPSHADOW));
    }
    //tips
    m_data.hWndTips = CreateWindowExW(WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE | WS_EX_TOPMOST, TOOLTIPS_CLASS, NULL, TTS_ALWAYSTIP,
        CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT,
        hWndParent, NULL, GetModuleHandle(NULL), NULL);

    SendMessageW(m_data.hWndTips, TTM_SETMAXTIPWIDTH, 0, 2048); //支持多行
    m_data.toolauto = new ti_s();
    m_data.toolauto->cbSize_ = sizeof(ti_s);
    m_data.toolauto->uFlags_ = TTF_SUBCLASS | TTF_TRANSPARENT | TTF_PARSELINKS | TTF_IDISHWND;
    m_data.toolauto->hWnd_ = hWnd;
    m_data.toolauto->uId_ = m_data.toolauto;
    SendMessageW(m_data.hWndTips, TTM_ADDTOOLW, 0, (LPARAM)m_data.toolauto);

    m_data.toolrack = new ti_s();
    m_data.toolrack->cbSize_ = sizeof(ti_s);
    m_data.toolrack->uFlags_ = TTF_TRACK | TTF_ABSOLUTE | TTF_TRANSPARENT | TTF_PARSELINKS | TTF_IDISHWND;
    m_data.toolrack->hWnd_ = hWnd;
    m_data.toolrack->uId_ = m_data.toolrack;
    SendMessageW(m_data.hWndTips, TTM_ADDTOOLW, 0, (LPARAM)m_data.toolrack);

    //UIhook::Thunkwindow(m_data.hWndTips, OnToolTipWndProc, this);

    m_data.canvas_display = new UICanvas(this, size.cx, size.cy);
    m_data.canvas_bkg = new UICanvas(this, size.cx, size.cy);

    _wnd_addstyle(hWnd, WS_THICKFRAME, FALSE); //窗口具有调整大小边框。 与 WS_SIZEBOX 样式相同。
    UIhook::Thunkwindow(hWnd, OnWndProc, this);
    _wnd_delstyle(hWnd, WS_THICKFRAME, FALSE);
    ImmAssociateContext(hWnd, FALSE);
    wm_recalcclient(size.cx, size.cy);
    if (!FLAGS_CHECK(dwUIStyle, EWS_MENU))
    {
        if ((dwUIStyle & UISTYLE_NOTITLEBAR) != UISTYLE_NOTITLEBAR)
            wm_sys_init();
        else
        {

        }
        if ((dwUIStyle & UISTYLE_FULLSCREEN) == UISTYLE_FULLSCREEN)
        {
            PostMessageW(m_data.hWnd, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
            SetWindowPos(m_data.hWnd, HWND_TOPMOST, 0, 0, 0, 0, 3);
        }
    }

    if (FLAGS_CHECK(dwUIStyle, UISTYLE_POPUPWINDOW))
    {
        m_data.lpPopupParams = (UIControl*)lParam;
        auto pWnd = (UIWnd*)m_data.hParent;
        if (pWnd)
        {
            HWND old = pWnd->m_data.hWndPopup;
            pWnd->m_data.hWndPopup = hWnd;
            DestroyWindow(old);
            FLAGS_ADD(pWnd->m_data.dwFlags, EWF_BPOPUPWINDOIWSHOWN);
            auto pObj = (UIControl*)lParam;
            if (pObj)
                ImmAssociateContext(hWndParent, pWnd->m_data.hImc);
        }
    }
    m_data.uTimerID = 0x1000;

    // 初始化窗口吸附和布局管理功能
    wm_snap_init();

    return TRUE;
}
bool w2bool(std::wstring str)
{
    return str.size() != 0;
}
void HHBUI::UIWnd::wm_xml_load_init(UIZip* hRes, LPVOID node, int depth, UIBase *parent)
{
    pugi::xml_node root = *reinterpret_cast<pugi::xml_node*>(node);
    if (root.type() == pugi::node_element)
    {
        if (depth != 0)
        {
            std::wstring root_name(root.name());
            for (pugi::xml_attribute attribute : root.attributes())
            {
                std::wstring attribute_name(attribute.name());
                std::wstring next_attribute = attribute.next_attribute().name();
                if (!w2bool(next_attribute))
                {
                    //output(std::wstring(depth, '\t').c_str(), root_name, next_attribute);

                    pugi::xml_node parentNode = root.parent();
                    // 判断上层节点是否有子节点
                    auto handle = parentNode.first_child() ? parent : this;

                    vstring AttribValuerect;
                    RECT rect = AttribValuerect.ToRect(root.attribute(L"Frame").value());
                    wm_xml_load_done(parent, handle, root_name, rect, reinterpret_cast<void*>(&root));


                }
            }
        }
        for (pugi::xml_node childNode : root.children())
        {
            wm_xml_load_init(hRes, reinterpret_cast<void*>(&childNode), depth + 1, parent);
        }
    }
    else if (root.type() == pugi::node_pcdata || root.type() == pugi::node_cdata) // 处理文本节点
    {

    }
}
void HHBUI::UIWnd::wm_xml_load_done(UIBase*& parent, UIBase* handle, const std::wstring& root_name, RECT rect, LPVOID node)
{
    pugi::xml_node root = *reinterpret_cast<pugi::xml_node*>(node);
    auto objattribute = [&](UIControl* obj) {
        INT valueInt = root.attribute(L"Layout").as_int();
        std::wstring value;
        if (valueInt == -1)
        {
            if (Layout_GetType() == elt_absolute)
            {
                value = root.attribute(L"Layout-value").as_string();
                std::vector<std::wstring> r_result = vstring::Tosplit(value);
                const std::array<int, 4> elcp_map = {
                    elcp_absolute_left,
                    elcp_absolute_top,
                    elcp_absolute_right,
                    elcp_absolute_bottom
                };

                for (size_t i = 0; i < r_result.size(); ++i) {
                    auto tmp1 = wcschr(r_result[i].c_str(), 37);  // 查找 '%' 的位置
                    bool is_percent = (tmp1 != nullptr);  // 判断是否包含 '%'
                    INT mva = std::stoi(r_result[i]);
                    if (mva != -1)
                        Layout_Absolute_Setedge(obj, elcp_map[i], is_percent ? elcp_absolute_type_ps : elcp_absolute_type_px, mva);

                }

            }
        }
        value = root.attribute(L"Bkg-Color").as_string();
        if (!value.empty())
            obj->SetColor(color_background, UIColor(value.c_str()));
        value = root.attribute(L"Frame-Color").as_string();
        if (!value.empty())
            obj->SetColor(color_border, UIColor(value.c_str()));
        value = root.attribute(L"Text-Color-Normal").as_string();
        if (!value.empty())
            obj->SetColor(color_text_normal, UIColor(value.c_str()));
        value = root.attribute(L"Text-Color-Hover").as_string();
        if (!value.empty())
            obj->SetColor(color_text_hover, UIColor(value.c_str()));
    };

    if (root_name == L"UIStatic") {
        auto obj = new UIStatic(handle, rect.left, rect.top, rect.right, rect.bottom, root.attribute(L"Title").as_string(), root.attribute(L"Style").as_int(), root.attribute(L"StyleEx").as_int(), root.attribute(L"ID").as_int(), root.attribute(L"Title-Format").as_int());
        objattribute(obj);
        parent = obj;
        return;
    }
    else if (root_name == L"UICheck") {
        auto obj = new UICheck(handle, rect.left, rect.top, rect.right, rect.bottom, root.attribute(L"Title").as_string(), root.attribute(L"ID").as_int(), root.attribute(L"Style").as_int(), root.attribute(L"StyleEx").as_int(),  root.attribute(L"Title-Format").as_int());
        objattribute(obj);
        parent = obj;
        return;
    }

}
void HHBUI::UIWnd::CenterFrom()
{
    RECT rcWindow{}, rcParent{}, rcArea{};
    GetWindowRect(m_data.hWnd, &rcWindow);
    UIWinApi::GetWndScreenRect(m_data.hWnd, rcParent, rcArea);

    int DlgWidth = (rcWindow.right - rcWindow.left) + m_data.size_tmp;
    int DlgHeight = (rcWindow.bottom - rcWindow.top) + m_data.size_tmp;

    // Find dialog's upper left based on rcCenter
    int xLeft = (rcParent.left + rcParent.right) / 2 - DlgWidth / 2;
    int yTop = (rcParent.top + rcParent.bottom) / 2 - DlgHeight / 2;

    // The dialog is outside the screen, move it inside
    if (xLeft < rcArea.left) xLeft = rcArea.left;
    else if (xLeft + DlgWidth > rcArea.right) xLeft = rcArea.right - DlgWidth;
    if (yTop < rcArea.top) yTop = rcArea.top;
    else if (yTop + DlgHeight > rcArea.bottom) yTop = rcArea.bottom - DlgHeight;
    ::SetWindowPos(m_data.hWnd, NULL, xLeft, yTop, DlgWidth, DlgHeight, SWP_NOZORDER | SWP_NOOWNERZORDER | SWP_NOACTIVATE);

}

void HHBUI::UIWnd::Max()
{
    PostMessageW(m_data.hWnd, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
}
void HHBUI::UIWnd::Min()
{
    PostMessageW(m_data.hWnd, WM_SYSCOMMAND, SC_MINIMIZE, 0);
}
HWND HHBUI::UIWnd::GethWnd()
{
    return m_data.hWnd;
}

HWND HHBUI::UIWnd::GethWndParent()
{
    return m_data.hWndParent;
}
HHBUI::UIWnd* HHBUI::UIWnd::GetInstance()
{
    return (UIWnd*)m_UIWindow;
}
void HHBUI::UIWnd::GetClientRect(ExRectF& lpClientRect)
{
    lpClientRect.right = m_data.size.right / UIWinApi::ToList.drawing_default_dpi;
    lpClientRect.bottom = m_data.size.bottom / UIWinApi::ToList.drawing_default_dpi;
}

void HHBUI::UIWnd::GetRect(ExRectF& lptRect)
{
    lptRect = m_data.size;
}

HHBUI::UIWnd* HHBUI::UIWnd::GetFromUIWnd(HWND hWnd)
{
    return (UIWnd*)SendMessageW(hWnd, UIWinApi::ToList.dwMessage, 0, MAKELONG(EMT_DUI, 0));
}

LPARAM HHBUI::UIWnd::GetlParam()
{
    return m_data.lParam;
}
void HHBUI::UIWnd::SetlParam(LPARAM dwlParam)
{
    m_data.lParam = dwlParam;
}
BOOL HHBUI::UIWnd::Show(INT nCmdShow)
{
    return_if_false(m_UIWindow, {}, false);
    m_data.dwFlags = m_data.dwFlags | EWF_INTED;
    if (FLAGS_CHECK(m_data.dwUIStyle, UISTYLE_POPUPWINDOW))
    { //弹出窗不获取焦点
        nCmdShow = SW_SHOWNOACTIVATE;
    }
    if (::GetFocus() == m_data.hWnd)
    {
        FLAGS_ADD(m_data.dwFlags, EWF_ACTIVE);
    }
    ShowWindow(m_data.hWnd, nCmdShow);
    auto atomName = GetClassLongPtrW(m_data.hWnd, GCW_ATOM);
    if (atomName == ATOM_DIALOG) //判断是模态窗口,添加模态窗口风格
    {
        SetFocus(m_data.hWnd);
    }
    InvalidateRect(m_data.hWnd, NULL, FALSE);
    return UpdateWindow(m_data.hWnd);
}
BOOL HHBUI::UIWnd::IsVisible()
{
    return IsWindowVisible(m_data.hWnd);
}
BOOL HHBUI::UIWnd::IsDisabled()
{
    return !IsWindowEnabled(m_data.hWnd);
}
void HHBUI::UIWnd::SetMinTrackSize(INT MinWidth, INT MinHeight)
{
    m_data.MinTrackSize.x = MinWidth + m_data.size_tmp * 2;
    m_data.MinTrackSize.y = MinHeight + m_data.size_tmp * 2;
}
BOOL HHBUI::UIWnd::SetText(LPCWSTR lpString, BOOL fTleRedraw)
{
    int nError = 0;
    if (((m_data.dwUIStyle & UISTYLE_TITLE) == UISTYLE_TITLE))
    {
        auto iter = m_hTableObjects.find(std::to_wstring(UISTYLE_TITLE));
        if (iter != m_hTableObjects.end())
        {
            auto titles = (UIControl*)iter->second;
            titles->SetText(lpString);
            nError = TRUE;
        }

    }
    if (fTleRedraw)
    {
        return nError;
    }
    return SetWindowTextW(m_data.hWnd, lpString);
}
INT HHBUI::UIWnd::GetKeys()
{
    INT ret = 0;
    if ((GetAsyncKeyState(VK_CONTROL) & 32768) != 0)
    {
        ret = ret | 1;
    }
    if ((GetAsyncKeyState(VK_SHIFT) & 32768) != 0)
    {
        ret = ret | 2;
    }
    if ((GetAsyncKeyState(VK_LWIN) & 32768) != 0)
    {
        ret = ret | 3;
    }
    if ((GetAsyncKeyState(VK_RWIN) & 32768) != 0)
    {
        ret = ret | 4;
    }
    if ((GetAsyncKeyState(VK_MENU) & 32768) != 0)
    {
        ret = ret | 5;
    }
    if ((GetAsyncKeyState(VK_TAB) & 32768) != 0)
    {
        ret = ret | 6;
    }
    if ((GetAsyncKeyState(VK_RETURN) & 32768) != 0)
    {
        ret = ret | 7;
    }
    if ((GetAsyncKeyState(VK_DELETE) & 32768) != 0)
    {
        ret = ret | 8;
    }
    if ((GetAsyncKeyState(VK_LEFT) & 32768) != 0)
    {
        ret = ret | 9;
    }
    if ((GetAsyncKeyState(VK_UP) & 32768) != 0)
    {
        ret = ret | 10;
    }
    if ((GetAsyncKeyState(VK_RIGHT) & 32768) != 0)
    {
        ret = ret | 11;
    }
    if ((GetAsyncKeyState(VK_DOWN) & 32768) != 0)
    {
        ret = ret | 12;
    }
    return ret;
}
INT HHBUI::UIWnd::Flags(INT dwFlags)
{
    if (dwFlags == 0)
        return m_data.dwFlags;
    m_data.dwFlags = dwFlags;
    return 0;
}
HHBUI::ti_s* HHBUI::UIWnd::GetToolrack(BOOL iRrack)
{
    if (iRrack)
        return m_data.toolrack;
    else
        return m_data.toolauto;
}
LPCWSTR HHBUI::UIWnd::GetText(BOOL fTleGet)
{
    if (fTleGet && m_data.objCaption)
    {
        LPVOID titles = ((UICombutton*)m_data.objCaption.load())->FindUIView(std::to_wstring(UISTYLE_TITLE).c_str());
        if (titles)
        {
            auto objtitles = (UIControl*)titles;
            return objtitles->GetText();
        }
        return nullptr;
    }
    else
    {
        int textLen = GetWindowTextLengthW(m_data.hWnd);
        wchar_t* buffer = new wchar_t[textLen + 1];
        GetWindowTextW(m_data.hWnd, buffer, textLen + 1);
        return buffer;
    }
}
LPVOID HHBUI::UIWnd::FindUIView(LPCWSTR lpName)
{
    auto iter = m_hTableObjects.find(lpName);
    if (iter != m_hTableObjects.end())
        return iter->second;
    return nullptr;
}

INT HHBUI::UIWnd::GetChildrenCout() const
{
    return (INT)m_hTableObjects.size();
}

LRESULT HHBUI::UIWnd::OnWndProc(HK_THUNK_DATA* pData, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    HWND hWnd = pData->hWnd;
    WNDPROC pOld = pData->Proc;
    auto window = (UIWnd*)pData->dwData;
    if (window)
    {
        if (uMsg == WM_TIMER)
        {
            for (auto& TimerInfo : window->m_data.vecTimers)
            {
                if (NULL == TimerInfo.pPropObj) continue;

                if (TimerInfo.hWnd == window->m_data.hWnd
                    && TimerInfo.uWinTimer == wParam
                    && TimerInfo.bKilled == false)
                {
                    if (TimerInfo.pPropObj->m_UIWindow)
                    {
                        if (wParam == (size_t)window + TIMER_BKG)
                        {
                            auto lpBI = window->m_data.lpBackgroundImage.load();
                            if (lpBI != 0)
                            {
                                INT iCur = lpBI->curFrame + 1;
                                INT iMax = lpBI->maxFrame - 1;
                                if (iCur > iMax)
                                {
                                    iCur = 0;
                                }
                                auto img = lpBI->hImage;
                                img->SetCurFrame(iCur);
                                lpBI->curFrame = iCur;

                                window->Update();
                                UpdateWindow(hWnd);
                            }
                        }
                        else
                            window->SendMsgProc(uMsg, TimerInfo.nLocalID, lParam);
                    }
                    else
                        ((UIControl*)TimerInfo.pPropObj->m_UIView)->OnBaseProc(hWnd, uMsg, TimerInfo.nLocalID, lParam);

                    break;
                }
            }
        }
        else if (window->SendMsgProc(uMsg, wParam, lParam) == S_FALSE)
        {
            return S_FALSE;
        }
        if (uMsg == WM_CANCELMODE)
        {
            if (window->m_data.dstImg)
            {
                delete window->m_data.dstImg;
                window->m_data.dstImg = nullptr;
            }
            UIImage* dstImg = nullptr;
            window->ToImage(&dstImg, TRUE, 10.f);
            window->m_data.dstImg = dstImg;
            //window->m_data.dstImg.store(dstImg, std::memory_order_release);
            window->Update();
            return S_OK;
        }
        else if (uMsg == WM_ENABLE)
        {
            if (wParam)
            {
                if (window->m_data.dstImg)
                {
                    delete window->m_data.dstImg;
                    window->m_data.dstImg = nullptr;
                }
            }
            return S_OK;
        }
        else if (uMsg == WM_CONTEXTMENU)
        {
            window->wm_menucontext(uMsg, wParam, lParam);
        }
        else if (uMsg == WM_NCLBUTTONDOWN)
        {
            window->wm_popupclose();
        }
        else if (uMsg == UIWinApi::ToList.dwMessage)
        {
            INT16 nType = LOWORD(lParam);
            if (nType == EMT_DUI)
            {
                return (size_t)window;
            }
            else if (nType == EMT_OBJECT)
            {
                auto lpData = (mempoolmsg_s*)wParam;
                if (lpData)
                {
                    ((UIControl*)lpData->pObj)->OnBaseProc(hWnd, lpData->uMsg, lpData->wParam, lpData->lParam);
                    delete lpData;
                }
            }
            return S_OK;
        }
        else if (uMsg == WM_CLOSE)
        {
            if (window->m_data.hWndParent)
            {
                if (GetActiveWindow() != window->m_data.hWndParent)
                {
                    SetActiveWindow(window->m_data.hWndParent);
                }
            }
        }
        else if (uMsg == WM_DESTROY)
        {
            window->m_data.dwFlags |= EWF_BDESTROYWINDOW;
            // 处理弹出窗口相关的清理
            auto pWnd = window->m_data.hParent.load();
            if (FLAGS_CHECK(window->m_data.dwUIStyle, UISTYLE_POPUPWINDOW)) {
                if (pWnd) {
                    FLAGS_DEL(pWnd->m_data.dwFlags, EWF_BPOPUPWINDOIWSHOWN);
                    pWnd->m_data.hWndPopup = 0;
                    auto pObj = (UIControl*)pWnd->m_data.lpPopupParams.load();
                    if (pObj) {
                        ImmAssociateContext(pWnd->m_data.hWnd, NULL);
                    }
                }
            }
            // 处理消息框相关的清理
            if ((window->m_data.dwUIStyle & EWS_MESSAGEBOX) == EWS_MESSAGEBOX) {
                if (window->m_data.lpMsgParams != nullptr) {
                    auto CheckBoxObj = (UICheck*)window->m_data.lpMsgParams->lpCheckBoxObj;
                    if (CheckBoxObj) {
                        __set_int(window->m_data.lpMsgParams->lpCheckBoxChecked, 0, CheckBoxObj->GetState());
                    }
                }
            }
            // 清理菜单和焦点对象
            window->m_data.pMenuPrevWnd = nullptr;
            window->m_data.pMenuTrackWnd = nullptr;
            window->m_data.pMenuHostWnd = nullptr;
            window->m_data.objTrack = nullptr;
            window->m_data.objFocus = nullptr;
            window->m_data.objHittest = nullptr;
            window->m_data.objHittestPrev = nullptr;
            window->m_data.objFocusPrev = nullptr;
            window->m_data.objTrackPrev = nullptr;
            // 清理窗口的子控件指针
            window->m_objChildLast = nullptr;
            window->m_objChildFirst = nullptr;

            // 清理消息处理函数
            window->m_data.pfnMsgProc = nullptr;
            // 销毁子控件
            auto objChildFirst = (UIControl*)window->m_objChildFirst;
            if (objChildFirst) {
                objChildFirst->OnBrothers(WM_DESTROY, 0, 0, FALSE, FALSE);
            }
            // 销毁相关窗口和资源
            if (window->m_data.hWndTips) {
                DestroyWindow(window->m_data.hWndTips);
            }
            window->RemoveAllTimer();
            // 删除相关资源
            delete window->m_data.hPath_Brush;
            delete window->m_data.hrgn_client;
            delete window->m_data.hPath_radius;
            delete window->m_data.canvas_display;
            delete window->m_data.canvas_bkg;
            if (window->m_data.dsToast)
                delete (UIStatic*)window->m_data.dsToast.load();

            // 清理父窗口相关操作
            if (window->m_data.hWndParent) {
                EnableWindow(window->m_data.hWndParent, TRUE);
                InvalidateRect(window->m_data.hWndParent, NULL, FALSE);
            }

            // 重置背景图
            window->wm_setbackgImage(0, 0, 0, 0, 0, 0, 0);

            // 清除通知图标
            if (window->m_data.lpNid) {
                Shell_NotifyIconW(NIM_DELETE, (PNOTIFYICONDATAW)window->m_data.lpNid.load());
                ExMemFree(window->m_data.lpNid);
                window->m_data.lpNid = nullptr;
            }

            // 删除背景颜色资源
            window->m_data.crbkg = nullptr;

            // 清理背景图片资源
            window->wm_setbackgImage(0, 0, 0, 0, 0, 0, 0);
            // 取消输入法上下文
            ImmAssociateContext(window->m_data.hWnd, NULL);
            ImmDestroyContext(window->m_data.hImc);
            // 删除工具栏相关资源
            delete window->m_data.toolrack;
            delete window->m_data.toolauto;
            // 清空对象表
            window->m_hTableObjects.clear();
            window->m_hTableEvent.clear();
            window->m_data.hWnd = 0;
            window->m_UIWindow = nullptr;
            // 恢复原窗口过程
            SetWindowLongPtrW(hWnd, GWLP_WNDPROC, (size_t)pOld);

            // 释放动态分配的内存
            VirtualFree(pData, 0, MEM_RELEASE);
            return S_OK;
        }
        else if (uMsg == WM_NCHITTEST)
        {
            return window->wm_nchittest(lParam);
        }
        else if (uMsg == WM_SETCURSOR) //32
        {
            if (window->wm_setcursor(lParam))
            {
                return S_FALSE;
            }
        }
        else if (uMsg == WM_ERASEBKGND)
        {
            return S_FALSE;
        }
        else if (uMsg == WM_SIZE)
        {
            INT width = GET_X_LPARAM(lParam);
            INT height = GET_Y_LPARAM(lParam);
            window->wm_size(wParam, width, height);
        }
        else if (uMsg == WM_WINDOWPOSCHANGING) //70
        {
            if (((window->m_data.dwUIStyle & EWS_MENU) == EWS_MENU))
            {
                auto pos = (tagWINDOWPOS*)lParam;
                if (!(pos->flags & SWP_NOSIZE))
                {
                    pos->cy += UIEngine::fScale(4);
                }
                if ((pos->flags & SWP_NOMOVE) != SWP_NOMOVE) //被移动了
                {
                    if (GetWindow(hWnd, GW_OWNER) != 0) //子菜单
                    {

                        window->m_data.dwFlags -= (window->m_data.dwFlags & EWF_BMENUREPOSTION);

                        window->wm_menu_setpos(hWnd, pos);
                    }
                    window->m_data.size.left = pos->x;
                    window->m_data.size.top = pos->y;
                    if ((window->m_data.dwFlags & EWF_BMENUINITED) != EWF_BMENUINITED)
                    {
                        window->m_data.dwFlags |= EWF_BMENUINITED;
                        window->wm_menu_createitems(hWnd);

                        SetWindowLongPtrW(hWnd, GWL_STYLE, WS_POPUP | WS_VISIBLE);
                        InvalidateRect(hWnd, NULL, FALSE);
                    }
                    return 0;
                }
            }
        }
        else if (uMsg == WM_WINDOWPOSCHANGED) //71
        {
            auto pos = (tagWINDOWPOS*)lParam;
            if (!(pos->flags & SWP_NOMOVE))
            {
                window->m_data.size.left = pos->x;
                window->m_data.size.top = pos->y;
            }
        }
        else if (uMsg == WM_NCCALCSIZE) //131
        {
            return 0;
        }
        else if (uMsg == WM_NCACTIVATE) //134
        {
            window->wm_leavecheck(window->m_data.objHittest, nullptr, lParam, TRUE);
            if (wParam == 0)
            {
                auto objFocus = window->m_data.objFocus.load();
                window->m_data.dwFlags -= (window->m_data.dwFlags & EWF_ACTIVE);
                window->m_data.objFocusPrev = objFocus;
                if (objFocus)
                    objFocus->KillFocus();
            }
            else
            {
                window->m_data.dwFlags |= EWF_ACTIVE;
                auto objFocus = window->m_data.objFocusPrev.load();
                if (objFocus)
                    objFocus->SetFocus();
                window->m_data.objFocusPrev = nullptr;
            }
            return S_FALSE; //防止出现边框/系统按钮
        }
        else if (uMsg == WM_NCPAINT) //133
        {
            return 0;
        }
        else if (uMsg == WM_MOUSEACTIVATE)
        {
            if (FLAGS_CHECK(window->m_data.dwUIStyle, UISTYLE_POPUPWINDOW))//防止弹出窗获取焦点
            {
                return MA_NOACTIVATE;
            }
        }
        else if (uMsg == WM_SHOWWINDOW) //24
        {
            if (wParam == 0)
            {
                auto objHittest = window->m_data.objHittest.load();
                if (objHittest)
                {
                    objHittest->DispatchNotify(WM_MOUSELEAVE, 0, 0);
                    window->m_data.objHittest = 0;
                    UpdateWindow(hWnd);
                }
            }
            InvalidateRect(hWnd, NULL, FALSE);
        }
        else if (uMsg == WM_PAINT) //15
        {
            window->OnPaintAndCalcControl();
        }
        else if (uMsg == WM_GETMINMAXINFO) //36
        {
            window->wm_getmmaxinfo(lParam);
            return 0;
        }
        else if (uMsg == WM_STYLECHANGING) //124
        {
            if (window->wm_stylechanging(wParam, lParam))
            {
                return 0;
            }
        }
        else if (uMsg == WM_MOUSEWHEEL) //522
        {
            if ((window->m_data.dwFlags & EWF_BPOPUPWINDOIWSHOWN))
            {
                return 0;
            }
            auto objHittest = (UIScroll*)window->m_data.objHittest.load();
            if (objHittest)
            {
                if (objHittest->OnBaseProc(hWnd, uMsg, wParam, lParam) == S_OK)
                {
                    if (lstrcmpW(objHittest->m_data.lpClsname, L"form-scroll") == 0)
                    {
                        objHittest->sb_parentnotify((wParam > 0 ? SB_LINEUP : SB_LINEDOWN), 0, 0);
                    }

                    if (((objHittest->m_data.dwStyle & eos_scroll_v) == eos_scroll_v))
                    {
                        objHittest->OnBaseProc(hWnd, WM_VSCROLL, (wParam > 0 ? SB_LINEUP : SB_LINEDOWN), 0);
                    }

                    if (((objHittest->m_data.dwStyle & eos_scroll_h) == eos_scroll_h))
                    {
                        objHittest->OnBaseProc(hWnd, WM_HSCROLL, (wParam > 0 ? SB_LINEUP : SB_LINEDOWN), 0);
                    }
                }
            }
        }
        else if (uMsg >= WM_MOUSEFIRST && uMsg < WM_MOUSELAST) //512 - 526
        {
            window->wm_mouse(uMsg, wParam, lParam);
        }
        else if (uMsg >= WM_KEYFIRST && uMsg <= WM_KEYLAST) //256,264
        {
            window->wm_keyboard(uMsg, wParam, lParam);
        }
        else if (uMsg == WM_ENTERSIZEMOVE) //561
        {
            window->m_data.dwFlags = window->m_data.dwFlags | EWF_BSIZEMOVING;

            // 开始窗口移动/调整大小时的吸附处理
            POINT cursorPos;
            GetCursorPos(&cursorPos);
            window->wm_snap_on_move_start(cursorPos);
        }
        else if (uMsg == WM_EXITSIZEMOVE) //562
        {
            window->m_data.dwFlags = window->m_data.dwFlags - (window->m_data.dwFlags & EWF_BSIZEMOVING);

            // 结束窗口移动/调整大小时的吸附处理
            window->wm_snap_on_move_end();
        }
        else if (uMsg == WM_MOVING) //534
        {
            // 窗口移动过程中的吸附处理
            POINT cursorPos;
            GetCursorPos(&cursorPos);
            window->wm_snap_on_moving(cursorPos);
        }
        else if (uMsg == WM_MEASUREITEM) //44
        {
            if (window->wm_measureitem_host(wParam, lParam))
            {
                return 1;
            }
        }
        else if (uMsg == WM_INITDIALOG) //272
        {
            if (((window->m_data.dwUIStyle & EWS_MESSAGEBOX) == EWS_MESSAGEBOX))
            {
                HHBUI::UIhook::hook_api_msgbox_initdialog(hWnd, window, wParam, lParam);
                return CallWindowProcW(pOld, hWnd, uMsg, wParam, lParam);
            }
        }
        else if (uMsg == WM_GETDLGCODE) //135
        {
            if (((window->m_data.dwFlags & EWF_BMODAL) == EWF_BMODAL))
            {
                return (DLGC_WANTARROWS | DLGC_WANTTAB | DLGC_WANTALLKEYS | DLGC_WANTMESSAGE | DLGC_HASSETSEL | DLGC_WANTCHARS);
            }
        }
        else if (uMsg == WM_INITMENUPOPUP) //279
        {
            if (((window->m_data.dwUIStyle & EWS_MENU) == EWS_MENU))
            {
                return 0;
            }
            window->wm_initmenupopup((HMENU)wParam);
        }
        else if (uMsg == WM_EXITMENULOOP) //530
        {
            if (((window->m_data.dwUIStyle & EWS_MENU) == EWS_MENU))
            {
                return 0;
            }
            window->m_data.dwFlags = window->m_data.dwFlags - (window->m_data.dwFlags & EWF_BMENUINITED);
            window->wm_nchittest(-1);
            window->wm_leavecheck(window->m_data.objHittest, nullptr, lParam, TRUE);
            window->m_data.dx_counts = 0;
        }
        else if (uMsg == WM_CAPTURECHANGED) //533
        {
            window->wm_obj_untrack(lParam, FALSE);
        }
        else if (uMsg == WM_SYSCOMMAND || uMsg == WM_COMMAND) //274,273
        {
            window->wm_command(uMsg, wParam, lParam);
        }
        else if (uMsg == WM_IME_COMPOSITION) //271
        {
            auto objHittest = window->m_data.objHittest.load();
            if (objHittest)
            {
                if (objHittest->OnBaseProc(hWnd, uMsg, wParam, lParam) == S_OK)
                {
                    window->wm_ime_composition();
                }
            }
         }
        else if (uMsg == WM_SETTEXT) //12
        {
        }
        else if (uMsg == 485) // MN_MSG_SELECTITEM
        {
            if (((window->m_data.dwFlags & EWF_BTRACKOBJECT) == EWF_BTRACKOBJECT))
            {
                POINT pt;
                GetCursorPos(&pt);
                window->wm_mouse(WM_MOUSEMOVE, 1, MAKELONG(pt.x - window->m_data.size.left, pt.y - window->m_data.size.top));
                return 0;
            }
            else
            {
                LONG_PTR item = 0;
                window->wm_menu_mouse(hWnd, WM_MOUSEMOVE, 0, (LONG_PTR*)&wParam);
                if (LODWORD(wParam) == -1)
                {
                    return 0;
                }
                else if(window->m_data.pMenuHostWnd)
                {
                    window->m_data.pMenuHostWnd.load()->m_data.pMenuPrevWnd = window;
                }
            }
        }
        else if (uMsg == 0x1E6) //MN_CANCELMENUS
        {
            auto pMenuHostWnd = window->m_data.pMenuHostWnd.load();
            if (pMenuHostWnd)
            {
                auto pMenuTrackWnd = (UIWnd*)window->m_data.pMenuTrackWnd;
                if (pMenuTrackWnd)
                {
                    if (((pMenuTrackWnd->m_data.dwFlags & EWF_BTRACKOBJECT) == EWF_BTRACKOBJECT))
                    {
                        pMenuTrackWnd->wm_obj_untrack(lParam, TRUE);
                        return CallWindowProcW(pOld, hWnd, 0x1EF, -1, 0);
                    }
                }
            }
        }
        else if (uMsg == 0x1ED) //MN_BUTTONDOWN
        {
            if (!window->wm_menu_mouse(hWnd, WM_LBUTTONDOWN, 1, (LONG_PTR*)&wParam))
            {
                return CallWindowProcW(pOld, hWnd, uMsg, -1, lParam);
            }

            if (((window->m_data.dwFlags & EWF_BTRACKOBJECT) != EWF_BTRACKOBJECT))
            {
                return CallWindowProcW(pOld, hWnd, uMsg, -1, lParam);
            }
        }
        else if (uMsg == 0x1EF) //MN_BUTTONUP
        {
            window->wm_menu_mouse(hWnd, WM_LBUTTONUP, 0, (LONG_PTR*)&wParam);
            if (window->m_data.objTrackPrev != window->m_data.objHittest)
            {
                return CallWindowProcW(pOld, hWnd, uMsg, -1, lParam);
            }
        }
        else if (uMsg == 0x01F1) //MN_DBLCLK
        {
            return 0;
        }
        else if (uMsg == WM_COPY)
        {
            if (window->wm_keyboard(uMsg, wParam, lParam))
            {
                return S_FALSE;
            }
        }
        else if (uMsg == WM_IME_CHAR)
        {
            if (window->wm_keyboard(uMsg, wParam, lParam))
            {
                return S_FALSE;
            }
        }
    }
    return CallWindowProcW(pOld, hWnd, uMsg, wParam, lParam);
}

BOOL HHBUI::UIWnd::RegClass(LPCWSTR lpwzClassName, HICON hIcon, HICON hIconsm)
{
    WNDCLASSEX wcx = { 0 };
    wcx.cbSize = sizeof(WNDCLASSEX);							// size of structure
    wcx.style = CS_HREDRAW | CS_VREDRAW | CS_DBLCLKS;			// redraw if size changes
    wcx.lpfnWndProc = _wnd_defwindowprocW;					    // points to window procedure
    wcx.cbClsExtra = 0;											// no extra class memory
    wcx.cbWndExtra = 0;											// no extra window memory
    wcx.hInstance = NULL;										// handle to instance
    wcx.hIcon = LoadIcon(NULL, IDI_APPLICATION);				// predefined app. icon
    wcx.hCursor = LoadCursor(NULL, IDC_ARROW);					// predefined arrow
    wcx.hbrBackground = (HBRUSH)GetStockObject(WHITE_BRUSH);	// white background brush
    wcx.lpszMenuName = NULL;									// name of menu resource
    wcx.lpszClassName = lpwzClassName;			        	// name of window class
    wcx.hIconSm = LoadIcon(NULL, IDI_APPLICATION);				// small class icon

    return RegisterClassExW(&wcx) != 0 || GetLastError() == ERROR_CLASS_ALREADY_EXISTS;
}

BOOL HHBUI::UIWnd::SetTimer(UIBase *pPropObj, size_t uTimerID, UINT uElapse)
{
    ASSERT(pPropObj != NULL);
    ASSERT(uElapse > 0);
    if (NULL == pPropObj->m_UIView && 0 == pPropObj->m_UIWindow) return false;

    for (auto& TimerInfo : m_data.vecTimers)
    {
        if (TimerInfo.pPropObj == pPropObj
            && TimerInfo.hWnd == m_data.hWnd
            && TimerInfo.nLocalID == uTimerID)
        {
            if (TimerInfo.bKilled == true)
            {
                if (::SetTimer(m_data.hWnd, TimerInfo.uWinTimer, uElapse, NULL))
                {
                    TimerInfo.bKilled = false;
                    return true;
                }

                return false;
            }

            return false;
        }
    }

    m_data.uTimerID = (++m_data.uTimerID) % 0xF0; //0xf1-0xfe特殊用途
    if (false == ::SetTimer(m_data.hWnd, m_data.uTimerID, uElapse, NULL)) return false;

    TimerInfo TimerInfo;
    TimerInfo.hWnd = m_data.hWnd;
    TimerInfo.pPropObj = pPropObj;
    TimerInfo.nLocalID = uTimerID;
    TimerInfo.uWinTimer = m_data.uTimerID;
    TimerInfo.bKilled = false;
    m_data.vecTimers.push_back(TimerInfo);

    return true;
}
BOOL HHBUI::UIWnd::SetTimer(size_t uTimerID, UINT uElapse)
{
    return SetTimer(this, uTimerID, uElapse);
}
BOOL HHBUI::UIWnd::FindTimer(UIBase *pPropObj, size_t uTimerID)
{
    ASSERT(pPropObj != NULL);
    if (NULL == pPropObj->m_UIView && 0 == pPropObj->m_UIWindow) return false;

    auto FindIt = find_if(m_data.vecTimers.begin(), m_data.vecTimers.end(), [&](TimerInfo& TimerInfo)
        {
            return TimerInfo.pPropObj == pPropObj
                && TimerInfo.hWnd == m_data.hWnd
                && TimerInfo.nLocalID == uTimerID
                && false == TimerInfo.bKilled;
        });

    return FindIt != m_data.vecTimers.end();
}

BOOL HHBUI::UIWnd::KillTimer(UIBase *pPropObj, size_t uTimerID)
{
    ASSERT(pPropObj != NULL);
    if (NULL == pPropObj->m_UIView && 0 == pPropObj->m_UIWindow) return false;

    for (auto& TimerInfo : m_data.vecTimers)
    {
        if (TimerInfo.pPropObj == pPropObj
            && TimerInfo.hWnd == m_data.hWnd
            && TimerInfo.nLocalID == uTimerID)
        {
            if (TimerInfo.bKilled == false)
            {
                if (::IsWindow(m_data.hWnd)) ::KillTimer(TimerInfo.hWnd, TimerInfo.uWinTimer);

                TimerInfo.bKilled = true;

                return true;
            }
        }
    }

    return false;
}

BOOL HHBUI::UIWnd::KillTimer(UIBase *pPropObj)
{
    ASSERT(pPropObj != NULL);
    if (NULL == pPropObj->m_UIView && 0 == pPropObj->m_UIWindow) return false;

    for (auto i = (int)m_data.vecTimers.size() - 1; i >= 0; i--)
    {
        TimerInfo TimerInfo = (m_data.vecTimers[i]);

        if (TimerInfo.pPropObj == pPropObj)
        {
            if (false == TimerInfo.bKilled) ::KillTimer(TimerInfo.hWnd, TimerInfo.uWinTimer);

            m_data.vecTimers.erase(m_data.vecTimers.begin() + i);
        }
    }

    return false;
}

BOOL HHBUI::UIWnd::KillTimer()
{
    return KillTimer(this);
}

BOOL HHBUI::UIWnd::RemoveAllTimer()
{
    for (auto& TimerInfo : m_data.vecTimers)
    {
        if (TimerInfo.bKilled == false)
        {
            if (::IsWindow(m_data.hWnd)) ::KillTimer(m_data.hWnd, TimerInfo.uWinTimer);
        }
    }

    m_data.vecTimers.clear();

    return true;
}

LRESULT HHBUI::UIWnd::SendMsg(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    return SendMessageW(m_data.hWnd, uMsg, wParam, lParam);
}

LRESULT HHBUI::UIWnd::SendMsgProc(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    if (m_data.pfnMsgProc != nullptr)
    {
        return m_data.pfnMsgProc(m_data.hWnd, this, m_data.objHittest, (m_data.objHittest ? m_data.objHittest.load()->GetID() : 0), uMsg, wParam, lParam);
    }
    return S_OK;
}

INT HHBUI::UIWnd::wm_nchittest(LPARAM lParam)
{
    INT dwHitCode = HTCLIENT;
    if (((m_data.dwFlags & EWF_BTRACKOBJECT) == EWF_BTRACKOBJECT))
        return dwHitCode;

    if (!((m_data.dwUIStyle & EWS_MENU) == EWS_MENU))
    {
        if (((m_data.dwFlags & EWF_BMENUINITED) == EWF_BMENUINITED))
        {
            return dwHitCode;
        }
    }
    POINT pt;
    UIControl* objMouse = nullptr;
    if (lParam == -1)
    {
        GetCursorPos(&pt);
    }
    else
    {
        pt.x = GET_X_LPARAM(lParam);
        pt.y = GET_Y_LPARAM(lParam);
    }
    ScreenToClient(m_data.hWnd, &pt);

    if (m_data.hrgn_client && m_data.hrgn_client.load()->HitTest(pt.x, pt.y) == S_OK || m_data.dwWinState == 2)
    {
        wm_nchittestlist((UIControl*)m_objChildLast, pt.x, pt.y, &dwHitCode, objMouse);
        if (objMouse)
        {
            m_data.dwHitCode = dwHitCode;
            if (objMouse == m_data.objCaption)
            {
                dwHitCode = HTCAPTION;
            }
            else if (objMouse->m_data.nID == UISTYLE_BTN_MAX)
            {
                // dwHitCode = HTMAXBUTTON;
            }
            else
            {
                if ((objMouse->m_data.dwStyle & eos_disabled) == eos_disabled)
                {

                    if ((m_data.dwUIStyle & UISTYLE_MOVEABLE) == UISTYLE_MOVEABLE) //允许随意移动
                    {
                        dwHitCode = HTCAPTION;
                    }
                    objMouse = nullptr;
                }
            }

        }
        else
        {
            if (((m_data.dwUIStyle & UISTYLE_MOVEABLE) == UISTYLE_MOVEABLE)) //允许随意移动
            {
                dwHitCode = HTCAPTION;
            }

        }
    }
    else if (m_data.hrgn_client)
    {
        //在尺寸边框内

        if (((m_data.dwUIStyle & UISTYLE_SIZEABLE) == UISTYLE_SIZEABLE)) //允许调整尺寸
        {
            dwHitCode = _wnd_wm_nchittest_sizebox(m_data.size.right, m_data.size.bottom, pt.x, pt.y, m_data.hrgn_client.load()->m_size.left);
        }
        else
        {
            dwHitCode = HTCAPTION;
        }
    }
    auto objHittest = m_data.objHittest.load();
    m_data.objHittest = objMouse;
    if (objHittest != objMouse)
    {
        m_data.objHittestPrev = objHittest;
        dwHitCode = HTCLIENT;
    }
    return dwHitCode;
}
void HHBUI::UIWnd::OnPaintAndCalcControl()
{
    PAINTSTRUCT ps = { 0 };
    if (((m_data.dwFlags & EWF_INTED) == EWF_INTED))
    {

        BOOL fSized = ((m_data.dwFlags & EWF_SIZED) == EWF_SIZED);
        if (fSized)
        {
            m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & EWF_SIZED);
        }

        BOOL fLayer = ((m_data.dwFlags & EWF_BLAYERED) == EWF_BLAYERED);

        if (fLayer)
        {
            if (fSized)
            {
                ps.rcPaint.right = m_data.size.right;
                ps.rcPaint.bottom = m_data.size.bottom;
            }
            else
            {
                GetUpdateRect(m_data.hWnd, &ps.rcPaint, FALSE);

            }
        }
        else
        {
            BeginPaint(m_data.hWnd, &ps);
        }

        OnPaintAndRender(ps.hdc, ps.rcPaint, fLayer);
        if (!fLayer)
        {
            EndPaint(m_data.hWnd, &ps);
        }
    }
}
void HHBUI::UIWnd::wm_size(WPARAM wParam, INT width, INT height)
{
    m_data.dwFlags = m_data.dwFlags | EWF_SIZED | EWF_BREDRAWBACKGROUND;
    if (width != m_data.size.right || height != m_data.size.bottom)
    {

        if (((m_data.dwUIStyle & EWS_MESSAGEBOX) == EWS_MESSAGEBOX))
        {
            _wnd_addstyle(m_data.hWnd, WS_EX_LAYERED, TRUE);
        }
        if (((m_data.dwUIStyle & EWS_MENU) == EWS_MENU))
        {
            width -= GetSystemMetrics(SM_CXFIXEDFRAME) * 2;
            height -= GetSystemMetrics(SM_CYFIXEDFRAME) * 2;
            width += m_data.size_tmp * 2;
            height += m_data.size_tmp * UIEngine::fScale(2);
        }
    }
    wm_recalcclient(width, height);
    auto objCaption = (UICombutton*)m_data.objCaption.load();
    if (objCaption)
    {
        if (wParam == SIZE_MAXIMIZED)
        {
            m_data.radius = 0;
            m_data.size_tmp = 0;
            objCaption->SetRadius(0, 0, 0, 0);


            if (m_data.hWndParent != 0)
                Move(0, 0);
        }
        else if (wParam == SIZE_RESTORED)
        {
            m_data.radius = m_data.radius_r;
            m_data.size_tmp = m_data.size_shadow;
            objCaption->SetRadius(m_data.radius, m_data.radius, 0, 0);

        }
        auto rad = UIEngine::fScale(m_data.size_tmp);
        RECT rcCaption{ 0 };
        wm_calc_captionrect(rcCaption);
        objCaption->SetPos(rad, rad, (rcCaption.right - rcCaption.left - rad) - rad, rcCaption.bottom - rcCaption.top,
            0, SWP_NOZORDER | SWP_NOACTIVATE | SWP_NOCOPYBITS | SWP_EX_NODPISCALE);

        if (m_data.dwWinState != wParam)
        {
            if (((m_data.dwUIStyle & UISTYLE_BTN_MAX) == UISTYLE_BTN_MAX))
            {
                auto objmax = (UIControl*)objCaption->FindUIView(std::to_wstring(UISTYLE_BTN_MAX).c_str());
                if (objmax)
                {
                    objmax->Redraw();
                }
            }

            if (((m_data.dwUIStyle & UISTYLE_BTN_MIN) == UISTYLE_BTN_MIN))
            {
                auto objmin = (UIControl*)objCaption->FindUIView(std::to_wstring(UISTYLE_BTN_MIN).c_str());
                if (objmin)
                {
                    objmin->Redraw();
                }
            }
            m_data.dwWinState = wParam;
        }
    }
    Layout_Update();
    InvalidateRect(m_data.hWnd, 0, FALSE);
}
void HHBUI::UIWnd::wm_recalcclient(INT width, INT height)
{
    m_data.size.right = width;
    m_data.size.bottom = height;
    if (SUCCEEDED(m_data.canvas_display.load()->Resize(width, height)))
    {
        if (SUCCEEDED(m_data.canvas_bkg.load()->Resize(width, height))) //客户区被改变,必定触发背景被改变事件
        {
            m_data.dwFlags = m_data.dwFlags | EWF_BREDRAWBACKGROUND;
        }
    }
    float nSizebox = 0.f;
    if (((m_data.dwUIStyle & UISTYLE_SIZEABLE) == UISTYLE_SIZEABLE))
    {
        nSizebox = UIEngine::fScale(15);
    }
    ExRectF rc = { nSizebox, nSizebox, (float)width - nSizebox, (float)height - nSizebox };
    if (m_data.hrgn_client)
    {
        if (!m_data.hrgn_client.load()->m_size.operator==(rc))
        {
            delete m_data.hrgn_client;
            m_data.hrgn_client = nullptr;
        }
    }
    auto setoff = UIEngine::fScale(m_data.size_tmp);
    if (!m_data.hrgn_client)
        m_data.hrgn_client = new UIRegion(rc.left, rc.top, rc.right, rc.bottom, setoff, setoff, setoff, setoff);

}
void HHBUI::UIWnd::OnPaintAndRender(LPVOID hDC, RECT rcPaint, BOOL fLayer)
{
    m_data.dwFlags = m_data.dwFlags | EWF_BRENDERING;
    if ((m_data.dwFlags & EWF_BREDRAWBACKGROUND) == EWF_BREDRAWBACKGROUND)
    {
        m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & EWF_BREDRAWBACKGROUND);
        wm_ps_bkg();
        if (m_data.hPath_Brush)
            delete m_data.hPath_Brush;
        m_data.hPath_Brush = new UIBrush(m_data.canvas_bkg, BrushModeEx::Default, m_data.alpha);
        m_data.hPath_radius.load()->Reset();
        m_data.hPath_radius.load()->BeginPath();
        if (SUCCEEDED(m_data.hPath_radius.load()->StartFigure(0, 0)))
        {
            auto setoff = UIEngine::fScale(m_data.size_tmp);
            auto rad = UIEngine::fScale(m_data.radius);
            if (m_data.radius)
                m_data.hPath_radius.load()->AddCustomRoundRect(setoff, setoff, m_data.size.right - setoff, m_data.size.bottom - setoff, rad, rad, rad, rad);
            else
                m_data.hPath_radius.load()->AddRect(setoff, setoff, m_data.size.right - setoff, m_data.size.bottom - setoff);
        }
        m_data.hPath_radius.load()->EndPath();

    }
    UIDrawContext::ToList.d2d_dc->SetTarget((ID2D1Bitmap*)m_data.canvas_display.load()->GetContext(3));
    if (SUCCEEDED(m_data.canvas_display.load()->BeginDraw()))
    {
        //处理背景
        m_data.canvas_display.load()->Clear();
        if (m_data.dstImg)
            m_data.canvas_display.load()->DrawImage(m_data.dstImg, 0, 0);
        else
        {
            m_data.canvas_display.load()->FillPath(m_data.hPath_Brush, m_data.hPath_radius);
            wm_renderPresent((UIControl*)m_objChildFirst, rcPaint, 0, 0);
            if (m_data.size_tmp != 0)
            {
                auto setoff = UIEngine::fScale(m_data.size_tmp);
                auto rad = UIEngine::fScale(m_data.radius);
                // 渲染阴影部分
                if (m_data.crshadow && !FLAGS_CHECK(m_data.dwUIStyle, UISTYLE_NOSHADOW))
                {
                    m_data.canvas_display.load()->DrawShadow(m_data.crshadow.get(), setoff, setoff,
                        m_data.size.right - setoff, m_data.size.bottom - setoff,
                        setoff, rad, rad, rad, rad);
                }

                // 渲染边框
                if (m_data.crBorder)
                {
                    m_data.canvas_display.load()->DrawRoundRect(m_data.crBorder.get(), setoff, setoff,
                        m_data.size.right - setoff, m_data.size.bottom - setoff,
                        rad, m_data.size_Border);
                }
            }
        }
        HDC mDC = nullptr;
        if (SUCCEEDED(m_data.canvas_display.load()->GetDC(&mDC)))
        {
            POINT ptDest = { (LONG)m_data.size.left, (LONG)m_data.size.top };
            POINT point = { 0,0 };
            SIZE pSize = { (LONG)m_data.size.right, (LONG)m_data.size.bottom };
            RECT pDirty = { rcPaint.left, rcPaint.top, rcPaint.right, rcPaint.bottom };
            BLENDFUNCTION bf = { AC_SRC_OVER, 0, 255, AC_SRC_ALPHA };
            UPDATELAYEREDWINDOWINFO ulwi_{};
            ulwi_.crKey = 0;
            ulwi_.hdcDst = 0;
            ulwi_.hdcSrc = mDC;
            ulwi_.dwFlags = ULW_ALPHA;
            ulwi_.pblend = &bf;
            ulwi_.pptDst = &ptDest;
            ulwi_.pptSrc = &point;
            ulwi_.prcDirty = &pDirty;
            ulwi_.psize = &pSize;
            ulwi_.cbSize = sizeof(ulwi_);
            UIWinApi::ToList.UpdateLayeredWindowIndirect(m_data.hWnd, &ulwi_);
            m_data.canvas_display.load()->ReleaseDC();
        }
        m_data.canvas_display.load()->EndDraw();
    }
    m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & EWF_BRENDERING);
}
void HHBUI::UIWnd::wm_renderPresent(UIControl* objChildFirst, ExRectF rcPaint, INT offsetX, INT offsetY)
{
    UIControl* objNext = objChildFirst;
    while (objNext) {
        auto& data = objNext->m_data;
        auto rcObj = data.Frame;
        rcObj.Offset(offsetX, offsetY);
        ExRectF rcClip = {};
        if (rcClip.IntersectRect(rcPaint, rcObj) && objNext->IsVisible()) {

            if (rcClip.right + m_data.size_tmp > m_data.size.right)
                rcClip.right -= m_data.size_tmp ? m_data.size_tmp + 10 : m_data.size_tmp;
            if (rcClip.bottom + m_data.size_tmp > m_data.size.bottom)
                rcClip.bottom -= m_data.size_tmp ? m_data.size_tmp + 10 : m_data.size_tmp;

            if ((data.dwFlags & EOF_BNEEDREDRAW) == EOF_BNEEDREDRAW) {
                data.dwFlags &= ~EOF_BNEEDREDRAW; // 清除重绘标志
                if (!data.Frame_d.empty()) {
                    if (objNext->m_data.pfnClsProc)
                    {
                        objNext->m_data.pfnClsProc(objNext->GethWnd(), WM_PAINT, 0, 0);
                    }
                    else
                    {
                        ps_context ps{};
                        if (objNext->BeginPaint(ps))
                        {
                            objNext->OnPaintProc(ps);
                        }
                        objNext->EndPaint();
                    }
                    //绘制结束后会清空当前 设置下一个上下文的目标
                    UIDrawContext::ToList.d2d_dc->SetTarget((ID2D1Bitmap*)m_data.canvas_display.load()->GetContext(3));
                }
            }

            if ((data.dwFlags & EOF_BPATH) == EOF_BPATH && data.hPath_Window) {
                if (data.hPathBrush)
                    delete data.hPathBrush;
                data.hPathBrush = new UIBrush(data.canvas, BrushModeEx::Default, data.alpha);
                if (data.hPathBrush) {
                    ExMatrix3x2 matrix;
                    matrix.Translate(data.Frame_w.left, data.Frame_w.top);
                    data.hPathBrush->SetTransForm(matrix);
                    m_data.canvas_display.load()->SetClipRect(rcClip.left, rcClip.top, rcClip.right, rcClip.bottom);
                    m_data.canvas_display.load()->FillPath(data.hPathBrush, data.hPath_Window);
                    m_data.canvas_display.load()->ResetClip();
                    matrix.Reset();
                }
            }
            else {
                CanvasDrawMode drawMode = ((data.dwStyleEx & eos_ex_composited) == eos_ex_composited)
                    ? CanvasDrawMode::Over
                    : CanvasDrawMode::Blend;

                m_data.canvas_display.load()->DrawCanvas(data.canvas, rcClip.left, rcClip.top, rcClip.right, rcClip.bottom,
                    rcClip.left - rcObj.left, rcClip.top - rcObj.top, drawMode, data.alpha);
            }

            UIControl* objChild = (UIControl*)objNext->m_objChildFirst;
            if (objChild) {
                wm_renderPresent(objChild, rcClip, rcObj.left, rcObj.top);
            }
        }

        objNext = data.objNext;
    }
}

void HHBUI::UIWnd::wm_ps_bkg()
{
    if (SUCCEEDED(m_data.canvas_bkg.load()->BeginDraw()))
    {
        UIWnd* p = this;
        if (SendMsgProc(WM_ERASEBKGND, (size_t)m_data.canvas_bkg.load(), MAKELONG(m_data.size.right, m_data.size.bottom)) == S_OK)
        {
            if ((m_data.dwUIStyle & UISTYLE_NOINHERITBKG) != UISTYLE_NOINHERITBKG)
            {
                auto pParent = m_data.hParent.load();
                if (pParent)
                {
                    p = pParent;
                }
            }
            if (m_data.fBlur != 0)
            {
                wm_ps_bkg(p);
                m_data.canvas_bkg.load()->blur(nullptr, m_data.fBlur, nullptr, 0, m_data.fmodesoft);
            }
            else
            {
                wm_ps_bkg(p);
            }
        }
        if (((m_data.dwUIStyle & EWS_MESSAGEBOX) == EWS_MESSAGEBOX))
        {
            HHBUI::UIhook::hook_api_msgbox_drawinfo(this, m_data.canvas_bkg, m_data.size.right, m_data.size.bottom);
        }

        m_data.canvas_bkg.load()->EndDraw();
    }
}
void HHBUI::UIWnd::wm_ps_bkg(UIWnd* p)
{
    //绘制背景
    if (p->m_data.lpBackgroundImage)
    {
        m_data.canvas_bkg.load()->Clear();
        auto bk = p->m_data.lpBackgroundImage.load();
        BOOL lpGrid = FALSE;
        if (bk->lpGrid)
        {
            if ((bk->lpGrid->left != bk->lpGrid->right) || (bk->lpGrid->top != bk->lpGrid->bottom))
                lpGrid = TRUE;
        }
        if (lpGrid)
        {
            info_GridsImage grids{};
            m_data.canvas_bkg.load()->DrawGridsImage(bk->hImage, bk->lpGrid->left, bk->lpGrid->top, bk->lpGrid->right, bk->lpGrid->bottom, &grids, bk->dwAlpha);
        }
        else if (bk->dwRepeat == bir_default)
        {
            m_data.canvas_bkg.load()->DrawImageRect(bk->hImage, bk->x, bk->y, m_data.size.right, m_data.size.bottom, ImageMode::Default, bk->dwAlpha);
        }
        else if (bk->dwRepeat == bir_epault)
        {
            uint32_t iw = 0, ih = 0;
            auto hImg = bk->hImage;
            if (hImg)
                hImg->GetSize(iw, ih);
            //iw ih=图片原始宽高，ww wh=窗口宽高，sw sh=缩放后宽高，x y=图片绘制坐标
            INT ww = m_data.size.right, wh = m_data.size.bottom, sw = 0, sh = 0, x = bk->x, y = bk->y;

            float sx = static_cast<float>(ww) / iw;
            float sy = static_cast<float>(wh) / ih;
            float sc = std::max(sx, sy);
            sw = iw * sc;
            sh = ih * sc;
            if (sw > ww) x = (ww - sw) / 2;
            else if (sh > wh) y = (wh - sh) / 2;
            m_data.canvas_bkg.load()->DrawImageRect(bk->hImage, x, y, x + sw, y + sh, ImageMode::Default, bk->dwAlpha);
        }
        else if (bk->dwRepeat == bir_epault_center)
        {
            m_data.canvas_bkg.load()->DrawImageRect(bk->hImage, 0, 0, m_data.size.right, m_data.size.bottom, ImageMode::ScaleFill, bk->dwAlpha);
        }
        else if (bk->dwRepeat == bir_no_repeat)
        {
            m_data.canvas_bkg.load()->DrawImage(bk->hImage, bk->x, bk->y, bk->dwAlpha);
        }
        else if (bk->dwRepeat == bir_repeat)
        {
            m_data.canvas_bkg.load()->DrawImageRect(bk->hImage, 0, 0, m_data.size.right, m_data.size.bottom, ImageMode::ScaleCenter, bk->dwAlpha);
        }
        else if (bk->dwRepeat == bir_repeat_center)
        {
            m_data.canvas_bkg.load()->DrawImageRect(bk->hImage, 0, 0, m_data.size.right, m_data.size.bottom, ImageMode::CenterMiddle, bk->dwAlpha);
        }
    }
    else
    {
        //绘制底色
        if (p->m_data.crbkg != 0)
        {
            m_data.canvas_bkg.load()->Clear(p->m_data.crbkg.get());
        }
    }
}
//限制窗口尺寸
void HHBUI::UIWnd::wm_getmmaxinfo(LPARAM lParam)
{
    MINMAXINFO* pMinMaxInfo = reinterpret_cast<MINMAXINFO*>(lParam);
    RECT rcMonitor = { 0 }, rcDesk = { 0 };

    UIWinApi::GetWndScreenRect(m_data.hWnd, rcMonitor, rcDesk);
    INT width = m_data.size.right;
    INT height = m_data.size.bottom;
    if (((m_data.dwUIStyle & UISTYLE_FULLSCREEN) == UISTYLE_FULLSCREEN)) // 如果是全屏模式
    {
        // 将窗口位置和尺寸设置为整个屏幕的位置和尺寸
        rcDesk = rcMonitor;
        output(rcDesk.right, rcDesk.bottom);
    }
    OffsetRect(&rcDesk, -rcMonitor.left, -rcMonitor.top);
    // 设置左边界和顶边界（不需要减去 1）
    pMinMaxInfo->ptMaxPosition.x = rcDesk.left;
    pMinMaxInfo->ptMaxPosition.y = rcDesk.top;

    // 计算最大宽度和高度
    INT nMaxWidth = rcDesk.right - rcDesk.left;
    INT nMaxHeight = rcDesk.bottom - rcDesk.top;

    // 设置最大宽度和高度
    pMinMaxInfo->ptMaxTrackSize.x = nMaxWidth;
    pMinMaxInfo->ptMaxTrackSize.y = nMaxHeight;

    // 设置允许调整的最小尺寸
    pMinMaxInfo->ptMinTrackSize.x = UIEngine::fScale(m_data.MinTrackSize.x);
    pMinMaxInfo->ptMinTrackSize.y = UIEngine::fScale(m_data.MinTrackSize.y);

    // 设置允许调整的最大尺寸为屏幕尺寸
    pMinMaxInfo->ptMaxSize.x = nMaxWidth;
    pMinMaxInfo->ptMaxSize.y = nMaxHeight;
}
BOOL HHBUI::UIWnd::wm_stylechanging(WPARAM wParam, LPARAM lParam)
{
    STYLESTRUCT* pStyleStruct = reinterpret_cast<STYLESTRUCT*>(lParam);
    BOOL ret = FALSE;
    INT styleNew = pStyleStruct->styleNew;
    INT styleDui = m_data.dwUIStyle;

    if (wParam == GWL_EXSTYLE)
    {

        if (((styleDui & EWS_MESSAGEBOX) == EWS_MESSAGEBOX))
        {
            m_data.dwFlags = m_data.dwFlags | EWF_BLAYERED;
        }
        else
        {

            if ((pStyleStruct->styleNew & WS_EX_LAYERED))
            {
                m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & EWF_BLAYERED);
            }
            else
            {

                if (((m_data.dwFlags & EWF_BLAYERED) == EWF_BLAYERED))
                {
                    m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & EWF_BLAYERED);
                }
            }
        }
        InvalidateRect(m_data.hWnd, NULL, FALSE);
    }
    else
    {
        styleNew = (styleNew & ~(WS_DLGFRAME | WS_BORDER));
        if ((styleDui & UISTYLE_BTN_MIN) == 0 || (styleDui & UISTYLE_FULLSCREEN) == UISTYLE_FULLSCREEN)
        {
            styleNew = (styleNew & ~WS_MINIMIZEBOX);
        }
        else
        {
            styleNew = styleNew | WS_MINIMIZEBOX;
        }

        if ((styleDui & UISTYLE_BTN_MAX) == 0 || (styleDui & UISTYLE_FULLSCREEN) == UISTYLE_FULLSCREEN)
        {
            styleNew = (styleNew & ~WS_MAXIMIZEBOX);
        }
        else
        {
            styleNew = styleNew | WS_MAXIMIZEBOX;
        }

        if ((styleDui & UISTYLE_FULLSCREEN) == UISTYLE_FULLSCREEN)
        {
            styleNew = (styleNew & ~WS_SYSMENU);
        }
        else
        {
            styleNew = styleNew | WS_SYSMENU;
        }

        if ((styleDui & UISTYLE_SIZEABLE) == UISTYLE_SIZEABLE)
        {
            styleNew = styleNew | WS_THICKFRAME;
        }
        else
        {
            styleNew = styleNew & ~WS_THICKFRAME;
        }

        if (styleNew != pStyleStruct->styleNew)
        {
            pStyleStruct->styleNew = styleNew;
            ret = TRUE;
        }
    }
    return ret;
}

void HHBUI::UIWnd::wm_nchittestlist(UIControl* objLast, INT x, INT y, INT* hitCode, UIControl*& objMouse)
{
    UIControl* objPrev = objLast;
    while (objPrev)
    {
        if (((objPrev->m_data.dwStyle & eos_hidden) != eos_hidden))
        {
            if (objPrev->m_data.Frame.PtInRect(x,y))
            {
                INT ox = x - objPrev->m_data.Frame.left;
                INT oy = y - objPrev->m_data.Frame.top;
                auto lParam = MAKELONG(ox, oy);
                if (((objPrev->m_data.dwStyle & eos_disabled) == eos_disabled)) //检测是否被禁止，被禁止组件还需检测是否可穿透
                {
                    *hitCode = objPrev->OnBaseProc(m_data.hWnd, WM_NCHITTEST, 0, lParam);
                    if (*hitCode == HTTRANSPARENT) //在穿透区域,则继续寻找
                    {
                        objPrev = objPrev->m_data.objPrev;
                        continue;
                    }
                    else
                    {
                        *hitCode = HTCLIENT; //不可穿透则直接停止寻找
                        objMouse = objPrev;
                        return;
                    }
                }
                else
                {
                    objLast = (UIControl*)objPrev->m_objChildLast;
                    if (objLast != 0)
                    {
                        UIControl* tmp = nullptr;
                        wm_nchittestlist(objLast, ox, oy, hitCode, tmp);
                        if (tmp && *hitCode != HTTRANSPARENT) //找到目标子组件
                        {
                            objMouse = tmp;
                            return;
                        }
                    }
                }
                *hitCode = objPrev->OnBaseProc(m_data.hWnd, WM_NCHITTEST, 0, lParam);
                if (*hitCode == HTTRANSPARENT) // 如果可穿透，则继续寻找
                {
                    objPrev = objPrev->m_data.objPrev;
                    continue;
                }
                else
                {
                    m_data.dwHitObjPos_Abs = lParam;
                    objMouse = objPrev;
                    return;
                }
            }
        }
        objPrev = objPrev->m_data.objPrev;
    }
}

BOOL HHBUI::UIWnd::wm_setcursor(LPARAM lParam)
{
    auto uHitCode = LOWORD(lParam);
    if (uHitCode == HTCLIENT)
    {
        auto objHittest = m_data.objHittest.load();
        if (objHittest && objHittest->OnBaseProc(m_data.hWnd, WM_SETCURSOR, m_data.dwHitObjPos_Abs, MAKELONG(m_data.dwHitCode, HIWORD(lParam))) == S_FALSE)
        {
            return TRUE; // 如果控件处理了光标设置，返回 TRUE
        }
    }
    else
    {
        static const std::unordered_map<WORD, LPCWSTR> cursorMap = {
      {HTCAPTION, IDC_ARROW},
      {HTTOPLEFT, IDC_SIZENWSE},
      {HTTOPRIGHT, IDC_SIZENESW},
      {HTTOP, IDC_SIZENS},
      {HTBOTTOMLEFT, IDC_SIZENESW},
      {HTBOTTOMRIGHT, IDC_SIZENWSE},
      {HTBOTTOM, IDC_SIZENS},
      {HTLEFT, IDC_SIZEWE},
      {HTRIGHT, IDC_SIZEWE}
        };
        auto it = cursorMap.find(uHitCode);
        if (it != cursorMap.end())
        {
            ::SetCursor(LoadCursorW(nullptr, it->second));
            return TRUE;
        }
    }
    return FALSE;
}


INT HHBUI::UIWnd::wm_popupclose()
{
    INT result = 0;
    if (FLAGS_CHECK(m_data.dwFlags, EWF_BPOPUPWINDOIWSHOWN))
    {
        if (m_data.lpPopupParams == 0 && IsWindowVisible(m_data.hWndPopup))
        {
            FLAGS_DEL(m_data.dwFlags, EWF_BPOPUPWINDOIWSHOWN);
            result = DestroyWindow(m_data.hWndPopup);
        }
    }
    return result;
}

void HHBUI::UIWnd::wm_leavecheck(UIControl* objCheck, UIControl* objHittest, LPARAM lParam, BOOL fTrack)
{
    if (!objHittest)
    {
        objHittest = m_data.objHittest;  // 默认使用 m_data.objHittest
    }

    if (objCheck == objHittest)
    {
        return;
    }

    // 如果 objCheck 存在且没有触发 WM_MOUSELEAVE
    if (objCheck && (m_data.dwFlags & EWF_BLEAVESENT) != EWF_BLEAVESENT)
    {
        m_data.dwFlags |= EWF_BLEAVESENT;
        objCheck->OnBaseProc(m_data.hWnd, WM_MOUSELEAVE, 0, lParam);  // 触发 WM_MOUSELEAVE
    }

    // 如果 objHittest 存在，触发 WM_MOUSEHOVER
    if (objHittest)
    {
        m_data.dwFlags &= ~EWF_BLEAVESENT;  // 清除 EWF_BLEAVESENT 标志
        m_data.objHittestPrev = objHittest;
        objHittest->OnBaseProc(m_data.hWnd, WM_MOUSEHOVER, 0, lParam);  // 触发 WM_MOUSEHOVER
    }

    // 如果启用了 fTrack，设置鼠标跟踪定时器
    if (objHittest && fTrack)
    {
        size_t nEvent = (size_t)this + TIMER_MOUSETRACK;
        ::KillTimer(m_data.hWnd, nEvent);
        ::SetTimer(m_data.hWnd, nEvent, 50, OnMouseTrack_Timer);
    }
}


void HHBUI::UIWnd::OnMouseTrack_Timer(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime)
{
    ::KillTimer(hWnd, idEvent);
    UIWnd* pWnd = (UIWnd*)(idEvent - TIMER_MOUSETRACK);
    if ((pWnd->m_data.dwFlags & EWF_BTRACKOBJECT) != EWF_BTRACKOBJECT)
    {
        POINT pt;
        GetCursorPos(&pt);
        if (WindowFromPoint(pt) != hWnd)
        {
            if ((pWnd->m_data.dwFlags & EWF_BLEAVESENT) != EWF_BLEAVESENT)
            {
                pWnd->m_data.dwFlags |= EWF_BLEAVESENT;
                auto objHit = pWnd->m_data.objHittestPrev.load();
                if (objHit)
                {
                    objHit->OnBaseProc(hWnd, WM_MOUSELEAVE, 0, 0); //发送离开
                    pWnd->m_data.objHittestPrev = pWnd->m_data.objHittest.load();
                    pWnd->m_data.objHittest = nullptr;
                }

            }
        }
    }
}
struct tips_s
{
    UINT uTimerID = 0;
    HHBUI::UIStatic* hObjly = nullptr;
    INT uTime = 0;
    INT dwMilliseconds = 0;
    HHBUI::UIColor crText;
    HHBUI::UIColor crBorder;
    HHBUI::UIColor crBackground;
    INT uType = 0;
};
LRESULT HHBUI::UIWnd::OnToastsMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
    auto window = (UIWnd*)pWnd;
    auto obj = (UIStatic*)UIView;
    if (uMsg == WM_TIMER)
    {
        tips_s* uDateTips = (tips_s*)obj->GetlParam();
        if (uDateTips)
        {
            if (uDateTips->uTime == uDateTips->dwMilliseconds)
            {
                uDateTips->dwMilliseconds = 0;
                uDateTips->uTime = 0;
                auto hObjly = (UIStatic*)uDateTips->hObjly;
                hObjly->Layout_DeleteChild(obj);
                hObjly->Layout_Update();

                if (!window->m_data.hTableTips.empty())
                {
                    auto& tmp = window->m_data.hTableTips.front();
                    window->PopupToast(tmp.lpText.c_str(), tmp.uType, tmp.dwMilliseconds, tmp.ptOffset, tmp.hFontSize);
                    window->m_data.hTableTips.erase(window->m_data.hTableTips.begin());
                }
                delete uDateTips;
                delete obj;
                return S_FALSE;
            }
            uDateTips->uTime += 1;
        }
    }
    else if (uMsg == WM_ERASEBKGND)
    {
        auto canvas = (UICanvas*)wParam;
        INT width = GET_X_LPARAM(lParam);
        INT height = GET_Y_LPARAM(lParam);

        tips_s* uDateTips = (tips_s*)obj->GetlParam();
        if (uDateTips)
        {
            UIBrush* hBrush = new UIBrush(uDateTips->crBackground);
            canvas->FillRoundRect(hBrush, 0, 0, width, height, 5);
            hBrush->SetColor(uDateTips->crBorder);
            canvas->DrawRoundRect(hBrush, 0, 0, width - 1.f, height - 1.f, 5, 1.5f, D2D1_DASH_STYLE_SOLID);
            FLOAT ultop = (height - UIEngine::fScale(32)) / 2;
            ExRectF iconR{ UIEngine::fScale(10), ultop, UIEngine::fScale(10 + 30), ultop + UIEngine::fScale(30) };

            if (FLAGS_CHECK(uDateTips->uType, Toast_error))//错误
            {
                canvas->FillRoundRect(hBrush, iconR.left, iconR.top, iconR.right, iconR.bottom, 30.f / 2.f);
                hBrush->SetColor(uDateTips->crText);
                canvas->DrawLine(hBrush, D2D1::Point2F(iconR.left + 10, iconR.top + 10), D2D1::Point2F(iconR.left + 20, iconR.top + 20), 3.0f);
                canvas->DrawLine(hBrush, D2D1::Point2F(iconR.left + 10, iconR.top + 20), D2D1::Point2F(iconR.left + 20, iconR.top + 10), 3.0f);
            }
            else if (FLAGS_CHECK(uDateTips->uType, Toast_enquire))//询问
            {
                canvas->FillRoundRect(hBrush, iconR.left, iconR.top, iconR.right, iconR.bottom, 30.f / 2.f);
                canvas->DrawTextByColor(obj->GetFont(), L"?", Center | Middle,
                    iconR.left,
                    iconR.top,
                    iconR.right,
                    iconR.bottom,
                    uDateTips->crText);
            }
            else if (FLAGS_CHECK(uDateTips->uType, Toast_success))//成功
            {
                canvas->FillRoundRect(hBrush, iconR.left, iconR.top, iconR.right, iconR.bottom, 30.f / 2.f);
                hBrush->SetColor(uDateTips->crText);
                auto dpath = new UIPath();
                dpath->BeginPath();
                if (dpath->StartFigure(iconR.left + 10, iconR.top + 16) == S_OK)
                {
                    dpath->LineTo(iconR.left + 14, iconR.top + 20);
                    dpath->LineTo(iconR.left + 20, iconR.top + 10);

                }
                dpath->EndPath();
                canvas->DrawPath(hBrush, dpath, 3.f);
                delete dpath;
            }
            else if (FLAGS_CHECK(uDateTips->uType, Toast_warning))//警告
            {
                canvas->FillRoundRect(hBrush, iconR.left, iconR.top, iconR.right, iconR.bottom, 30.f / 2.f);
                canvas->DrawTextByColor(obj->GetFont(), L"!", Center | Middle,
                    iconR.left,
                    iconR.top,
                    iconR.right,
                    iconR.bottom,
                    uDateTips->crText);
            }
            else
            {
                canvas->FillRoundRect(hBrush, iconR.left, iconR.top, iconR.right, iconR.bottom, 30.f / 2.f);
                canvas->DrawTextByColor(obj->GetFont(), L"i", Center | Middle,
                    iconR.left,
                    iconR.top,
                    iconR.right,
                    iconR.bottom,
                    uDateTips->crText);
            }
            canvas->DrawTextByColor(obj->GetFont(), obj->GetText(), obj->GetTextFormat(),
                UIEngine::fScale(48),
                UIEngine::fScale(10),
                width - UIEngine::fScale(5),
                height - UIEngine::fScale(10),
                uDateTips->crText);
            delete hBrush;

            return S_FALSE;
        }
    }
    return S_OK;
}

LRESULT HHBUI::UIWnd::OnToolTipWndProc(HK_THUNK_DATA* pData, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    HWND hWnd = pData->hWnd;
    WNDPROC pOld = pData->Proc;
    auto window = (UIWnd*)pData->dwData;
    if (window)
    {
        switch (uMsg)
        {
        case WM_ERASEBKGND:
            return 1; // 防止默认背景绘制
        case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);
            RECT rect;
            ::GetClientRect(hWnd, &rect);

            auto objFocus = window->m_data.objFocus.load();
            if (objFocus)
            {
                output(objFocus->m_data.pstrTips);
                FillRect(hdc, &rect, (HBRUSH)(COLOR_WINDOW + 1)); // 自定义背景色
                DrawText(hdc, objFocus->m_data.pstrTips, -1, &rect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
            }
            EndPaint(hWnd, &ps);
            return S_OK;
        }
        case WM_NOTIFY:
        {
            switch (((LPNMHDR)lParam)->code)
            {
            case TTN_GETDISPINFO: /*TTN_NEEDTEXT:*/
                LPNMTTDISPINFO pInfo = (LPNMTTDISPINFO)lParam;
                //SendMessage(pInfo->hdr.hwndFrom, TTM_SETMAXTIPWIDTH, 0, this_window->m_nWidth);
                output(pInfo->szText);
                break;
            }
        }
        break;
        default:
            return CallWindowProcW(pOld, hWnd, uMsg, wParam, lParam);
        }
    }
    return S_OK;
}

void HHBUI::UIWnd::wm_ime_composition()
{
    auto hImc = ImmGetContext(m_data.hWnd);
    if (hImc != 0)
    {
        POINT pt;
        GetCaretPos(&pt);
        if (FLAGS_CHECK(m_data.dwFlags, EWF_BPOPUPWINDOIWSHOWN))
        {
            RECT rect;
            GetWindowRect(m_data.hWndPopup, &rect);
            pt.x += rect.left - m_data.size.left;
            pt.y += rect.top - m_data.size.top;
        }
        COMPOSITIONFORM cf;
        cf.dwStyle = CFS_POINT;
        cf.ptCurrentPos.x = pt.x;
        cf.ptCurrentPos.y = pt.y;
        ImmSetCompositionWindow(hImc, &cf);
        auto objFocus = m_data.objFocus.load();
        if (objFocus)
        {
            UIFont *hFont = objFocus->m_data.hFont;
            if (hFont == 0)
                hFont = UIWinApi::ToList.default_font;
            LOGFONTW Logfont{ 0 };
            hFont->GetLogFont(&Logfont);
            ImmSetCompositionFontW(hImc, &Logfont);
        }
        ImmReleaseContext(m_data.hWnd, hImc);
    }
}

BOOL HHBUI::UIWnd::wm_keyboard(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    auto objFocus = m_data.objFocus.load();
    BOOL bPopupWindowShown = FALSE;

    // 处理弹出窗口
    if (objFocus && uMsg != WM_IME_CHAR)
    {
        if (FLAGS_CHECK(m_data.dwFlags, EWF_BPOPUPWINDOIWSHOWN))
        {
            if (uMsg >= WM_KEYDOWN && uMsg <= WM_KEYLAST)
            {
                SendMessageW(m_data.hWndPopup, uMsg, wParam, lParam);
                bPopupWindowShown = TRUE;
            }
        }
        if (objFocus->OnBaseProc(m_data.hWnd, uMsg, wParam, lParam) == S_FALSE)
        {
            return TRUE;
        }
    }
    switch (uMsg)
    {
    case WM_KEYDOWN:
        return wm_handlekeydown(wParam, objFocus, bPopupWindowShown, lParam);

    case WM_KEYUP:
        return wm_handlekeyup(wParam, objFocus);

    case WM_COPY:
        return wm_handlecopy(uMsg, objFocus);

    case WM_IME_CHAR:
        return wm_handleimechar(wParam, lParam, objFocus);

    default:
        return 0;
    }
}

BOOL HHBUI::UIWnd::wm_handlekeydown(WPARAM wParam, UIControl* objFocus, BOOL bPopupWindowShown, LPARAM lParam)
{
    if (wParam == VK_ESCAPE)
    {
        if ((m_data.dwUIStyle & UISTYLE_ESCEXIT) == UISTYLE_ESCEXIT && !bPopupWindowShown)
        {
            SendMessageW(m_data.hWnd, WM_CLOSE, 0, 0);
        }
    }
    else if (wParam == VK_SPACE)
    {
        if (GetKeys() == 0 && objFocus && ((objFocus->m_data.dwFlags & EOF_BDISABLESPACEANDENTER) != EOF_BDISABLESPACEANDENTER))
        {
            wm_buttondown(objFocus, WM_LBUTTONDOWN, 0, 0);
        }
    }
    else if (wParam == VK_TAB)
    {
        wm_obj_settabstop(objFocus);
    }
    else if (wParam == VK_UP || wParam == VK_DOWN || wParam == VK_LEFT || wParam == VK_RIGHT)
    {
        if ((m_data.dwUIStyle & EWS_MENU) == EWS_MENU)
        {
            return 1; // 禁止菜单项的方向键操作
        }
    }

    if ((m_data.dwFlags & EWF_BMODAL) == EWF_BMODAL)
    {
        UINT retvalue = MapVirtualKeyW(wParam, 2); // 判断是否为输入法字符
        if (retvalue && objFocus)
        {
            BYTE keyboardState[256] = { 0 };
            ::GetKeyboardState(keyboardState);
            WCHAR charactersPressed[2] = {};
            ToUnicode(wParam, lParam, keyboardState, charactersPressed, 2, 0);
            objFocus->OnBaseProc(m_data.hWnd, WM_CHAR, (WPARAM)charactersPressed[0], lParam);
        }
    }

    return 0;
}

BOOL HHBUI::UIWnd::wm_handlekeyup(WPARAM wParam, UIControl* objFocus)
{
    if (wParam == VK_SPACE && objFocus)
    {
        if ((objFocus->m_data.dwFlags & EOF_BDISABLESPACEANDENTER) != EOF_BDISABLESPACEANDENTER)
        {
            wm_mouse(WM_LBUTTONUP, 0, 0);
            objFocus->OnBaseProc(m_data.hWnd, WM_EX_LCLICK, 0, 0);
        }
    }
    return 0;
}

BOOL HHBUI::UIWnd::wm_handlecopy(UINT uMsg, UIControl* objFocus)
{
    if ((m_data.dwUIStyle & EWS_MESSAGEBOX) == EWS_MESSAGEBOX && objFocus)
    {
        if (lstrcmpW(objFocus->m_data.lpClsname, L"form-edit") == 0)
        {
            return 1; // 阻止消息框中的复制操作
        }
    }
    return 0;
}

BOOL HHBUI::UIWnd::wm_handleimechar(WPARAM wParam, LPARAM lParam, UIControl* objFocus)
{
    if ((m_data.dwFlags & EWF_BMODAL) == EWF_BMODAL && objFocus)
    {
        objFocus->OnBaseProc(m_data.hWnd, WM_CHAR, wParam, lParam);
    }
    return 0;
}


void HHBUI::UIWnd::wm_mouse(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    UIControl* parent = nullptr;
    if (((m_data.dwFlags & EWF_BTRACKOBJECT) == EWF_BTRACKOBJECT)) //是否按住组件
    {
        parent = m_data.objTrack;
    }
    else
    {
        parent = m_data.objHittest;
    }
    INT dwHitCode = m_data.dwHitCode;
    if (parent)
    {
        auto pObjLast = parent;
        if (((m_data.dwFlags & EWF_BTRACKOBJECT) == EWF_BTRACKOBJECT))
        {
            lParam = MAKELONG(GET_X_LPARAM(lParam) - pObjLast->m_data.Frame_w.left, GET_Y_LPARAM(lParam) - pObjLast->m_data.Frame_w.top);
            m_data.dwHitObjPos_Abs = lParam;
        }
        else
        {
            lParam = m_data.dwHitObjPos_Abs;
        }
    }
    if (uMsg == WM_MOUSEMOVE)
    {
        if (wParam == 0)
        {
            wm_leavecheck(m_data.objHittestPrev, parent, lParam, TRUE);
        }
        if (parent)
        {
            parent->OnBaseProc(m_data.hWnd, uMsg, wParam, lParam);
        }
    }
    else if (parent)
    {
        if (uMsg == WM_LBUTTONDOWN)
        {
            wm_popupclose();
        }
        if (uMsg == WM_LBUTTONDOWN || uMsg == WM_RBUTTONDOWN || uMsg == WM_MBUTTONDOWN)
        {
            wm_buttondown(parent, uMsg, wParam, lParam);
        }
        else if (uMsg == WM_LBUTTONUP || uMsg == WM_RBUTTONUP || uMsg == WM_MBUTTONUP)
        {
            wm_obj_untrack(lParam, FALSE);
            parent->OnBaseProc(m_data.hWnd, uMsg, wParam, lParam);
            return;
        }
        else if (uMsg == WM_LBUTTONDBLCLK || uMsg == WM_RBUTTONDBLCLK || uMsg == WM_MBUTTONDBLCLK)
        {
            INT newMsg = uMsg;
            if (uMsg == WM_LBUTTONDBLCLK)
            {
                newMsg = WM_LBUTTONDBLCLK;
            }
            else if (uMsg == WM_RBUTTONDBLCLK)
            {
                newMsg = WM_RBUTTONDOWN;
            }
            else
            {
                newMsg = WM_MBUTTONDOWN;
            }
            wm_buttondown(parent, newMsg, wParam, lParam);

        }
    }
}

void HHBUI::UIWnd::wm_obj_settabstop(UIControl* objLastFocus)
{
    auto pObjLast = objLastFocus;
    if (pObjLast)
    {
        BOOL objNextFocus = wm_obj_setnextfocus(pObjLast->m_data.objNext, objLastFocus);
        if (objNextFocus == FALSE)
        {
            auto objParent = (UIControl*)pObjLast->m_data.Parent->m_UIView;
            if (objParent)
            {
                wm_obj_setnextfocus((UIControl*)objParent->m_objChildFirst, objLastFocus);
            }
            else
            {
                wm_obj_setnextfocus((UIControl*)m_objChildFirst, objLastFocus);
            }
        }
    }
    else
    {
        wm_obj_setnextfocus((UIControl*)m_objChildFirst, objLastFocus);
    }
}

BOOL HHBUI::UIWnd::wm_obj_setnextfocus(UIControl* objEntry, UIControl* objLastFocus)
{
    auto pObjEntry = objEntry;
    while (pObjEntry)
    {

        if (((pObjEntry->m_data.dwStyleEx & eos_ex_tabstop) == eos_ex_tabstop))
        {
            if (objLastFocus != objEntry)
            {
                return pObjEntry->SetFocus();
            }
        }
        pObjEntry = pObjEntry->m_data.objNext;
    }
    return FALSE;
}

void HHBUI::UIWnd::wm_buttondown(UIControl* parent, INT uMsg, WPARAM wParam, LPARAM lParam)
{
    if ((m_data.dwFlags & EWF_BTRACKOBJECT) != EWF_BTRACKOBJECT)
    {
        m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & (EWF_BLEFTTRACK | EWF_BRIGHTTRACK | EWF_BMIDTRACK | EWF_BLEFTDTRACK));
        m_data.dwFlags |=  EWF_BTRACKOBJECT;
        if (uMsg == WM_LBUTTONDOWN)
        {
            m_data.dwFlags |= EWF_BLEFTTRACK;
            if (parent)
                parent->SetFocus();
        }
        else if (uMsg == WM_LBUTTONDBLCLK)
        {
            m_data.dwFlags |=  EWF_BLEFTDTRACK;
            if (parent)
                parent->SetFocus();
        }
        else if (uMsg == WM_RBUTTONDOWN)
        {
            m_data.dwFlags |=  EWF_BRIGHTTRACK;
        }
        else
        {
            m_data.dwFlags |= EWF_BMIDTRACK;
        }
        m_data.objTrack = parent;
        if (parent)
        {
            parent->TooltipsPop();
            parent->OnBaseProc(m_data.hWnd, uMsg, wParam, lParam);
        }
        if (wParam != 0)
        {
            SetCapture(m_data.hWnd);
        }
    }
}

void HHBUI::UIWnd::wm_obj_untrack(LPARAM lParam, BOOL fMsgDispatch)
{
    if (((m_data.dwFlags & EWF_BTRACKOBJECT) == EWF_BTRACKOBJECT))
    {
        m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & EWF_BTRACKOBJECT);
        auto objTrack = m_data.objTrack.load();
        m_data.objTrack = 0;
        m_data.objTrackPrev = objTrack;
        if (!fMsgDispatch)
        {
            ReleaseCapture();
        }
        if (objTrack)
        {
            POINT pt;
            GetCursorPos(&pt);
            INT uMsg = 0;
            if (fMsgDispatch)
            {

                if ((m_data.dwFlags & EWF_BLEFTTRACK) == EWF_BLEFTTRACK)
                {
                    uMsg = WM_LBUTTONUP;
                }
                else if ((m_data.dwFlags & EWF_BRIGHTTRACK) == EWF_BRIGHTTRACK)
                {
                    uMsg = WM_RBUTTONUP;
                }
                else if ((m_data.dwFlags & EWF_BMIDTRACK) == EWF_BMIDTRACK)
                {
                    uMsg = WM_MBUTTONUP;
                }
                objTrack->OnBaseProc(m_data.hWnd, uMsg, 0, 0);
                wm_nchittest(MAKELONG(pt.x, pt.y));
                wm_leavecheck(objTrack, nullptr, lParam, FALSE);
            }
            else
            {
                wm_nchittest(MAKELONG(pt.x, pt.y));
                wm_leavecheck(objTrack, nullptr, lParam, FALSE);
                if (m_data.objHittest == objTrack)
                {
                    if (((m_data.dwFlags & EWF_BLEFTTRACK) == EWF_BLEFTTRACK))
                    {
                        uMsg = WM_EX_LCLICK;
                    }
                    else if (((m_data.dwFlags & EWF_BLEFTDTRACK) == EWF_BLEFTDTRACK))
                    {
                        uMsg = WM_EX_LDCLICK;
                    }
                    else if (((m_data.dwFlags & EWF_BRIGHTTRACK) == EWF_BRIGHTTRACK))
                    {
                        uMsg = WM_EX_RCLICK;
                    }
                    else
                    {
                        uMsg = WM_EX_MCLICK;
                    }
                    objTrack->OnBaseProc(m_data.hWnd, uMsg, 0, m_data.dwHitObjPos_Abs);
                }
            }
        }
    }
}

void HHBUI::UIWnd::wm_menucontext(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    auto objHittest = m_data.objHittest.load();
    if (objHittest)
    {
        m_data.objMenucontext = objHittest;
        m_data.dwFlags |= EWF_BMENUCONTEXT;
        objHittest->OnBaseProc(m_data.hWnd, uMsg, wParam, lParam);
        m_data.dwFlags -= (m_data.dwFlags & EWF_BMENUCONTEXT);
    }
}

void HHBUI::UIWnd::wm_command(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    auto objMenucontext = m_data.objMenucontext.load();
    m_data.objMenucontext = 0;
    if (objMenucontext)
    {
        objMenucontext->OnBaseProc(m_data.hWnd, uMsg, wParam, lParam);
    }
    if (((m_data.dwFlags & EWF_BMODAL) == EWF_BMODAL))
    {
        if (wParam == 2)
        {
            EndDialog(m_data.hWnd, IDCANCEL);
        }

    }
}

BOOL HHBUI::UIWnd::wm_setbackgImage(UIImage *hImg, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    if (m_data.lpBackgroundImage)
    {
        m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & bif_playimage);
        KillTimer(this, (size_t)this + TIMER_BKG);
        auto hImage = m_data.lpBackgroundImage.load()->hImage;
        if(hImage) delete hImage;
        delete m_data.lpBackgroundImage;
        m_data.lpBackgroundImage = nullptr;
    }
    if (hImg)
    {
        UIImage* hImage = nullptr;
        INT width = 0, height = 0;
        if (dwFlags != bif_disablescale && dwFlags != bif_playimage)
        {
            hImg->Scale(m_data.size.right, m_data.size.bottom, &hImage);
            delete hImg;
        }
        else
        {
            hImage = hImg;
        }

        if (hImage != 0)
        {
            m_data.lpBackgroundImage = new info_backgroundimage();
            if (m_data.lpBackgroundImage)
            {
                m_data.lpBackgroundImage.load()->dwFlags = dwFlags;
                m_data.lpBackgroundImage.load()->hImage = hImage;
                m_data.lpBackgroundImage.load()->x = x;
                m_data.lpBackgroundImage.load()->y = y;
                m_data.lpBackgroundImage.load()->dwRepeat = dwRepeat;
                m_data.lpBackgroundImage.load()->dwAlpha = dwAlpha;
                m_data.lpBackgroundImage.load()->lpGrid = lpGrid;

                if (hImage->GetFrameCount() > 1)
                {
                    UINT nDelay = 0;
                    if (hImage->GetFrameDelay(nDelay, 1))
                    {
                        m_data.lpBackgroundImage.load()->nDelay = nDelay;
                        m_data.lpBackgroundImage.load()->maxFrame = hImage->GetFrameCount();
                        m_data.dwFlags = m_data.dwFlags - (m_data.dwFlags & bif_playimage);
                        KillTimer(this, (size_t)this + TIMER_BKG);

                        if ((dwFlags & bif_playimage) != 0 && nDelay != 0)
                        {
                            m_data.dwFlags |= bif_playimage;
                            SetTimer((size_t)this + TIMER_BKG, nDelay);
                        }
                    }
                }
                else
                {
                    Update();
                }
                return TRUE;
            }
        }
    }
    return FALSE;
}

void HHBUI::UIWnd::wm_calc_captionrect(RECT& rcCaption)
{
    rcCaption.right = m_data.size_caption.cx / 100 * m_data.size.right; //保证百分比
    rcCaption.bottom = UIEngine::fScale(m_data.size_caption.cy);    //绝对高度

    rcCaption.left = UIEngine::fScale(m_data.margin_caption.left);
    rcCaption.top = m_data.margin_caption.top;
    rcCaption.right = rcCaption.right - UIEngine::fScale(m_data.margin_caption.right);
    rcCaption.bottom = rcCaption.bottom + rcCaption.top;
}

void HHBUI::UIWnd::wm_sys_init()
{
    RECT rcCaption = { 0 };
    wm_calc_captionrect(rcCaption);
    INT nMinHeight = 0;
    BOOL uMsgStyle = FALSE;
    LPCWSTR lpTitle = 0;
    //FLAGS_CHECK(dwUIStyle, UISTYLE_NOTITLEBAR)
    INT ShowStyle = (FLAGS_CHECK(m_data.dwUIStyle, UISTYLE_NOTITLEBAR) ? eos_hidden : 0);
    if ((m_data.dwUIStyle & EWS_MESSAGEBOX) == EWS_MESSAGEBOX)
    {
        uMsgStyle = TRUE;
        lpTitle = m_data.lpMsgParams->lpTitle;
    }
    else
    {

        lpTitle = GetText();
    }
    auto objCaption = new UICombutton(this, 0, 0, rcCaption.right - rcCaption.left, rcCaption.bottom - rcCaption.top, lpTitle, UISTYLE_TITLE | ShowStyle,
        ((m_data.dwUIStyle & UISTYLE_NOCAPTIONTOPMOST) != 0 ? 0 : eos_ex_topmost | eos_ex_focusable), UISTYLE_TITLE, Left | Middle | EndEllipsis);

    //pObjCaption->outXml = TRUE;
    nMinHeight = rcCaption.bottom + rcCaption.top;
    INT nMinWidth = objCaption->Reset();

    ExRectF tmp{};
    objCaption->GetRect(tmp, grt_textoff);
    objCaption->SetPadding(8, 0, tmp.right - tmp.left + (nMinWidth / UIWinApi::ToList.drawing_default_dpi) + 8, 0);
    m_data.objCaption = objCaption;

    nMinWidth = nMinWidth + rcCaption.left + m_data.size.right - rcCaption.right;

    if ((m_data.dwUIStyle & UISTYLE_HASICON) == UISTYLE_HASICON)
    {
        nMinWidth = nMinWidth + UIEngine::fScale(16);
        if ((m_data.dwUIStyle & EWS_MESSAGEBOX) == EWS_MESSAGEBOX)
        {
            m_data.lParam = (size_t)m_data.lpMsgParams->hWnd;
        }
    }
    m_data.MinTrackSize.x = nMinWidth;
    m_data.MinTrackSize.y = nMinHeight;

}
void HHBUI::UIWnd::wm_initmenupopup(HMENU hMenu)
{
    BOOL fChecked = TRUE;
    INT nCount = GetMenuItemCount(hMenu);
    UIFont *hFont = m_data.hFont_Menu;
    if (hFont == 0)
        hFont = UIWinApi::ToList.default_font;
    WCHAR buff[520];
    FLOAT width, height, nMax = 0;
    for (INT i = 0; i < nCount; i++)
    {
        GetMenuStringW(hMenu, i, buff, 520, MF_BYPOSITION);
        UICanvas::CalcTextSize(hFont, buff, DT_SINGLELINE, 0, 0, &width, &height);
        if (nMax < width)
        {
            nMax = width;
        }
    }
    MENUITEMINFOW mii{ 0 };
    mii.cbSize = sizeof(MENUITEMINFOW);
    mii.fMask = MIIM_FTYPE | MIIM_ID;
    for (INT i = 0; i < nCount; i++)
    {
        if (GetMenuItemInfoW(hMenu, i, TRUE, &mii))
        {
            if ((mii.fType & MFT_SEPARATOR) != 0)
            {
                mii.wID = 0;
            }
            mii.fType = mii.fType | MFT_OWNERDRAW;
            if (SetMenuItemInfoW(hMenu, i, TRUE, &mii))
            {
                continue;
            }
        }
        fChecked = FALSE;
        break;
    }
    if (fChecked)
    {
        MENUINFO mi{};
        mi.cbSize = sizeof(MENUINFO);
        mi.fMask = MIM_MENUDATA | MIM_HELPID;
        mi.dwMenuData = (size_t)m_UIWindow;
        mi.dwContextHelpID = nMax;
        if (SetMenuInfo(hMenu, &mi))
        {
            m_data.hMenuPopup = hMenu;
            m_data.dwFlags |= EWF_BMENUINITED;
        }

    }
}
void HHBUI::UIWnd::wm_menu_setpos(HWND hWnd, tagWINDOWPOS* pos)
{
    auto pMenuHostWnd = m_data.pMenuHostWnd.load();
    UIWnd* pMenuPrevWnd = nullptr;
    RECT rcParent = { 0 }, rcDesk = { 0 };
    if (pMenuHostWnd != 0)
    {
        pMenuPrevWnd = pMenuHostWnd->m_data.pMenuPrevWnd;

        if (pMenuPrevWnd != 0)
        {
            GetWindowRect(pMenuPrevWnd->m_data.hWnd, &rcParent);
        }
    }
    INT x = pos->x;
    INT y = pos->y;
    POINT pt;
    GetCursorPos(&pt);
    if (rcParent.left < x) //子菜单在右边
    {
        x = rcParent.right + 5 - m_data.size_tmp * 2;
    }
    else
    {
        //子菜单在左边
        x = rcParent.left + m_data.size_tmp;
        GetWindowRect(hWnd, &rcParent);
        x -= (rcParent.right - rcParent.left) + GetSystemMetrics(SM_CXFIXEDFRAME) * 2 + 1;
        x -= 5;
    }
    pos->x = x;

    if (pMenuPrevWnd != 0)
    {
        auto pObj = pMenuPrevWnd->m_data.objFocus.load();
        if (pObj)
        {
            y = pObj->m_data.Frame_w.top + pMenuPrevWnd->m_data.size.top - 4;
            GetWindowRect(hWnd, &rcParent);
            INT height = rcParent.bottom - rcParent.top - GetSystemMetrics(SM_CYFIXEDFRAME) * 2;
            UIWinApi::GetWndScreenRect(hWnd, rcParent, rcDesk);

            if (y + height > rcParent.bottom)
            {
                if (pObj->m_data.Frame_w.bottom < height)
                {
                    y = pObj->m_data.Frame_w.top + pMenuPrevWnd->m_data.size.top - 2 - height + 5;
                }
                else
                {
                    y = pObj->m_data.Frame_w.bottom - height + 2;
                }
            }
        }
    }
    pos->y = y;
}
void HHBUI::UIWnd::wm_menu_createitems(HWND hWnd)
{
    size_t hMenu = SendMessageW(hWnd, MN_GETHMENU, 0, 0);
    INT nCount = GetMenuItemCount((HMENU)hMenu);
    auto pMenuHostWnd = m_data.pMenuHostWnd.load();

    if (pMenuHostWnd && hMenu)
    {
        menu_s* menu = m_data.lpMenuParams;
        RECT rcParent{ 0 };
        GetWindowRect(pMenuHostWnd->m_data.hWnd, &rcParent);
        RECT rcPaddingClient = { (LONG)UIEngine::fScale(5),(LONG)UIEngine::fScale(2),(LONG)UIEngine::fScale(5),(LONG)UIEngine::fScale(2)};
        INT width = m_data.size.right - UIEngine::fScale(rcPaddingClient.left + rcPaddingClient.right) - m_data.size_tmp * UIEngine::fScale(2);
        INT height = m_data.size.bottom - UIEngine::fScale(rcPaddingClient.top + rcPaddingClient.bottom);
        auto objTmp = new UIPage(this, rcPaddingClient.left, rcPaddingClient.top, width, height, 0, EMIS_SUBMENU, eos_ex_focusable);

        Layout_Init(elt_absolute);
        Layout_Absolute_Setedge(objTmp, elcp_absolute_left, elcp_absolute_type_px, rcPaddingClient.left);
        Layout_Absolute_Setedge(objTmp, elcp_absolute_top, elcp_absolute_type_px, rcPaddingClient.top);
        Layout_Absolute_Setedge(objTmp, elcp_absolute_right, elcp_absolute_type_px, rcPaddingClient.right);
        Layout_Absolute_Setedge(objTmp, elcp_absolute_bottom, elcp_absolute_type_px, rcPaddingClient.bottom);

        auto objPP = new UIPage(objTmp, 0, 0, width, height, 0, 0, eos_ex_focusable, 0);

        objTmp->Layout_Init(elt_absolute);
        objTmp->Layout_Absolute_Setedge(objPP, elcp_absolute_left, elcp_absolute_type_px, 0);
        objTmp->Layout_Absolute_Setedge(objPP, elcp_absolute_top, elcp_absolute_type_px, 0);
        objTmp->Layout_Absolute_Setedge(objPP, elcp_absolute_right, elcp_absolute_type_px, 0);
        objTmp->Layout_Absolute_Setedge(objPP, elcp_absolute_bottom, elcp_absolute_type_px, 0);

        INT heightParent = height;
        height = 0;
        MENUITEMINFOW mii{ 0 };
        mii.cbSize = sizeof(MENUITEMINFOW);
        mii.fMask = MIIM_FTYPE | MIIM_SUBMENU | MIIM_ID;
        RECT rcItem{ 0 };
        INT offsetTop = 0;

        for (INT i = 0; i < nCount; i++)
        {
            if (GetMenuItemRect(pMenuHostWnd->m_data.hWnd, (HMENU)hMenu, i, &rcItem))
            {
                INT eos = eos_nodpiscale | eos_scroll_disableno;
                if (GetMenuItemInfoW((HMENU)hMenu, i, TRUE, &mii))
                {
                    if ((mii.fType & MFT_SEPARATOR) != 0) //分隔符
                        eos = eos | EMIS_SEPARATOR;
                    else
                    {
                        if (mii.hSubMenu != 0)
                        {

                            eos = eos | EMIS_SUBMENU;
                        }
                    }
                }
                WCHAR buff[520];
                OffsetRect(&rcItem, -rcParent.left, -rcParent.top);
                // 组件超出屏幕左边会出现菜单项目左边负数
                if (rcItem.left < 0)
                {
                    INT offset = abs(rcItem.left);
                    rcItem.left = rcItem.left + offset;
                    rcItem.right = rcItem.right + offset;
                }
                else if (rcItem.left > 0)
                {
                    INT offset = abs(rcItem.left);
                    rcItem.left = rcItem.left - offset;
                    rcItem.right = rcItem.right - offset;
                }
                // 判断第一项，取第一项顶边偏移,组件移到屏幕最顶端二级子菜单第一项会负数
                if (rcItem.top < 0 && i == 0)
                {
                    offsetTop = abs(rcItem.top);
                }
                rcItem.top = rcItem.top + offsetTop;
                rcItem.bottom = rcItem.bottom + offsetTop;
                GetMenuStringW((HMENU)hMenu, i, buff, 520, MF_BYPOSITION);

                auto objitem = new UIItem(objPP, rcItem.left, rcItem.top, width, rcItem.bottom - rcItem.top, buff, eos, eos_ex_focusable,
                    mii.wID, Prefix | SingleLine | Middle);
                objitem->m_data.lParam = i;
                if (m_data.radius)
                    objitem->m_data.radius.left = UIEngine::fScale(6);
                objitem->m_data.dwFlags |= EOF_BMENUITEM;
                if (menu->crText.empty())
                    menu->crText = UIColor(0, 0, 0, 230);
                objitem->SetColor(color_text_normal, menu->crText);
                objitem->SetColor(color_text_hover, menu->crTextHot);
                objitem->SetPadding(32, 0, 20, 0);
                height += rcItem.bottom - rcItem.top;
            }
        }

        objPP->SetPos(CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT, height, 0, SWP_NOMOVE | SWP_NOZORDER | SWP_NOREDRAW | SWP_NOACTIVATE | SWP_NOCOPYBITS | SWP_EX_NODPISCALE);

        //objTmp->SetScrollShow(FALSE, height - heightParent > 0);
        //objTmp->SetScrollInfo(FALSE, SIF_RANGE | SIF_PAGE, 0, height - heightParent, heightParent, 0, TRUE);

        SendMessageW(hWnd, WM_INITMENUPOPUP, hMenu, height);
    }
}
BOOL HHBUI::UIWnd::wm_measureitem_host(WPARAM wParam, LPARAM lParam)
{
    BOOL ret = FALSE;
    if (wParam == 0) //menu MEASUREITEMSTRUCT https://docs.microsoft.com/en-us/windows/win32/api/winuser/ns-winuser-measureitemstruct
    {
        if (((MEASUREITEMSTRUCT*)lParam)->CtlType == ODT_MENU)
        {
            INT nID = ((MEASUREITEMSTRUCT*)lParam)->itemID;
            RECT ppc{ 0,2,0,2 };
            INT offset = UIEngine::fScale(ppc.left + ppc.right);

            RECT ppt{ 42,0,20,0 };
            offset += UIEngine::fScale(ppt.left + ppt.right);

            INT width, height;
            if (nID == 0) //MF_SEPARATOR
            {
                width = 5 + offset;
                height = 5;//分割线高度
            }
            else
            {
                MENUINFO mi{};
                mi.cbSize = sizeof(MENUINFO);
                mi.fMask = MIM_HELPID;
                GetMenuInfo(m_data.hMenuPopup, &mi);
                width = mi.dwContextHelpID + offset;
                height = UIEngine::fScale(35);//普通项目高度

            }
            ((MEASUREITEMSTRUCT*)lParam)->itemWidth = width;
            ((MEASUREITEMSTRUCT*)lParam)->itemHeight = height;
            ret = TRUE;
        }
    }
    return ret;
}
BOOL HHBUI::UIWnd::wm_menu_mouse(HWND hWnd, INT uMsg, WPARAM wParam, LONG_PTR* iItem)
{
    *iItem = -1;
    POINT pt;
    GetCursorPos(&pt);

    wm_nchittest(MAKELONG(pt.x, pt.y));
    wm_mouse(uMsg, wParam, MAKELONG(pt.x, pt.y));
    auto objHittest = m_data.objHittest.load();
    BOOL ret = FALSE;
    if (objHittest)
    {

        if (((objHittest->m_data.dwStyleEx & eos_ex_focusable) == eos_ex_focusable))
        {

            if (((objHittest->m_data.dwFlags & EOF_BMENUITEM) == EOF_BMENUITEM))
            {
                *iItem = objHittest->m_data.lParam;
            }
            ret = TRUE;
        }
    }

    auto pHost = m_data.pMenuHostWnd.load();
    if (pHost)
    {
        m_data.pMenuTrackWnd = (UIWnd*)m_UIWindow;
    }
    return ret;
}
DWORD HHBUI::UIWnd::UIStyle(DWORD wwsStyle, BOOL dwNewlong)
{
    if (wwsStyle == 0)
        return m_data.dwUIStyle;
    if (dwNewlong)
    {
        m_data.dwUIStyle = wwsStyle;
    }
    else
    {
        if (FLAGS_CHECK(m_data.dwUIStyle, wwsStyle))
        {
            FLAGS_DEL(m_data.dwUIStyle, wwsStyle);
        }
        else
        {
            FLAGS_ADD(m_data.dwUIStyle, wwsStyle);
        }
    }
    InvalidateRect(m_data.hWnd, NULL, FALSE);
    UpdateWindow(m_data.hWnd);
    return 0;
}
INT HHBUI::UIWnd::Style(INT dwStyle)
{
    if (dwStyle == 0)
        return GetWindowLongPtrW(m_data.hWnd, GWL_STYLE);
    if (_wnd_querystyle(m_data.hWnd, dwStyle, FALSE))
    {
        _wnd_delstyle(m_data.hWnd, dwStyle, FALSE);
    }
    else
    {
        _wnd_addstyle(m_data.hWnd, dwStyle, FALSE);
    }
    return 0;
}
INT HHBUI::UIWnd::StyleEx(INT dwStyleEx)
{
    if (dwStyleEx == 0)
        return GetWindowLongPtrW(m_data.hWnd, GWL_EXSTYLE);
    if (_wnd_querystyle(m_data.hWnd, dwStyleEx, TRUE))
    {
        _wnd_delstyle(m_data.hWnd, dwStyleEx, TRUE);
    }
    else
    {
        _wnd_addstyle(m_data.hWnd, dwStyleEx, TRUE);
    }
    return 0;
}
BOOL HHBUI::UIWnd::Move(INT x, INT y, INT width, INT height, BOOL bRepaint)
{
    RECT rect = { 0 };
    GetWindowRect(m_data.hWnd, &rect);
    if (x == CW_USEDEFAULT)
        x = rect.left;
    if (y == CW_USEDEFAULT)
        y = rect.top;
    if (width == CW_USEDEFAULT)
        width = rect.right - rect.left;
    if (height == CW_USEDEFAULT)
        height = rect.bottom - rect.top;
    m_data.size.left = x;
    m_data.size.top = y;
    return ::MoveWindow(m_data.hWnd, x, y, width, height, bRepaint);
}
void HHBUI::UIWnd::Update(BOOL bUpdate)
{
    m_data.dwFlags |= EWF_BREDRAWBACKGROUND;
    InvalidateRect(m_data.hWnd, NULL, FALSE);

    if (bUpdate)
    {
        auto atomName = GetClassLongPtrW(m_data.hWnd, GCW_ATOM);
        if (atomName == ATOM_DIALOG) //判断是模态窗口,添加模态窗口风格
        {
            SetFocus(m_data.hWnd);
        }
        Layout_Update();
        UpdateWindow(m_data.hWnd);
    }
}

void HHBUI::UIWnd::SetShadowColor(UIColor dwColor, INT fSize)
{
    m_data.crshadow.reset();
    if (!dwColor.empty())
        m_data.crshadow = std::make_unique <UIBrush>(dwColor);
    if (fSize)
    {
        m_data.size_shadow = fSize;
        m_data.size_tmp = fSize;
    }
    InvalidateRect(m_data.hWnd, NULL, FALSE);

}
void HHBUI::UIWnd::GetShadowColor(UIColor& dwColor, INT& fSize)
{
    if (m_data.crshadow)
        m_data.crshadow.get()->GetColor(dwColor);
    fSize = m_data.size_shadow;
}
void HHBUI::UIWnd::SetBorderColor(UIColor dwColor, INT fSize)
{
    m_data.crBorder.reset();
    if (!dwColor.empty())
        m_data.crBorder = std::make_unique <UIBrush>(dwColor);
    if (fSize)
    {
        m_data.size_Border = fSize;
        m_data.size_tmp = fSize;
    }
    InvalidateRect(m_data.hWnd, NULL, FALSE);
}

void HHBUI::UIWnd::GetBorderColor(UIColor& dwColor, INT& fSize)
{
    if (m_data.crBorder)
        m_data.crBorder.get()->GetColor(dwColor);
    fSize = m_data.size_Border;
}

INT HHBUI::UIWnd::GetSideSize()
{
    return m_data.size_tmp;
}

void HHBUI::UIWnd::SetBackgColor(UIColor dwColor)
{
    if (m_data.crbkg)
        m_data.crbkg.reset();
    if (!dwColor.empty())
    {
        m_data.crbkg = std::make_unique <UIBrush>(dwColor);
        m_data.dwUIStyle |= UISTYLE_NOINHERITBKG;
    }
    m_data.dwFlags |= EWF_BREDRAWBACKGROUND;
    InvalidateRect(m_data.hWnd, NULL, FALSE);
    UpdateWindow(m_data.hWnd);
}

BOOL HHBUI::UIWnd::SetBackgImage(LPVOID lpImage, size_t dwImageLen, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    if (dwImageLen != 0)
    {
        auto hImg = new UIImage(lpImage, dwImageLen);
        return wm_setbackgImage(hImg, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
    }
    return wm_setbackgImage(0, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
}

BOOL HHBUI::UIWnd::SetBackgImage(UIImage *hImage, BOOL fReset, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    if(fReset)
        return wm_setbackgImage(0, 0, 0, 0, 0, 0, 0);
    return wm_setbackgImage(hImage, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
}

BOOL HHBUI::UIWnd::SetBackgImage(LPCWSTR lpImagefile, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    if (lpImagefile)
    {
        auto hImg = new UIImage(lpImagefile);
        return wm_setbackgImage(hImg, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
    }
    return wm_setbackgImage(0, x, y, dwRepeat, lpGrid, dwFlags, dwAlpha);
}
void HHBUI::UIWnd::SetBackgInfo(INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha)
{
    auto lpBI = m_data.lpBackgroundImage.load();
    if (lpBI)
    {
        lpBI->dwFlags = dwFlags;
        lpBI->x = x;
        lpBI->y = y;
        lpBI->dwRepeat = dwRepeat;
        lpBI->dwAlpha = dwAlpha;
        lpBI->lpGrid = lpGrid;
    }
}
BOOL HHBUI::UIWnd::SetBackgPlay(BOOL fPlayFrames, BOOL fResetFrame)
{
    auto lpBI = m_data.lpBackgroundImage.load();
    if (lpBI)
    {
        auto pImg = lpBI->hImage;
        if (pImg->GetFrameCount() > 1)
        {
            lpBI->dwFlags = lpBI->dwFlags - (lpBI->dwFlags & bif_playimage);
            KillTimer(this, (size_t)this + TIMER_BKG);
            if (fPlayFrames && lpBI->nDelay != 0)
            {
                lpBI->dwFlags = lpBI->dwFlags | bif_playimage;
                SetTimer((size_t)this + TIMER_BKG, lpBI->nDelay);
            }
            if (fResetFrame)
                pImg->SetCurFrame(0);

            return TRUE;
        }
    }
    return FALSE;
}

void HHBUI::UIWnd::SetIme(BOOL bEnable)
{
    ImmAssociateContext(m_data.hWnd, (bEnable ? m_data.hImc : 0));
}

void HHBUI::UIWnd::SetRadius(INT radius)
{
    m_data.radius = radius;
    m_data.radius_r = radius;
    auto objCaption = (UICombutton*)m_data.objCaption.load();
    if (objCaption)
        objCaption->SetRadius(radius + 5, radius + 5, 0, 0);

    m_data.dwFlags |= EWF_BREDRAWBACKGROUND;
    InvalidateRect(m_data.hWnd, NULL, FALSE);
}
INT HHBUI::UIWnd::GetRadius()
{
    return m_data.radius;
}
void HHBUI::UIWnd::SetAlpha(INT fAlpha)
{
    m_data.alpha = fAlpha;
}
void HHBUI::UIWnd::SetCaptionInfo(info_objcaption *Info)
{
    if (m_data.objCaption && Info)
    {
        auto objCaption = (UICombutton*)m_data.objCaption.load();
        if (Info->hicon) {
            objCaption->SetIcon(Info->hicon);
        }
        auto sObj = (UICombutton*)objCaption->m_objChildFirst;
        while (sObj)
        {
            sObj->SetCrbutton(Info->crbutton_normal, Info->crbutton_hover, Info->crbutton_down);
            sObj->SetCrBkgbutton(Info->crBkgbutton_normal, Info->crBkgbutton_hover, Info->crBkgbutton_down);
            switch (sObj->GetID())
            {
            case UISTYLE_BTN_CLOSE:
                sObj->SetImgbutton(Info->imgbutton_close);
                break;
            case UISTYLE_BTN_MAX:
                sObj->SetImgbutton(Info->imgbutton_max, Info->imgbutton_restore);
                break;
            case UISTYLE_BTN_MIN:
                sObj->SetImgbutton(Info->imgbutton_min);
                break;
            case UISTYLE_BTN_MENU:
                sObj->SetImgbutton(Info->imgbutton_menu);
                break;
            case UISTYLE_BTN_SKIN:
                sObj->SetImgbutton(Info->imgbutton_skin);
                break;
            case UISTYLE_BTN_SETTING:
                sObj->SetImgbutton(Info->imgbutton_setting);
                break;
            case UISTYLE_BTN_HELP:
                sObj->SetImgbutton(Info->imgbutton_help);
                break;
            default:
                return;
            }
            sObj = (UICombutton*)sObj->GetNode(GW_HWNDNEXT);
        }

        objCaption->m_data.pfnSubClass = Info->pfnSubmsg;
        if (Info->Fontinfo)
        {
            objCaption->SetFontLogFont(Info->Fontinfo, TRUE);
        }
        else if (Info->lpszFontfamily != NULL || Info->dwFontsize != 0 || Info->dwFontstyle != 0)
        {
            objCaption->SetFontFromFamily(Info->lpszFontfamily, Info->dwFontsize, Info->dwFontstyle, TRUE);
        }
        if (!Info->crTitle.empty())
            objCaption->SetColor(color_text_normal, Info->crTitle);
        if (Info->crBkg.GetR() != 0.0f || Info->crBkg.GetG() != 0.0f || Info->crBkg.GetB() != 0.0f)
            objCaption->SetColor(color_background, Info->crBkg);
        if (Info->titlebHeight != 0)
            m_data.size_caption.cy = Info->titlebHeight;
        if (Info->dwTextFormat != 0)
            objCaption->SetTextFormat(Info->dwTextFormat);
        objCaption->Redraw();
    }
}
LPVOID HHBUI::UIWnd::GetCaptionInfo(info_objcaption& Info, INT uType)
{
    if (m_data.objCaption)
    {
        auto objCaption = (UICombutton*)m_data.objCaption.load();
        auto sObj = (UICombutton*)objCaption->m_objChildFirst;

        switch (uType)
        {
        case 1:
            sObj->GetCrbutton(Info.crbutton_normal, Info.crbutton_hover, Info.crbutton_down);
            break;
        case 2:
            sObj->GetCrBkgbutton(Info.crBkgbutton_normal, Info.crBkgbutton_hover, Info.crBkgbutton_down);
            break;
        default:break;
        }
    }
    return m_data.objCaption;
}
LPVOID HHBUI::UIWnd::GetCaptionObj(INT uType)
{
    if (m_data.objCaption)
        return ((UICombutton*)m_data.objCaption.load())->FindUIView(std::to_wstring(uType).c_str());
    return nullptr;
}
void HHBUI::UIWnd::SetBlur(FLOAT fDeviation, BOOL fmodesoft, BOOL bRedraw)
{
    m_data.fBlur = fDeviation;
    m_data.fmodesoft = fmodesoft;
    if (bRedraw)
        InvalidateRect(m_data.hWnd, 0, 0);
}
INT HHBUI::UIWnd::PopupMsg(LPCWSTR lpText, LPCWSTR lpTitle, INT uType, LPCWSTR lpCheckBox, BOOL* lpCheckBoxChecked, INT dwUIStyle, UIColor crText, UIColor crBackground,
    LPVOID dwButfig, INT dwMilliseconds, MsgPROC lpfnMsgProc)
{
    mbp_s mbp{};
    mbp.pWnd = m_UIWindow;
    mbp.lpText = lpText;
    mbp.lpTitle = lpTitle;
    mbp.uType = uType;
    mbp.lpCheckBox = lpCheckBox;
    mbp.lpCheckBoxChecked = lpCheckBoxChecked;
    mbp.dwFlags = dwUIStyle;
    mbp.lpfnNotifyCallback = lpfnMsgProc;
    mbp.crText = crText;
    mbp.crBackground = crBackground;
    mbp.hWnd = m_data.hWnd;
    mbp.radius = m_data.radius;
    if (m_data.crshadow)
        m_data.crshadow.get()->GetColor(mbp.crshadow);
    mbp.dwButfig = dwButfig;
    m_data.lpMsgParams = &mbp;
    if (dwMilliseconds)
        return MessageBoxTimeoutW(m_data.hWnd, lpText, lpTitle, uType, 0, dwMilliseconds);
    else
        return MessageBoxW(m_data.hWnd, lpText, lpTitle, uType);
}
BOOL HHBUI::UIWnd::PopupTrayIcon(LPCWSTR lpwzInfo, LPCWSTR lpwzInfoTitle, INT dwInfoFlags)
{
    BOOL nError = 0;
    LPVOID lpNid = m_data.lpNid;
    if (lpNid != 0)
    {
        ((NOTIFYICONDATAW*)lpNid)->uFlags = NIF_INFO;
        RtlZeroMemory((LPVOID)((size_t)lpNid + offsetof(NOTIFYICONDATAW, szInfo)), 512);
        INT len = lstrlenW(lpwzInfo);
        if (len > 255)
            len = 255;
        RtlMoveMemory((LPVOID)((size_t)lpNid + offsetof(NOTIFYICONDATAW, szInfo)), lpwzInfo, len * 2);
        RtlZeroMemory((LPVOID)((size_t)lpNid + offsetof(NOTIFYICONDATAW, szInfoTitle)), 128);
        len = lstrlenW(lpwzInfoTitle);
        if (len > 63)
            len = 63;
        if (len)
            RtlMoveMemory((LPVOID)((size_t)lpNid + offsetof(NOTIFYICONDATAW, szInfoTitle)), lpwzInfoTitle, len * 2);
        ((NOTIFYICONDATAW*)lpNid)->dwInfoFlags = dwInfoFlags;
        nError = Shell_NotifyIconW(NIM_MODIFY, (PNOTIFYICONDATAW)lpNid);
    }
    return nError;
}

void animateAlpha(HHBUI::UIStatic* handle, int dwAlphaStart, int dwAlphaEnd, int oDuration, int oInterval) {
    INT oSteps = oDuration / oInterval; // 总步数
    for (INT i = 0; i <= oSteps; i++) {

        std::this_thread::sleep_for(std::chrono::milliseconds(oInterval));
        INT alpha = dwAlphaStart + (dwAlphaEnd - dwAlphaStart) * i / oSteps;
        handle->SetAlpha(alpha); // 计算当前透明度值
    }
}
BOOL HHBUI::UIWnd::PopupToast(LPCWSTR lpText, INT uType, INT dwMilliseconds, INT ptOffset, INT hFontSize)
{
    ExRectF rcObj{};
    GetClientRect(rcObj);
    auto hLayoutTips = (UIStatic*)m_data.dsToast.load();
    size_t arraySize = 0;
    if (hLayoutTips)
    {
        arraySize = hLayoutTips->Layout_GetChildCount();
    }
    if (arraySize == 0)
    {
        if (m_data.dsToast)
            delete hLayoutTips;
        m_data.dsToast = new UIStatic(this, 0, 0, rcObj.right - rcObj.left, rcObj.bottom - rcObj.top, NULL, eos_hidden);
        hLayoutTips = (UIStatic*)m_data.dsToast.load();
        hLayoutTips->Layout_Init(elt_linear);
        hLayoutTips->Layout_SetProp(elp_linear_direction, elp_direction_v);
        if (FLAGS_CHECK(uType, Toast_type_right_bottom))
        {
            hLayoutTips->Layout_SetProp(elp_padding_bottom, ptOffset == 0 ? 30 : ptOffset);
        }
        else
        {
            hLayoutTips->Layout_SetProp(elp_padding_top, ptOffset == 0 ? 30 : ptOffset); //设置距离顶边30
        }
    }
    if (arraySize < 7)
    {
        if (hFontSize < 10)
            hFontSize = 11;
        FLOAT nWidthText = 0.f, nHeightText = 0.f;
        auto hFont = new UIFont(NULL, hFontSize);
        UICanvas::CalcTextSize(hFont, lpText, DT_NOPREFIX | DT_WORDBREAK | DT_EDITCONTROL,
            (static_cast<float>(rcObj.right) - rcObj.left) / 2 - nWidthText / 2, 100, &nWidthText, &nHeightText);

        delete hFont;
        nWidthText = nWidthText + 80;
        nHeightText = nHeightText + 40;

        INT dwTextFormat = DT_LEFT | DT_VCENTER;
        if (nHeightText > 100)
        {
            nHeightText = 100;
            dwTextFormat = DT_LEFT | DT_TOP | DT_WORDBREAK | DT_VCENTER | DT_PATH_ELLIPSIS;
        }
        if (dwMilliseconds < 1)
            dwMilliseconds = 4;
        auto dsToast = new UIStatic(this, 0, 0, nWidthText, nHeightText, lpText, 0, eos_ex_topmost, 0, dwTextFormat);
        dsToast->SetFontFromFamily(NULL, hFontSize);
        if (FLAGS_CHECK(uType, Toast_type_right_bottom))
            hLayoutTips->Layout_SetChildProp(dsToast, elcp_margin_bottom, 10);
        else
            hLayoutTips->Layout_SetChildProp(dsToast, elcp_margin_top, 10);

        if (FLAGS_CHECK(uType, Toast_type_left_top))
        {
            hLayoutTips->Layout_SetChildProp(dsToast, elcp_linear_align, elcp_linear_align_left_top);
            hLayoutTips->Layout_SetProp(elp_padding_left, 20);
        }
        else if (FLAGS_CHECK(uType, Toast_type_right_top))
        {
            hLayoutTips->Layout_SetChildProp(dsToast, elcp_linear_align, elcp_linear_align_right_top);
            hLayoutTips->Layout_SetProp(elp_padding_right, 20);
        }
        else if (FLAGS_CHECK(uType, Toast_type_right_bottom))
        {
            hLayoutTips->Layout_SetChildProp(dsToast, elcp_linear_align, elcp_linear_align_right_bottom);
            hLayoutTips->Layout_SetProp(elp_padding_right, 20);
            hLayoutTips->Layout_SetProp(elp_padding_bottom, 20);
        }
        else if (FLAGS_CHECK(uType, Toast_type_center))
        {
            hLayoutTips->Layout_SetChildProp(dsToast, elcp_linear_align, elcp_linear_align_center);
        }
        tips_s* tmp = new tips_s();
        tmp->hObjly = hLayoutTips;
        if (FLAGS_CHECK(uType, Toast_success))
        {
            tmp->crText = UIColor(78, 187, 35, 255);
            tmp->crBorder = UIColor(216, 247, 186, 255);
            tmp->crBackground = UIColor(243, 255, 232, 255);
        }
        else if (FLAGS_CHECK(uType, Toast_warning))
        {
            tmp->crText = UIColor(241, 179, 6, 255);
            tmp->crBorder = UIColor(251, 241, 204, 255);
            tmp->crBackground = UIColor(255, 249, 235, 255);
        }
        else if (FLAGS_CHECK(uType, Toast_error))
        {
            tmp->crText = UIColor(243, 75, 81, 255);
            tmp->crBorder = UIColor(254, 226, 226, 255);
            tmp->crBackground = UIColor(255, 247, 247, 255);
        }
        else if (FLAGS_CHECK(uType, Toast_enquire))
        {
            tmp->crText = UIColor(66, 194, 192, 255);
            tmp->crBorder = UIColor(193, 249, 248, 255);
            tmp->crBackground = UIColor(231, 253, 252, 255);
        }
        else
        {
            tmp->crText = UIColor(15, 175, 173, 255);
            tmp->crBorder = UIColor(191, 249, 248, 255);
            tmp->crBackground = UIColor(231, 253, 252, 255);
        }

        tmp->dwMilliseconds = dwMilliseconds;
        tmp->uType = uType;
        tmp->uTime = 0;
        dsToast->SetlParam((size_t)tmp);
        dsToast->SetMsgProc(OnToastsMsgProc);
        hLayoutTips->Layout_Update();
        Update();
        /*
        if (dwMilliseconds >= 3)
        {
            dsToast->SetAlpha(0);
            int dwAlphaStart = 0; // 起始透明度
            int dwAlphaEnd = 255; // 结束透明度
            int oDuration = 500; // 动画持续时间，单位为毫秒
            int oInterval = 10; // 更新间隔，单位为毫秒
            std::thread myThread(animateAlpha, dsToast, dwAlphaStart, dwAlphaEnd, oDuration, oInterval);
            // 分离线程，使其在后台运行
            myThread.detach();
        }*/
        dsToast->SetTimer(2001, 1000);
    }
    else
        m_data.hTableTips.emplace_back(Tips_Tmp{ lpText, uType, dwMilliseconds, ptOffset, hFontSize });
    return TRUE;
}

HRESULT HHBUI::UIWnd::ToImage(UIImage**dstImg, BOOL cdraw, FLOAT fBlur)
{
    if (!m_data.canvas_display)
        return E_FAIL;
    return m_data.canvas_display.load()->ToImage(dstImg, cdraw, fBlur);
}

UINT HHBUI::UIWnd::GetLastFPS() const
{
    return UIDrawContext::ToList.fpsCache;
}
void HHBUI::UIWnd::SetRenderMode(bool active)
{
    m_data.renderMode = active;
}

void HHBUI::UIWnd::MessageLoop()
{
    //message
    BOOL bRet = 0; UINT nRet = 0;
    MSG pMsg = {};
    while (m_UIWindow && (bRet = GetMessageW(&pMsg, NULL, 0, 0)) != 0)
    {
        //限制FPS
        UIDrawContext::ToList.fpsCounter.LimitFPS();
        // handle the error and possibly exit
        if (bRet == -1)
        {
            assert(false);
            break;
        }
        ::TranslateMessage(&pMsg);
        ::DispatchMessageW(&pMsg);
        if (pMsg.message == WM_QUIT) break;


        //记录当前FPS
        UIDrawContext::ToList.fpsCache = UIDrawContext::ToList.fpsCounter.CalcFPS();

    }
    if (pMsg.message == WM_QUIT) ::PostQuitMessage(pMsg.wParam);

}

// ==================== 窗口吸附和布局管理功能实现 ====================

void HHBUI::UIWnd::EnableWindowSnapping(bool enable)
{
    m_data.snapConfig.enabled = enable;
    if (enable) {
        wm_snap_init();
    } else {
        wm_snap_cleanup();
    }
}

void HHBUI::UIWnd::SetSnapConfig(const SnapConfig& config)
{
    m_data.snapConfig = config;

    // 更新预览画刷
    if (m_data.snapPreviewBrush) {
        m_data.snapPreviewBrush.reset();
    }
    m_data.snapPreviewBrush = std::make_unique<UIBrush>(config.previewColor);
}

HHBUI::SnapConfig HHBUI::UIWnd::GetSnapConfig() const
{
    return m_data.snapConfig;
}

void HHBUI::UIWnd::SnapToPosition(SnapPosition position, bool animated)
{
    if (!m_data.snapConfig.enabled) return;

    // 保存当前位置为原始位置（如果还没有保存）
    if (!m_data.layoutInfo.isSnapped) {
        GetWindowRect(m_data.hWnd, &m_data.layoutInfo.originalRect);
    }

    // 计算目标矩形
    RECT targetRect = wm_snap_calculate_rect(position);

    // 应用位置
    wm_layout_apply_position(position, animated);

    // 更新布局信息
    m_data.layoutInfo.snapPosition = position;
    m_data.layoutInfo.isSnapped = (position != SnapPosition::None);
    m_data.layoutInfo.currentRect = targetRect;
}

bool HHBUI::UIWnd::IsSnappedToEdge() const
{
    return m_data.layoutInfo.isSnapped &&
           m_data.layoutInfo.snapPosition != SnapPosition::None &&
           m_data.layoutInfo.snapPosition != SnapPosition::Center;
}

HHBUI::SnapPosition HHBUI::UIWnd::GetSnapPosition() const
{
    return m_data.layoutInfo.snapPosition;
}

void HHBUI::UIWnd::SetLayoutMode(LayoutMode mode)
{
    m_data.layoutInfo.layoutMode = mode;
}

HHBUI::LayoutMode HHBUI::UIWnd::GetLayoutMode() const
{
    return m_data.layoutInfo.layoutMode;
}

RECT HHBUI::UIWnd::SuggestOptimalPosition()
{
    RECT workArea = GetWorkArea();
    std::vector<RECT> existingWindows = GetAllVisibleWindowRects();

    // 简单的最优位置算法：找到最少重叠的位置
    RECT suggested = workArea;

    // 默认尺寸（如果窗口还没有尺寸）
    int defaultWidth = 800;
    int defaultHeight = 600;

    RECT currentRect;
    GetWindowRect(m_data.hWnd, &currentRect);
    int width = currentRect.right - currentRect.left;
    int height = currentRect.bottom - currentRect.top;

    if (width <= 0) width = defaultWidth;
    if (height <= 0) height = defaultHeight;

    // 尝试在工作区域内找到合适的位置
    int stepX = 50;
    int stepY = 50;
    int minOverlap = INT_MAX;

    for (int y = workArea.top; y <= workArea.bottom - height; y += stepY) {
        for (int x = workArea.left; x <= workArea.right - width; x += stepX) {
            RECT testRect = { x, y, x + width, y + height };

            int overlap = 0;
            for (const auto& existing : existingWindows) {
                RECT intersection;
                if (IntersectRect(&intersection, &testRect, &existing)) {
                    overlap += (intersection.right - intersection.left) *
                              (intersection.bottom - intersection.top);
                }
            }

            if (overlap < minOverlap) {
                minOverlap = overlap;
                suggested = testRect;
                if (overlap == 0) break; // 找到无重叠位置
            }
        }
        if (minOverlap == 0) break;
    }

    return suggested;
}

void HHBUI::UIWnd::ArrangeWindows(const std::vector<UIWnd*>& windows, LayoutMode mode)
{
    if (windows.empty()) return;

    // 获取主显示器工作区域
    RECT workArea;
    SystemParametersInfo(SPI_GETWORKAREA, 0, &workArea, 0);

    int workWidth = workArea.right - workArea.left;
    int workHeight = workArea.bottom - workArea.top;

    switch (mode) {
    case LayoutMode::Cascade: {
        int offsetX = 30;
        int offsetY = 30;
        int startX = workArea.left + 50;
        int startY = workArea.top + 50;

        for (size_t i = 0; i < windows.size(); ++i) {
            int x = startX + (static_cast<int>(i) * offsetX);
            int y = startY + (static_cast<int>(i) * offsetY);

            // 确保窗口不会超出工作区域
            if (x > workArea.right - 400) x = startX;
            if (y > workArea.bottom - 300) y = startY;

            windows[i]->Move(x, y, CW_USEDEFAULT, CW_USEDEFAULT, true);
        }
        break;
    }

    case LayoutMode::TileHorizontal: {
        int windowWidth = workWidth / static_cast<int>(windows.size());
        for (size_t i = 0; i < windows.size(); ++i) {
            int x = workArea.left + (static_cast<int>(i) * windowWidth);
            windows[i]->Move(x, workArea.top, windowWidth, workHeight, true);
        }
        break;
    }

    case LayoutMode::TileVertical: {
        int windowHeight = workHeight / static_cast<int>(windows.size());
        for (size_t i = 0; i < windows.size(); ++i) {
            int y = workArea.top + (static_cast<int>(i) * windowHeight);
            windows[i]->Move(workArea.left, y, workWidth, windowHeight, true);
        }
        break;
    }

    case LayoutMode::Grid: {
        int cols = static_cast<int>(std::ceil(std::sqrt(windows.size())));
        int rows = static_cast<int>(std::ceil(static_cast<double>(windows.size()) / cols));

        int windowWidth = workWidth / cols;
        int windowHeight = workHeight / rows;

        for (size_t i = 0; i < windows.size(); ++i) {
            int row = static_cast<int>(i) / cols;
            int col = static_cast<int>(i) % cols;

            int x = workArea.left + (col * windowWidth);
            int y = workArea.top + (row * windowHeight);

            windows[i]->Move(x, y, windowWidth, windowHeight, true);

            // 更新网格信息
            windows[i]->m_data.layoutInfo.gridRow = row;
            windows[i]->m_data.layoutInfo.gridCol = col;
            windows[i]->m_data.layoutInfo.isInLayout = true;
        }
        break;
    }

    case LayoutMode::Smart: {
        // 智能布局：根据窗口数量选择最佳布局
        if (windows.size() <= 2) {
            ArrangeWindows(windows, LayoutMode::TileHorizontal);
        } else if (windows.size() <= 4) {
            ArrangeWindows(windows, LayoutMode::Grid);
        } else {
            ArrangeWindows(windows, LayoutMode::Cascade);
        }
        break;
    }

    default:
        break;
    }

    // 更新所有窗口的布局模式
    for (auto* window : windows) {
        window->m_data.layoutInfo.layoutMode = mode;
        window->m_data.layoutInfo.isInLayout = true;
    }
}

HHBUI::WindowLayoutInfo HHBUI::UIWnd::GetLayoutInfo() const
{
    return m_data.layoutInfo;
}

void HHBUI::UIWnd::ResetToOriginalPosition(bool animated)
{
    if (!m_data.layoutInfo.isSnapped && !m_data.layoutInfo.isInLayout) return;

    RECT& originalRect = m_data.layoutInfo.originalRect;

    if (animated) {
        // 使用现有的动画系统
        UIAnimation::Start(this,
            originalRect.left, originalRect.left,
            originalRect.top, originalRect.top,
            AniEffect::Cubic_Out, 20, false);
    }

    Move(originalRect.left, originalRect.top,
         originalRect.right - originalRect.left,
         originalRect.bottom - originalRect.top, true);

    // 重置布局信息
    m_data.layoutInfo.snapPosition = SnapPosition::None;
    m_data.layoutInfo.isSnapped = false;
    m_data.layoutInfo.isInLayout = false;
    m_data.layoutInfo.gridRow = -1;
    m_data.layoutInfo.gridCol = -1;
}

void HHBUI::UIWnd::SaveCurrentAsOriginal()
{
    GetWindowRect(m_data.hWnd, &m_data.layoutInfo.originalRect);
}

RECT HHBUI::UIWnd::GetWorkArea() const
{
    RECT workArea;
    SystemParametersInfo(SPI_GETWORKAREA, 0, &workArea, 0);
    return workArea;
}

std::vector<RECT> HHBUI::UIWnd::GetAllVisibleWindowRects()
{
    std::vector<RECT> windowRects;

    // 枚举所有可见窗口
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        auto* rects = reinterpret_cast<std::vector<RECT>*>(lParam);

        if (IsWindowVisible(hwnd) && !IsIconic(hwnd)) {
            RECT rect;
            GetWindowRect(hwnd, &rect);

            // 过滤掉太小的窗口（可能是工具栏等）
            int width = rect.right - rect.left;
            int height = rect.bottom - rect.top;
            if (width > 100 && height > 100) {
                rects->push_back(rect);
            }
        }

        return TRUE;
    }, reinterpret_cast<LPARAM>(&windowRects));

    return windowRects;
}

bool HHBUI::UIWnd::DetectWindowCollision(const RECT& rect) const
{
    std::vector<RECT> existingWindows = GetAllVisibleWindowRects();

    for (const auto& existing : existingWindows) {
        RECT intersection;
        if (IntersectRect(&intersection, &rect, &existing)) {
            // 检查是否是当前窗口自己
            RECT currentRect;
            GetWindowRect(m_data.hWnd, &currentRect);
            if (!EqualRect(&existing, &currentRect)) {
                return true;
            }
        }
    }

    return false;
}

// ==================== 私有辅助方法实现 ====================

void HHBUI::UIWnd::wm_snap_init()
{
    // 初始化吸附配置
    if (!m_data.snapPreviewBrush) {
        m_data.snapPreviewBrush = std::make_unique<UIBrush>(m_data.snapConfig.previewColor);
    }

    // 初始化布局信息
    m_data.layoutInfo.snapPosition = SnapPosition::None;
    m_data.layoutInfo.layoutMode = LayoutMode::Manual;
    m_data.layoutInfo.isSnapped = false;
    m_data.layoutInfo.isInLayout = false;
    m_data.layoutInfo.gridRow = -1;
    m_data.layoutInfo.gridCol = -1;

    // 保存当前位置为原始位置
    GetWindowRect(m_data.hWnd, &m_data.layoutInfo.originalRect);
    m_data.layoutInfo.currentRect = m_data.layoutInfo.originalRect;
}

void HHBUI::UIWnd::wm_snap_cleanup()
{
    wm_snap_hide_preview();
    m_data.snapPreviewBrush.reset();

    if (m_data.snapPreviewWnd) {
        DestroyWindow(m_data.snapPreviewWnd);
        m_data.snapPreviewWnd = nullptr;
    }
}

void HHBUI::UIWnd::wm_snap_on_move_start(POINT startPos)
{
    if (!m_data.snapConfig.enabled) return;

    m_data.isDragging = true;
    m_data.dragStartPos = startPos;

    // 如果窗口当前是吸附状态，先恢复到原始大小
    if (m_data.layoutInfo.isSnapped) {
        // 这里可以添加恢复逻辑
    }
}

void HHBUI::UIWnd::wm_snap_on_moving(POINT currentPos)
{
    if (!m_data.snapConfig.enabled || !m_data.isDragging) return;

    RECT currentRect;
    GetWindowRect(m_data.hWnd, &currentRect);

    // 检测吸附位置
    SnapPosition detectedPosition = wm_snap_detect_position(currentRect);

    if (detectedPosition != SnapPosition::None) {
        if (!m_data.isSnapping) {
            m_data.isSnapping = true;

            // 显示吸附预览
            if (m_data.snapConfig.showPreview) {
                RECT previewRect = wm_snap_calculate_rect(detectedPosition);
                wm_snap_show_preview(previewRect);
            }
        }
    } else {
        if (m_data.isSnapping) {
            m_data.isSnapping = false;
            wm_snap_hide_preview();
        }
    }
}

void HHBUI::UIWnd::wm_snap_on_move_end()
{
    if (!m_data.snapConfig.enabled) return;

    m_data.isDragging = false;

    if (m_data.isSnapping) {
        // 应用吸附
        RECT currentRect;
        GetWindowRect(m_data.hWnd, &currentRect);
        SnapPosition position = wm_snap_detect_position(currentRect);

        if (position != SnapPosition::None) {
            SnapToPosition(position, true);
        }

        m_data.isSnapping = false;
        wm_snap_hide_preview();
    }
}

HHBUI::SnapPosition HHBUI::UIWnd::wm_snap_detect_position(const RECT& rect)
{
    if (!m_data.snapConfig.enabled) return SnapPosition::None;

    RECT workArea = GetWorkArea();
    int sensitivity = m_data.snapConfig.sensitivity;

    int left = rect.left;
    int top = rect.top;
    int right = rect.right;
    int bottom = rect.bottom;

    // 检测屏幕边缘吸附
    if (m_data.snapConfig.snapToScreen) {
        // 左边缘
        if (abs(left - workArea.left) <= sensitivity) {
            if (abs(top - workArea.top) <= sensitivity) {
                return SnapPosition::TopLeft;
            } else if (abs(bottom - workArea.bottom) <= sensitivity) {
                return SnapPosition::BottomLeft;
            } else {
                return SnapPosition::Left;
            }
        }

        // 右边缘
        if (abs(right - workArea.right) <= sensitivity) {
            if (abs(top - workArea.top) <= sensitivity) {
                return SnapPosition::TopRight;
            } else if (abs(bottom - workArea.bottom) <= sensitivity) {
                return SnapPosition::BottomRight;
            } else {
                return SnapPosition::Right;
            }
        }

        // 顶部边缘
        if (abs(top - workArea.top) <= sensitivity) {
            return SnapPosition::Top;
        }

        // 底部边缘
        if (abs(bottom - workArea.bottom) <= sensitivity) {
            return SnapPosition::Bottom;
        }
    }

    // 检测窗口吸附
    if (m_data.snapConfig.snapToWindows) {
        std::vector<RECT> targets = wm_snap_get_snap_targets();

        for (const auto& target : targets) {
            // 检查是否接近其他窗口的边缘
            if (abs(left - target.right) <= sensitivity ||
                abs(right - target.left) <= sensitivity ||
                abs(top - target.bottom) <= sensitivity ||
                abs(bottom - target.top) <= sensitivity) {
                // 这里可以返回更具体的窗口吸附位置
                // 暂时返回None，让屏幕边缘吸附优先
            }
        }
    }

    return SnapPosition::None;
}

RECT HHBUI::UIWnd::wm_snap_calculate_rect(SnapPosition position)
{
    RECT workArea = GetWorkArea();
    RECT currentRect;
    GetWindowRect(m_data.hWnd, &currentRect);

    int width = currentRect.right - currentRect.left;
    int height = currentRect.bottom - currentRect.top;
    int margin = m_data.snapConfig.edgeMargin;

    RECT result = currentRect;

    switch (position) {
    case SnapPosition::Left:
        result.left = workArea.left + margin;
        result.top = workArea.top + margin;
        result.right = workArea.left + (workArea.right - workArea.left) / 2 - margin;
        result.bottom = workArea.bottom - margin;
        break;

    case SnapPosition::Right:
        result.left = workArea.left + (workArea.right - workArea.left) / 2 + margin;
        result.top = workArea.top + margin;
        result.right = workArea.right - margin;
        result.bottom = workArea.bottom - margin;
        break;

    case SnapPosition::Top:
        result.left = workArea.left + margin;
        result.top = workArea.top + margin;
        result.right = workArea.right - margin;
        result.bottom = workArea.top + (workArea.bottom - workArea.top) / 2 - margin;
        break;

    case SnapPosition::Bottom:
        result.left = workArea.left + margin;
        result.top = workArea.top + (workArea.bottom - workArea.top) / 2 + margin;
        result.right = workArea.right - margin;
        result.bottom = workArea.bottom - margin;
        break;

    case SnapPosition::TopLeft:
        result.left = workArea.left + margin;
        result.top = workArea.top + margin;
        result.right = workArea.left + (workArea.right - workArea.left) / 2 - margin;
        result.bottom = workArea.top + (workArea.bottom - workArea.top) / 2 - margin;
        break;

    case SnapPosition::TopRight:
        result.left = workArea.left + (workArea.right - workArea.left) / 2 + margin;
        result.top = workArea.top + margin;
        result.right = workArea.right - margin;
        result.bottom = workArea.top + (workArea.bottom - workArea.top) / 2 - margin;
        break;

    case SnapPosition::BottomLeft:
        result.left = workArea.left + margin;
        result.top = workArea.top + (workArea.bottom - workArea.top) / 2 + margin;
        result.right = workArea.left + (workArea.right - workArea.left) / 2 - margin;
        result.bottom = workArea.bottom - margin;
        break;

    case SnapPosition::BottomRight:
        result.left = workArea.left + (workArea.right - workArea.left) / 2 + margin;
        result.top = workArea.top + (workArea.bottom - workArea.top) / 2 + margin;
        result.right = workArea.right - margin;
        result.bottom = workArea.bottom - margin;
        break;

    case SnapPosition::Center:
        result.left = workArea.left + (workArea.right - workArea.left - width) / 2;
        result.top = workArea.top + (workArea.bottom - workArea.top - height) / 2;
        result.right = result.left + width;
        result.bottom = result.top + height;
        break;

    case SnapPosition::Maximize:
        result = workArea;
        result.left += margin;
        result.top += margin;
        result.right -= margin;
        result.bottom -= margin;
        break;

    default:
        break;
    }

    return result;
}

void HHBUI::UIWnd::wm_snap_show_preview(const RECT& rect)
{
    if (!m_data.snapConfig.showPreview) return;

    // 创建预览窗口（如果还没有）
    if (!m_data.snapPreviewWnd) {
        m_data.snapPreviewWnd = CreateWindowEx(
            WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_TOPMOST,
            L"Static", L"SnapPreview",
            WS_POPUP,
            0, 0, 0, 0,
            nullptr, nullptr, GetModuleHandle(nullptr), nullptr
        );

        if (m_data.snapPreviewWnd) {
            // 设置透明度
            SetLayeredWindowAttributes(m_data.snapPreviewWnd, 0, 128, LWA_ALPHA);
        }
    }

    if (m_data.snapPreviewWnd) {
        // 更新预览窗口位置和大小
        SetWindowPos(m_data.snapPreviewWnd, HWND_TOPMOST,
            rect.left, rect.top,
            rect.right - rect.left, rect.bottom - rect.top,
            SWP_SHOWWINDOW | SWP_NOACTIVATE);

        m_data.snapPreviewRect = rect;
    }
}

void HHBUI::UIWnd::wm_snap_hide_preview()
{
    if (m_data.snapPreviewWnd) {
        ShowWindow(m_data.snapPreviewWnd, SW_HIDE);
    }
}

bool HHBUI::UIWnd::wm_snap_is_near_edge(const RECT& rect, int edge)
{
    RECT workArea = GetWorkArea();
    int sensitivity = m_data.snapConfig.sensitivity;

    switch (edge) {
    case 0: // Left
        return abs(rect.left - workArea.left) <= sensitivity;
    case 1: // Top
        return abs(rect.top - workArea.top) <= sensitivity;
    case 2: // Right
        return abs(rect.right - workArea.right) <= sensitivity;
    case 3: // Bottom
        return abs(rect.bottom - workArea.bottom) <= sensitivity;
    default:
        return false;
    }
}

std::vector<RECT> HHBUI::UIWnd::wm_snap_get_snap_targets()
{
    std::vector<RECT> targets;

    if (!m_data.snapConfig.snapToWindows) return targets;

    // 获取所有可见窗口作为吸附目标
    std::vector<RECT> allWindows = GetAllVisibleWindowRects();

    RECT currentRect;
    GetWindowRect(m_data.hWnd, &currentRect);

    for (const auto& windowRect : allWindows) {
        // 排除当前窗口
        if (!EqualRect(&windowRect, &currentRect)) {
            targets.push_back(windowRect);
        }
    }

    return targets;
}

void HHBUI::UIWnd::wm_layout_apply_position(SnapPosition position, bool animated)
{
    RECT targetRect = wm_snap_calculate_rect(position);

    if (animated) {
        // 使用现有的动画系统进行平滑移动
        UIAnimation::Start(this,
            targetRect.left, targetRect.left,
            targetRect.top, targetRect.top,
            AniEffect::Cubic_Out, 15, false);
    }

    // 应用新位置
    Move(targetRect.left, targetRect.top,
         targetRect.right - targetRect.left,
         targetRect.bottom - targetRect.top, true);
}

RECT HHBUI::UIWnd::wm_layout_calculate_grid_position(int row, int col, int totalRows, int totalCols)
{
    RECT workArea = GetWorkArea();

    int cellWidth = (workArea.right - workArea.left) / totalCols;
    int cellHeight = (workArea.bottom - workArea.top) / totalRows;

    RECT result;
    result.left = workArea.left + (col * cellWidth);
    result.top = workArea.top + (row * cellHeight);
    result.right = result.left + cellWidth;
    result.bottom = result.top + cellHeight;

    // 添加边距
    int margin = m_data.snapConfig.edgeMargin;
    result.left += margin;
    result.top += margin;
    result.right -= margin;
    result.bottom -= margin;

    return result;
}

