﻿#include "pch.h"
#include "splashscreen.h"
#include <algorithm> // 添加algorithm头文件，提供std::max和std::min
#include <gdiplus.h>
#pragma comment(lib, "gdiplus.lib")

namespace HHBUI
{
    // 静态成员变量，用于在窗口过程中访问SplashScreen实例
    static UISplashScreen* g_pSplashScreen = nullptr;

    UISplashScreen::UISplashScreen() : m_pWnd(nullptr), m_pText(nullptr), m_pProgress(nullptr), m_dwTimerId(0), m_bShowProgress(FALSE),
        m_nWidth(650), m_nHeight(160), m_nRadius(10), m_pBackImage(nullptr)
    {
        m_strText = L"欢迎使用HHBUI，正在启动主窗口...";
        m_nProgress = 0;
        m_dwBackColor = UIColor(30, 30, 30, 128); // 半透明背景
        m_dwTextColor = UIColor(255, 255, 255, 255); // 文字完全不透明
        m_dwFontSize = 28;
        g_pSplashScreen = this; // 保存实例指针
    }

    UISplashScreen::~UISplashScreen()
    {
        Hide();
        if (m_pBackImage)
        {
            delete m_pBackImage;
            m_pBackImage = nullptr;
        }
        g_pSplashScreen = nullptr;
    }

    BOOL UISplashScreen::Show(LPCWSTR lpText, UIColor dwBackColor, UIColor dwTextColor, INT dwFontSize, DWORD dwTimeout, FLOAT fBlur)
    {
        // 如果已存在窗口，先关闭
        if (m_pWnd)
        {
            Hide();
        }

        // 保存参数
        m_strText = lpText ? lpText : L"欢迎使用HHBUI，正在启动主窗口...";
        m_dwBackColor = dwBackColor;
        m_dwTextColor = dwTextColor;
        m_dwFontSize = dwFontSize;

        // 获取屏幕尺寸，计算窗口居中位置
        INT screenWidth = GetSystemMetrics(SM_CXSCREEN);
        INT screenHeight = GetSystemMetrics(SM_CYSCREEN);
        INT x = (screenWidth - m_nWidth) / 2;
        INT y = (screenHeight - m_nHeight) / 2;

        // 创建分层窗口，支持透明
        m_pWnd = new UIWnd(
            x, y, m_nWidth, m_nHeight,
            L"HHBUI启动",
            WS_POPUP,
            WS_EX_LAYERED | WS_EX_TOPMOST | WS_EX_TRANSPARENT, // 添加WS_EX_TRANSPARENT使窗口完全透明
            UISTYLE_NOTITLEBAR | UISTYLE_CENTERWINDOW,
            NULL, NULL, 0, OnSplashWndProc
        );

        if (!m_pWnd || !m_pWnd->GethWnd())
        {
            if (m_pWnd)
            {
                delete m_pWnd;
                m_pWnd = nullptr;
            }
            return FALSE;
        }

        // 设置窗口背景色和圆角
        m_pWnd->SetBackgColor(m_dwBackColor);
        m_pWnd->SetRadius(m_nRadius);
        m_pWnd->SetBorderColor(UIColor(0, 0, 0, 0), 0); // 无边框

        // 设置模糊效果
        if (fBlur > 0.0f)
        {
            m_pWnd->SetBlur(fBlur, TRUE);
        }

        // 如果有背景图片，设置背景图片
        if (m_pBackImage)
        {
            m_pWnd->SetBackgImage(m_pBackImage, false, 0, 0, bir_default, 0, bif_disablescale);
        }

        // 重绘窗口内容
        RedrawWindow();

        // 显示窗口
        HWND hWnd = m_pWnd->GethWnd();
        if (hWnd)
        {
            ShowWindow(hWnd, SW_SHOW);
            SetWindowPos(hWnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);

            // 确保窗口居中显示
            m_pWnd->CenterFrom();
        }

        // 如果设置了超时，创建定时器
        if (dwTimeout > 0 && m_pWnd && m_pWnd->GethWnd())
        {
            HWND hWnd = m_pWnd->GethWnd();
            m_dwTimerId = SetTimer(hWnd, 1, dwTimeout, NULL);
        }

        return TRUE;
    }

    void UISplashScreen::Hide()
    {
        // 关闭定时器
        if (m_dwTimerId && m_pWnd && m_pWnd->GethWnd())
        {
            KillTimer(m_pWnd->GethWnd(), m_dwTimerId);
            m_dwTimerId = 0;
        }

        // 释放窗口资源
        if (m_pWnd)
        {
            m_pWnd->Show(SW_HIDE);
            delete m_pWnd;
            m_pWnd = nullptr;
        }
    }

    void UISplashScreen::UpdateText(LPCWSTR lpText)
    {
        if (!lpText) return;

        m_strText = lpText;

        // 重绘窗口内容
        if (m_pWnd && m_pWnd->GethWnd())
        {
            RedrawWindow();
        }
    }

    void UISplashScreen::SetProgress(INT nProgress)
    {
        // 限制进度值范围
        m_nProgress = std::max(0, std::min(100, nProgress));

        // 如果未显示进度条，则显示
        if (!m_bShowProgress)
        {
            m_bShowProgress = TRUE;
        }

        // 重绘窗口内容
        if (m_pWnd && m_pWnd->GethWnd())
        {
            RedrawWindow();
        }
    }

    void UISplashScreen::ShowProgress(BOOL bShow)
    {
        if (m_bShowProgress != bShow)
        {
            m_bShowProgress = bShow;

            // 重绘窗口内容
            if (m_pWnd && m_pWnd->GethWnd())
            {
                RedrawWindow();
            }
        }
    }

    BOOL UISplashScreen::SetBackgImage(LPCWSTR lpImagePath)
    {
        // 如果传入的路径为空，清除背景图片
        if (!lpImagePath)
        {
            if (m_pBackImage)
            {
                delete m_pBackImage;
                m_pBackImage = nullptr;
            }
            return TRUE;
        }

        // 加载新图片
        m_pBackImage = new UIImage(lpImagePath);
        if (!m_pBackImage)
        {
            delete m_pBackImage;
            m_pBackImage = nullptr;
            return FALSE;
        }

        // 如果窗口已创建，更新背景图片
        if (m_pWnd && m_pWnd->GethWnd())
        {
            m_pWnd->SetBackgImage(m_pBackImage, false, 0, 0, bir_default, 0, bif_disablescale);
            RedrawWindow();
        }

        return TRUE;
    }

    BOOL UISplashScreen::SetBackgImage(LPVOID pImageData, size_t nSize)
    {
        // 如果传入的数据为空，清除背景图片
        if (!pImageData || nSize == 0)
        {
            if (m_pBackImage)
            {
                delete m_pBackImage;
                m_pBackImage = nullptr;
            }
            return TRUE;
        }

        // 加载新图片
        m_pBackImage = new UIImage(pImageData, nSize);
        if (!m_pBackImage)
        {
            delete m_pBackImage;
            m_pBackImage = nullptr;
            return FALSE;
        }

        // 如果窗口已创建，更新背景图片
        if (m_pWnd && m_pWnd->GethWnd())
        {
            m_pWnd->SetBackgImage(m_pBackImage, false, 0, 0, bir_default, 0, bif_disablescale);
            RedrawWindow();
        }

        return TRUE;
    }

    BOOL UISplashScreen::SetBackgImage(UIImage* pImage)
    {
        // 如果传入的图片为空，清除背景图片
        if (!pImage)
        {
            if (m_pBackImage)
            {
                delete m_pBackImage;
                m_pBackImage = nullptr;
            }
            return TRUE;
        }

        // 设置新图片
        if (m_pBackImage)
        {
            delete m_pBackImage;
        }
        m_pBackImage = pImage;

        // 如果窗口已创建，更新背景图片
        if (m_pWnd && m_pWnd->GethWnd())
        {
            m_pWnd->SetBackgImage(m_pBackImage, false, 0, 0, bir_default, 0, bif_disablescale);
            RedrawWindow();
        }

        return TRUE;
    }

    void UISplashScreen::SetSize(INT nWidth, INT nHeight)
    {
        // 限制最小尺寸
        m_nWidth = std::max(200, nWidth);
        m_nHeight = std::max(100, nHeight);

        // 如果窗口已创建，调整大小
        if (m_pWnd && m_pWnd->GethWnd())
        {
            // 获取窗口当前位置
            ExRectF rect;
            m_pWnd->GetRect(rect);
            // 调整大小
            m_pWnd->Move((int)rect.left, (int)rect.top, m_nWidth, m_nHeight, TRUE);
            m_pWnd->CenterFrom();
            RedrawWindow();
        }
    }

    void UISplashScreen::SetPosition(INT x, INT y)
    {
        // 如果窗口已创建，调整位置
        if (m_pWnd && m_pWnd->GethWnd())
        {
            m_pWnd->Move(x, y, CW_USEDEFAULT, CW_USEDEFAULT, TRUE);
        }
    }

    void UISplashScreen::SetRadius(INT nRadius)
    {
        m_nRadius = nRadius;

        // 如果窗口已创建，设置圆角
        if (m_pWnd && m_pWnd->GethWnd())
        {
            m_pWnd->SetRadius(m_nRadius);
            RedrawWindow();
        }
    }

    void UISplashScreen::RedrawWindow()
    {
        if (!m_pWnd || !m_pWnd->GethWnd()) return;

        HWND hWnd = m_pWnd->GethWnd();

        // 初始化GDI+
        Gdiplus::GdiplusStartupInput gdiplusStartupInput;
        ULONG_PTR gdiplusToken;
        Gdiplus::GdiplusStartup(&gdiplusToken, &gdiplusStartupInput, NULL);

        // 使用UpdateLayeredWindow实现真正的每像素透明
        HDC hdcScreen = GetDC(NULL);
        HDC hdcMem = CreateCompatibleDC(hdcScreen);

        RECT rcWindow;
        GetWindowRect(hWnd, &rcWindow);
        int nWidth = rcWindow.right - rcWindow.left;
        int nHeight = rcWindow.bottom - rcWindow.top;

        HBITMAP hBitmap = CreateCompatibleBitmap(hdcScreen, nWidth, nHeight);
        HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);

        // 创建GDI+绘图对象
        Gdiplus::Graphics graphics(hdcMem);
        graphics.SetSmoothingMode(Gdiplus::SmoothingModeHighQuality);
        graphics.SetTextRenderingHint(Gdiplus::TextRenderingHintClearTypeGridFit);

        // 清除背景为透明
        graphics.Clear(Gdiplus::Color(0, 0, 0, 0));

        // 创建字体
        Gdiplus::FontFamily fontFamily(L"微软雅黑");
        Gdiplus::Font font(&fontFamily, static_cast<float>(m_dwFontSize),
            Gdiplus::FontStyleRegular, Gdiplus::UnitPixel);

        // 创建文本刷子 - 确保文字不透明
        Gdiplus::Color textColor(
            255, // Alpha通道始终为255，确保文字不透明
            m_dwTextColor.GetR(),
            m_dwTextColor.GetG(),
            m_dwTextColor.GetB()
        );
        Gdiplus::SolidBrush textBrush(textColor);

        // 创建文本布局
        Gdiplus::StringFormat stringFormat;
        stringFormat.SetAlignment(Gdiplus::StringAlignmentCenter);
        stringFormat.SetLineAlignment(Gdiplus::StringAlignmentCenter);

        // 绘制文本
        Gdiplus::RectF layoutRect(0.0f, 0.0f, static_cast<float>(nWidth), static_cast<float>(nHeight - 50));
        graphics.DrawString(
            m_strText.c_str(),
            -1,
            &font,
            layoutRect,
            &stringFormat,
            &textBrush
        );

        // 如果需要显示进度条
        if (m_bShowProgress)
        {
            // 进度条背景 - 半透明
            Gdiplus::Color progressBgColor(80, 255, 255, 255); // 半透明白色背景
            Gdiplus::SolidBrush progressBgBrush(progressBgColor);

            // 调整进度条宽度，留出更多空间给百分比文字
            float progressBarWidth = static_cast<float>(nWidth - 150); // 减少宽度，为百分比留出更多空间
            Gdiplus::RectF progressBgRect(50.0f, static_cast<float>(nHeight - 40), progressBarWidth, 10.0f);

            // 绘制圆角进度条背景
            Gdiplus::GraphicsPath path;
            float radius = 5.0f; // 圆角半径
            path.AddArc(progressBgRect.X, progressBgRect.Y, radius * 2, radius * 2, 180, 90);
            path.AddArc(progressBgRect.X + progressBgRect.Width - radius * 2, progressBgRect.Y, radius * 2, radius * 2, 270, 90);
            path.AddArc(progressBgRect.X + progressBgRect.Width - radius * 2, progressBgRect.Y + progressBgRect.Height - radius * 2, radius * 2, radius * 2, 0, 90);
            path.AddArc(progressBgRect.X, progressBgRect.Y + progressBgRect.Height - radius * 2, radius * 2, radius * 2, 90, 90);
            path.CloseFigure();
            graphics.FillPath(&progressBgBrush, &path);

            // 进度条前景 - 不透明
            Gdiplus::Color progressColor;
            // 根据进度值改变颜色
            if (m_nProgress < 30) {
                progressColor = Gdiplus::Color(255, 255, 100, 100); // 红色
            }
            else if (m_nProgress < 70) {
                progressColor = Gdiplus::Color(255, 255, 200, 0); // 黄色
            }
            else {
                progressColor = Gdiplus::Color(255, 100, 255, 100); // 绿色
            }

            Gdiplus::SolidBrush progressBrush(progressColor);
            float progressWidth = progressBarWidth * m_nProgress / 100.0f;

            // 确保进度条宽度至少为圆角直径，否则绘制会出问题
            progressWidth = std::max(progressWidth, radius * 2);

            // 绘制圆角进度条前景
            Gdiplus::RectF progressRect(50.0f, static_cast<float>(nHeight - 40), progressWidth, 10.0f);
            Gdiplus::GraphicsPath progressPath;
            progressPath.AddArc(progressRect.X, progressRect.Y, radius * 2, radius * 2, 180, 90);
            progressPath.AddArc(progressRect.X + progressRect.Width - radius * 2, progressRect.Y, radius * 2, radius * 2, 270, 90);
            progressPath.AddArc(progressRect.X + progressRect.Width - radius * 2, progressRect.Y + progressRect.Height - radius * 2, radius * 2, radius * 2, 0, 90);
            progressPath.AddArc(progressRect.X, progressRect.Y + progressRect.Height - radius * 2, radius * 2, radius * 2, 90, 90);
            progressPath.CloseFigure();
            graphics.FillPath(&progressBrush, &progressPath);

            // 显示百分比文本 - 调整位置，增加与进度条的间距
            std::wstring percentText = std::to_wstring(m_nProgress) + L"%";
            Gdiplus::Font percentFont(&fontFamily, 16.0f, Gdiplus::FontStyleRegular, Gdiplus::UnitPixel);

            // 调整百分比文字位置，向右移动，增加与进度条的间距
            Gdiplus::RectF percentRect(progressBgRect.X + progressBgRect.Width + 15.0f, // 增加15像素的间距
                progressBgRect.Y - 5.0f, // 稍微上移，使其与进度条垂直居中
                100.0f, // 给百分比文字预留足够宽度
                20.0f); // 给百分比文字预留足够高度

            Gdiplus::StringFormat percentFormat;
            percentFormat.SetAlignment(Gdiplus::StringAlignmentNear); // 左对齐，而不是右对齐
            percentFormat.SetLineAlignment(Gdiplus::StringAlignmentCenter);
            graphics.DrawString(percentText.c_str(), -1, &percentFont, percentRect, &percentFormat, &textBrush);
        }

        // 设置每像素透明度
        BLENDFUNCTION blend = { 0 };
        blend.BlendOp = AC_SRC_OVER;
        blend.BlendFlags = 0;
        blend.SourceConstantAlpha = 255;
        blend.AlphaFormat = AC_SRC_ALPHA;

        POINT ptSrc = { 0, 0 };
        SIZE sizeWnd = { nWidth, nHeight };
        POINT ptDst = { rcWindow.left, rcWindow.top };

        // 更新分层窗口
        UpdateLayeredWindow(hWnd, hdcScreen, &ptDst, &sizeWnd, hdcMem, &ptSrc, 0, &blend, ULW_ALPHA);

        // 清理资源
        SelectObject(hdcMem, hOldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hdcMem);
        ReleaseDC(NULL, hdcScreen);
        Gdiplus::GdiplusShutdown(gdiplusToken);
    }

    LRESULT CALLBACK UISplashScreen::OnSplashWndProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
    {
        if (!g_pSplashScreen) return 0;

        switch (uMsg)
        {
        case WM_TIMER:
            if (wParam == 1 && g_pSplashScreen)
            {
                g_pSplashScreen->Hide();
            }
            break;
        }
        return 0;
    }
}
