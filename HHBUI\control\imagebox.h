﻿#pragma once

namespace HHBUI
{
	class TOAPI UIImageBox : public UIControl
	{
    public:
		enum image_fit {
			fill,		//拉伸以填充整个视图
			contain,	//等比缩放（可能会有空白）
			cover,		//等比缩放填充视图（可能会有裁切）
			none,		//不缩放，按原图大小显示
			scale_down	//原图小于视图大小时等同于none，否则等同于contain
		};

		UIImageBox(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, UIImage *img = NULL, INT nID = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT textFormat = -1);

		//设置图片显示类型
		void SetFit(image_fit fit);

		//设置图片从句柄（设置后请勿销毁图像句柄）
		BOOL SetImg(UIImage *img);
		//设置图片从文件
		BOOL SetImg(LPCWSTR file);
		//设置图片从内存
		BOOL SetImg(LPVOID data, size_t size);
		//设置图片从流
		BOOL SetImg(LPSTREAM stream);
		//取当前图片
		void GetImg(UIImage** lpimg);

		//清除图片
		void Clear();


	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		struct switch_s
		{
			image_fit fit = fill;
			UIImage *img = nullptr;
		}p_data;
	};
}
