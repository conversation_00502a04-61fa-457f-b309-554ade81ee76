﻿#include "pch.h"
#include "imagebox.h"

HHBUI::UIImageBox::UIImageBox(UIBase *hParent, INT x, INT y, INT width, INT height, UIImage *img, INT nID, INT dwStyle, INT dwStyleEx, INT textFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-imagebox", 0, dwStyle, dwStyleEx, nID, textFormat);
	if (img) SetImg(img);
}

void HHBUI::UIImageBox::SetFit(image_fit fit)
{
	p_data.fit = fit;
	Redraw();
}

BOOL HHBUI::UIImageBox::SetImg(UIImage *img)
{
	if (img) {
		if (p_data.img) delete p_data.img;
		p_data.img = img;
		Redraw();
		return TRUE;
	}
	return FALSE;
}

BOOL HHBUI::UIImageBox::SetImg(LPCWSTR file)
{
	return SetImg(new UIImage(file));
}

BOOL HHBUI::UIImageBox::SetImg(LPVOID data, size_t size)
{
	return SetImg(new UIImage(data, size));
}

BOOL HHBUI::UIImageBox::SetImg(LPSTREAM stream)
{
	return SetImg(new UIImage(stream));
}

void HHBUI::UIImageBox::GetImg(UIImage **lpimg)
{
	if (p_data.img) {
		*lpimg = p_data.img;
	}
}

void HHBUI::UIImageBox::Clear()
{
	if (p_data.img) delete p_data.img;
	p_data.img = nullptr;
	Redraw();
}

LRESULT HHBUI::UIImageBox::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_DESTROY) {
		if (p_data.img) delete p_data.img;
		p_data.img = nullptr;
	}
	return S_OK;
}

//等比缩放图片
void ImgResize(UINT ow, UINT oh, UINT sw, UINT sh, INT& nw, INT& nh) {
	if (ow > oh) {
		if (ow != sw) {
			nw = sw;
			float bs = (float)nw / ow;
			nh = (float)oh * bs;
		}
	}
	else {
		if (oh != sh) {
			nh = sh;
			float bs = (float)nh / oh;
			nw = (float)ow * bs;
		}
	}
	
}

void HHBUI::UIImageBox::OnPaintProc(ps_context ps)
{
	if (p_data.img) {
		switch (p_data.fit) {
		case fill: {
			ps.hCanvas->DrawImageRect(p_data.img, 0, 0, ps.uWidth, ps.uHeight);
			break;
		}
		case contain: {
			UINT iw = 0, ih = 0;
			INT nw = 0, nh = 0;
			p_data.img->GetSize(iw, ih);
			ImgResize(iw, ih, ps.uWidth, ps.uHeight, nw, nh);
			ps.hCanvas->DrawImageRect(p_data.img, (ps.uWidth - nw) / 2, (ps.uHeight - nh) / 2, (ps.uWidth - nw) / 2 + nw, (ps.uHeight - nh) / 2 + nh);
			break;
		}
		case cover: {
			ps.hCanvas->DrawImageRect(p_data.img, 0, 0, ps.uWidth, ps.uHeight, ScaleFill);
			break;
		}
		case none: {
			UINT iw = 0, ih = 0;
			p_data.img->GetSize(iw, ih);
			INT iwh = (ps.uWidth - iw), ihh = (ps.uHeight - ih);
			ps.hCanvas->DrawImage(p_data.img, iwh / 2, ihh / 2);
			break;
		}
		case scale_down: {
			UINT iw = 0, ih = 0;
			p_data.img->GetSize(iw, ih);
			if (iw < ps.uWidth && ih < ps.uHeight) {
				INT iwh = (ps.uWidth - iw), ihh = (ps.uHeight - ih);
				ps.hCanvas->DrawImage(p_data.img, iwh / 2, ihh / 2);
			}
			else {
				INT nw = 0, nh = 0;
				ImgResize(iw, ih, ps.uWidth, ps.uHeight, nw, nh);
				ps.hCanvas->DrawImageRect(p_data.img, (ps.uWidth - nw) / 2, (ps.uHeight - nh) / 2, (ps.uWidth - nw) / 2 + nw, (ps.uHeight - nh) / 2 + nh);
			}
			break;
		}
		}
	}
}
