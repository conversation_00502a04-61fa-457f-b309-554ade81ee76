/**
** =====================================================================================
**
**       文件名称: dx11_render_manager.h
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】DirectX11渲染管理器 - 高级渲染管线管理与协调系统 （声明文件）
**
**       主要功能:
**       - 高性能DirectX11渲染管线管理
**       - 智能渲染资源协调与优化
**       - 多渲染目标与纹理管理
**       - 渲染状态管理与切换
**       - 高级渲染工厂模式实现
**       - 渲染性能监控与统计
**       - 跨平台渲染抽象层
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - COM接口规范与智能指针管理
**       - 异常安全保证与错误恢复机制
**       - 高性能渲染管线调度算法
**       - 多线程安全的资源管理
**       - 智能渲染状态缓存与优化
**       - 实时性能监控与调试诊断
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 创建DirectX11渲染管理器系统
**                             2. 实现高性能渲染管线管理
**                             3. 添加智能资源协调机制
**                             4. 支持多渲染目标管理
**                             5. 集成渲染状态管理
**                             6. 添加性能监控与调试
**                             7. 确保线程安全与异常安全
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "dx11_shader.h"
#include "dx11_buffer.h"
#include "common/unknown_impl.hpp"
#include <d3d11.h>
#include <dxgi1_2.h>
#include <wrl.h>
#include <memory>
#include <chrono>

namespace HHBUI
{
	/// DX11纹理实现类
	class UIDx11Texture : public ExUnknownImpl<ITexture>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_ITexture);
		EX_DECLEAR_INTERFACE_2(IID_IRenderObject);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIDx11Texture(ID3D11Device* device, ID3D11DeviceContext* context);
		UIDx11Texture(); // 默认构造函数
		virtual ~UIDx11Texture();

		/// 初始化纹理（用于从已有的D3D11资源创建）
		HRESULT Initialize(ID3D11Texture2D* texture, ID3D11ShaderResourceView* srv);

		// IRenderObject接口实现
		EXMETHOD RenderType GetRenderType() const override { return RenderType::D3D_ONLY; }
		EXMETHOD const RenderStats& GetRenderStats() const override { return m_stats; }
		EXMETHOD void ResetRenderStats() override { m_stats = RenderStats(); }

		// ITexture接口实现
		EXMETHOD HRESULT Create2D(uint32_t width, uint32_t height, DXGI_FORMAT format, 
			const void* initial_data = nullptr, bool render_target = false, bool shader_resource = true) override;
		EXMETHOD HRESULT LoadFromFile(LPCWSTR file_path) override;
		EXMETHOD HRESULT BindAsShaderResource(uint32_t slot) override;
		EXMETHOD HRESULT BindAsRenderTarget() override;
		EXMETHOD void GetSize(uint32_t* width, uint32_t* height) const override;
		EXMETHOD DXGI_FORMAT GetFormat() const override { return m_format; }

		// 获取原生D3D11对象
		ID3D11Texture2D* GetNativeTexture() const { return m_texture.Get(); }
		ID3D11ShaderResourceView* GetShaderResourceView() const { return m_srv.Get(); }
		ID3D11RenderTargetView* GetRenderTargetView() const { return m_rtv.Get(); }

	private:
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;
		Microsoft::WRL::ComPtr<ID3D11Texture2D> m_texture;
		Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_srv;
		Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_rtv;
		
		uint32_t m_width;
		uint32_t m_height;
		DXGI_FORMAT m_format;
		RenderStats m_stats;
	};

	/// DX11渲染状态实现类
	class UIDx11RenderState : public ExUnknownImpl<IRenderState>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IRenderState);
		EX_DECLEAR_INTERFACE_2(IID_IRenderObject);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIDx11RenderState(ID3D11Device* device, ID3D11DeviceContext* context);
		virtual ~UIDx11RenderState();

		// IRenderObject接口实现
		EXMETHOD RenderType GetRenderType() const override { return RenderType::D3D_ONLY; }
		EXMETHOD const RenderStats& GetRenderStats() const override { return m_stats; }
		EXMETHOD void ResetRenderStats() override { m_stats = RenderStats(); }

		// IRenderState接口实现
		EXMETHOD HRESULT SetBlendState(bool enable, D3D11_BLEND src_blend = D3D11_BLEND_SRC_ALPHA, 
			D3D11_BLEND dest_blend = D3D11_BLEND_INV_SRC_ALPHA) override;
		EXMETHOD HRESULT SetDepthStencilState(bool depth_enable, bool depth_write_enable = true, 
			D3D11_COMPARISON_FUNC depth_func = D3D11_COMPARISON_LESS) override;
		EXMETHOD HRESULT SetRasterizerState(D3D11_CULL_MODE cull_mode = D3D11_CULL_BACK, 
			D3D11_FILL_MODE fill_mode = D3D11_FILL_SOLID, bool scissor_enable = false) override;
		EXMETHOD HRESULT Apply() override;

	private:
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;
		Microsoft::WRL::ComPtr<ID3D11BlendState> m_blend_state;
		Microsoft::WRL::ComPtr<ID3D11DepthStencilState> m_depth_stencil_state;
		Microsoft::WRL::ComPtr<ID3D11RasterizerState> m_rasterizer_state;
		
		RenderStats m_stats;
		bool m_states_dirty;
	};

	/// DX11高级渲染管理器实现类
	class UIDx11RenderManager : public ExUnknownImpl<IRenderManager>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IRenderManager);
		EX_DECLEAR_INTERFACE_2(IID_IRenderObject);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIDx11RenderManager();
		virtual ~UIDx11RenderManager();

		// IRenderObject接口实现
		EXMETHOD RenderType GetRenderType() const override { return m_render_type; }
		EXMETHOD const RenderStats& GetRenderStats() const override { return m_stats; }
		EXMETHOD void ResetRenderStats() override;

		// IRenderManager接口实现
		EXMETHOD HRESULT Initialize(RenderType render_type, HWND hwnd = nullptr) override;
		EXMETHOD void Shutdown() override;
		EXMETHOD HRESULT BeginFrame() override;
		EXMETHOD HRESULT EndFrame() override;
		EXMETHOD HRESULT Present(bool vsync = true) override;
		EXMETHOD HRESULT CreateShader(ShaderType type, IShader** shader) override;
		EXMETHOD HRESULT CreateBuffer(BufferType type, IBuffer** buffer) override;
		EXMETHOD HRESULT CreateTexture(ITexture** texture) override;
		EXMETHOD HRESULT CreateRenderState(IRenderState** render_state) override;
		EXMETHOD HRESULT SetViewport(float x, float y, float width, float height, 
			float min_depth = 0.0f, float max_depth = 1.0f) override;
		EXMETHOD HRESULT ClearRenderTarget(float r = 0.0f, float g = 0.0f, float b = 0.0f, float a = 1.0f) override;
		EXMETHOD HRESULT ClearDepthStencil(float depth = 1.0f, uint8_t stencil = 0) override;
		EXMETHOD HRESULT Draw(uint32_t vertex_count, uint32_t start_vertex = 0) override;
		EXMETHOD HRESULT DrawIndexed(uint32_t index_count, uint32_t start_index = 0, uint32_t base_vertex = 0) override;
		EXMETHOD HRESULT DrawIndexedInstanced(uint32_t index_count, uint32_t instance_count,
											 uint32_t start_index = 0, uint32_t base_vertex = 0,
											 uint32_t start_instance = 0) override;

		/// 设置顶点缓冲区
		EXMETHOD HRESULT SetVertexBuffer(uint32_t slot, void* buffer, uint32_t stride, uint32_t offset = 0) override;

		/// 设置索引缓冲区
		EXMETHOD HRESULT SetIndexBuffer(void* buffer, DXGI_FORMAT format, uint32_t offset = 0) override;

		/// 设置着色器
		EXMETHOD HRESULT SetShader(void* shader) override;

		/// 设置纹理
		EXMETHOD HRESULT SetTexture(uint32_t slot, void* texture) override;

		/// 清除渲染目标（重载版本，接受纹理参数）
		EXMETHOD HRESULT ClearRenderTarget(void* render_target, const float color[4]) override;

		/// 创建2D纹理
		EXMETHOD HRESULT CreateTexture2D(uint32_t width, uint32_t height, DXGI_FORMAT format,
										 ITexture** texture) override;

		/// 从文件加载纹理
		EXMETHOD HRESULT LoadTextureFromFile(const wchar_t* file_path, ITexture** texture) override;
		EXMETHOD ID3D11Device* GetD3D11Device() const override { return m_device.Get(); }
		EXMETHOD ID3D11DeviceContext* GetD3D11DeviceContext() const override { return m_context.Get(); }
		EXMETHOD ID2D1DeviceContext* GetD2D1DeviceContext() const override { return m_d2d_context.Get(); }
		EXMETHOD void SetDebugMode(bool enable) override { m_debug_mode = enable; }
		EXMETHOD uint64_t GetGPUMemoryUsage() const override;

		// 扩展功能
		HRESULT CreateSwapChain(HWND hwnd, uint32_t width, uint32_t height);
		HRESULT ResizeSwapChain(uint32_t width, uint32_t height);
		HRESULT SetRenderTarget(ITexture* render_target);
		HRESULT SetDepthStencil(ITexture* depth_stencil);

	private:
		HRESULT CreateD3D11Device();
		HRESULT CreateD2D1Device();
		HRESULT CreateDefaultRenderTarget();
		HRESULT UpdateRenderStats();

	private:
		// D3D11核心对象
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;
		Microsoft::WRL::ComPtr<IDXGISwapChain1> m_swap_chain;
		Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_back_buffer_rtv;
		Microsoft::WRL::ComPtr<ID3D11DepthStencilView> m_depth_stencil_view;

		// D2D1对象（用于混合渲染）
		Microsoft::WRL::ComPtr<ID2D1Device> m_d2d_device;
		Microsoft::WRL::ComPtr<ID2D1DeviceContext> m_d2d_context;

		// 管理器
		std::unique_ptr<UIShaderManager> m_shader_manager;
		std::unique_ptr<UIBufferManager> m_buffer_manager;
		std::unique_ptr<UIInputLayoutManager> m_input_layout_manager;

		// 状态
		RenderType m_render_type;
		RenderStats m_stats;
		bool m_debug_mode;
		bool m_in_frame;
		HWND m_hwnd;

		// 性能监控
		std::chrono::high_resolution_clock::time_point m_frame_start_time;
		std::chrono::high_resolution_clock::time_point m_gpu_start_time;
	};

	/// 渲染工厂实现类
	class UIDx11RenderFactory : public ExUnknownImpl<IRenderFactory>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IRenderFactory);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIDx11RenderFactory();
		virtual ~UIDx11RenderFactory();

		// IRenderFactory接口实现
		EXMETHOD HRESULT CreateRenderManager(IRenderManager** render_manager) override;
		EXMETHOD uint32_t GetSupportedRenderTypes() const override;
		EXMETHOD bool IsFeatureSupported(LPCWSTR feature_name) const override;

	private:
		bool CheckD3D11Support() const;
		bool CheckD2D1Support() const;
	};
}
