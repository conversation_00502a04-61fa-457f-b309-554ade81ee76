﻿#pragma once

namespace HHBUI
{
    class TOAPI UILogin : public UIControl
    {
    public:
        UILogin() = default;
        UILogin(UIBase* hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0);

        // 图片适配模式枚举
        enum ImageFitMode {
            FILL,      // 拉伸填充整个区域
            CONTAIN,   // 保持比例缩放，确保整个图片可见
            COVER,     // 保持比例缩放，填满区域（可能裁剪部分图片）
            CENTER,    // 居中显示，不缩放
            TILE       // 重复平铺图片
        };

        // 分割线样式枚举
        enum SeparatorStyle {
            NONE,      // 无分割线
            SOLID,     // 实线
            DASHED,    // 虚线
            GRADIENT   // 渐变线
        };

        // 设置登录标题
        void SetTitle(LPCWSTR lpszTitle);
        void SetSubTitle(LPCWSTR lpszSubTitle);

        // 设置左侧背景图片
        void SetBackgroundImage(LPCWSTR lpszImagePath);
        void SetBackgroundImage(LPVOID pImgData, size_t size);
        void SetBackgroundBlur(FLOAT fBlurRadius);
        void SetBackgroundDark(FLOAT fDarkness);
        void SetBackgroundFitMode(ImageFitMode mode);  // 新增：设置左侧背景适配模式
        
        // 设置右侧背景图片
        void SetRightBackgroundImage(LPCWSTR lpszImagePath);
        void SetRightBackgroundImage(LPVOID pImgData, size_t size);
        void SetRightBackgroundBlur(FLOAT fBlurRadius);
        void SetRightBackgroundDark(FLOAT fDarkness);
        void SetRightBackgroundFitMode(ImageFitMode mode);  // 新增：设置右侧背景适配模式

        // 设置logo图片
        void SetLogo(LPCWSTR lpszImagePath);
        void SetLogo(LPVOID pImgData, size_t size);
        void SetLogoSize(INT size);
        void SetLogoFitMode(ImageFitMode mode);  // 新增：设置Logo适配模式

        // 设置输入框提示文本
        void SetUsernamePlaceholder(LPCWSTR lpszText);
        void SetPasswordPlaceholder(LPCWSTR lpszText);
        
        // 设置输入框边框
        void SetInputBorderColor(UIColor color);      // 新增：设置输入框边框颜色
        void SetInputFocusBorderColor(UIColor color); // 新增：设置输入框聚焦时的边框颜色

        // 设置按钮文本
        void SetLoginButtonText(LPCWSTR lpszText);
        void SetRegisterButtonText(LPCWSTR lpszText);
        void SetRememberMeText(LPCWSTR lpszText);
        void SetForgetPasswordText(LPCWSTR lpszText);

        // 显示/隐藏控件
        void ShowRegisterButton(BOOL bShow);
        void ShowRememberMe(BOOL bShow);
        void ShowForgetPassword(BOOL bShow);
        void ShowSeparator(BOOL bShow);  // 控制是否显示左右区域的分割线
        void SetSeparatorStyle(SeparatorStyle style); // 设置分割线样式
        void SetSeparatorColor(UIColor color); // 设置分割线颜色
        void SetSeparatorWidth(INT width); // 设置分割线宽度

        // 获取用户名和密码
        LPCWSTR GetUsername();
        LPCWSTR GetPassword();
        BOOL GetRememberMe();

        // 注册回调
        void SetLoginCallback(std::function<LRESULT(LPCWSTR, LPCWSTR)> callback);
        void SetRegisterCallback(std::function<LRESULT()> callback);
        void SetForgetPasswordCallback(std::function<LRESULT()> callback);

        // 设置主题颜色
        void SetThemeColor(UIColor primary);
        void EnableLightEffect(BOOL bEnable);

        // 设置边框样式
        void SetInputBorderRadius(INT radius);
        
        // 设置窗口圆角半径
        void SetWindowRadius(INT radius);
        
        // 设置布局模式：自动布局或手动布局
        enum LayoutMode { AUTO_LAYOUT, MANUAL_LAYOUT };
        void SetLayoutMode(LayoutMode mode);
        
        // 设置内容区宽度比例 (0.0-1.0)
        void SetContentWidthRatio(FLOAT ratio);

    protected:
        EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
        EXMETHOD void OnPaintProc(ps_context ps) override;

    private:
        void InitControls();
        void UpdateLayout();
        void ApplyImageFitMode(UIImageBox* imageBox, ImageFitMode mode);  // 新增：应用图片适配模式
        static LRESULT CALLBACK OnLoginButtonEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam);
        static LRESULT CALLBACK OnForgetPasswordEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam);

        struct login_s
        {
            UIStatic* titleLabel = nullptr;
            UIStatic* subTitleLabel = nullptr;
            UIEdit* usernameEdit = nullptr;
            UIEdit* passwordEdit = nullptr;
            UIButton* loginButton = nullptr;
            UIButton* registerButton = nullptr;
            UIButton* forgetPasswordButton = nullptr;
            UICheck* rememberCheckbox = nullptr;
            UIImageBox* backgroundImage = nullptr;   // 左侧背景图
            UIImageBox* rightBgImage = nullptr;      // 右侧背景图
            UIImageBox* logoImage = nullptr;
            UIColor themeColor = UIColor(20, 126, 255, 255); // 默认蓝色主题
            std::wstring title = L"系统登录";
            std::wstring subTitle = L"";
            std::wstring usernamePlaceholder = L"请输入用户名";
            std::wstring passwordPlaceholder = L"请输入密码";
            std::wstring loginButtonText = L"登 录";
            std::wstring registerButtonText = L"注 册";
            std::wstring rememberMeText = L"记住密码";
            std::wstring forgetPasswordText = L"忘记密码?";
            INT logoSize = 80;
            INT inputBorderRadius = 4;
            UIColor inputBorderColor = UIColor(180, 180, 180, 255); // 新增：输入框边框颜色
            UIColor inputFocusBorderColor = UIColor(20, 126, 255, 255); // 新增：输入框聚焦时边框颜色
            FLOAT backgroundBlur = 0.0f;
            FLOAT backgroundDarkness = 0.0f;
            FLOAT rightBgBlur = 0.0f;
            FLOAT rightBgDarkness = 0.0f;
            BOOL showRegister = TRUE;
            BOOL showRememberMe = TRUE;
            BOOL showForgetPassword = FALSE;
            BOOL showSeparator = FALSE;  // 是否显示左右区域分割线，默认不显示
            BOOL enableLightEffect = FALSE;
            SeparatorStyle separatorStyle = SOLID; // 分割线样式，默认为实线
            UIColor separatorColor = UIColor(200, 200, 200, 130); // 分割线颜色，默认浅灰色半透明
            INT separatorWidth = 1; // 分割线宽度，默认1像素
            INT windowRadius = 12; // 窗口圆角半径，默认12像素
            LayoutMode layoutMode = AUTO_LAYOUT;
            FLOAT contentWidthRatio = 0.4f; // 默认内容区宽度比例
            ImageFitMode bgFitMode = COVER;  // 新增：左侧背景图适配模式，默认为覆盖模式
            ImageFitMode rightBgFitMode = COVER;  // 新增：右侧背景图适配模式，默认为覆盖模式
            ImageFitMode logoFitMode = CONTAIN;  // 新增：Logo适配模式，默认为包含模式
            std::function<LRESULT(LPCWSTR, LPCWSTR)> loginCallback = nullptr;
            std::function<LRESULT()> registerCallback = nullptr;
            std::function<LRESULT()> forgetPasswordCallback = nullptr;
        } p_data;
    };
}
