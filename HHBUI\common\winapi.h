﻿#pragma once
#define WM_BPATHBYROUNDEDRECT 1031//通知设置了圆角

#define FLAGS_CHECK(a, b) (((a) & (b)) == (b))
#define FLAGS_ADD(a, b) (a |= (b))
#define FLAGS_DEL(a, b) (a &= ~(b))

#define EWF_BLAYERED 0x04
#define EWF_SIZED 0x08
#define EWF_ACTIVE 0x02
#define EWF_BRENDERING 0x80
#define EWF_BMODAL 0x1000
#define EWF_BLEAVESENT 0x2000
#define EWF_INTED 0x80000000
#define EWF_BTRACKOBJECT 0x40
#define EWF_BMENUINITED 0x800000
#define EWF_BDESTROYWINDOW 0x8000000
#define EWF_BSIZEMOVING 0x10000000
#define EWF_BPOPUPWINDOIWSHOWN 0x100
#define EWF_BCOMPOSITEDCHECK 0x2000000
#define EWF_BTOOLTIPSPOPUP 0x200000
#define EWF_BTOOLTIPSTRACKPOSTION 0x400000
#define EWF_BMENUREPOSTION 0x1000000
#define EWF_BLEFTTRACK 0x200
#define EWF_BRIGHTTRACK 0x400
#define EWF_BMIDTRACK 0x800
#define EWF_BLEFTDTRACK 0x8000
#define EWF_BMENUCONTEXT 0x10
#define EWF_BREDRAWBACKGROUND 0x20
#define EOF_BUSERPROCESSESED 0x01
#define EOF_BPATH 0x10
#define EOF_BAUTOSIZED 0x04
#define EOF_BDOWN 0x400
#define EOF_BNEEDREDRAW 0x8000
#define EOF_BEVENTBUBBLE 0x80000
#define EOF_BDISABLESPACEANDENTER 0x200000
#define EOF_INITED 0x80000000
#define EOF_BIME 0x400000
#define EOF_NOBORDER 0x800 //禁止边框
#define EOF_BMSGBOXCONTROL 0x02
#define EOF_BMENUITEM 0x08

#define SWP_EX_UPDATEPATH 0x20000000
#define SWP_EX_UPDATEOBJECT 0x40000000
#define SWP_EX_NODPISCALE 0x80000000

#define ATOM_DIALOG 32770

#define TIMER_MOUSETRACK 8
#define EMT_DUI -1
#define EMT_OBJECT -2
#define EMT_EASING -4
#define TIMER_BKG 1
#define TIMER_ANIMATION 2
#define TIMER_SCROLLBAR 4
#define TIMER_TOOLTIPS_POPUP 5
#define TIMER_TOOLTIPS_POP 6
#define TIMER_EDIT_CARET 7
#define TIMER_MOUSETRACK 8

#define ATOM_MENU 32768
//条目风格_子菜单
#define EMIS_SUBMENU 1
//条目风格_分隔符
#define EMIS_SEPARATOR 2
#define MB_TIMEDOUT 32000

//内部使用
constexpr int EWS_COMBOWINDOW = 0x100000;//组合框弹出窗口 *内部用
constexpr int EWS_HANDLE = 0x800000;// *内部用
constexpr int EWS_MENU = 0x40000000;//菜单 *内部用
constexpr int EWS_MESSAGEBOX = 0x80000000;//信息框 *内部用

typedef struct ICONDIRENTRY
{
	BYTE bWidth;         // Width, in pixels, of the image
	BYTE bHeight;        // Height, in pixels, of the image
	BYTE bColorCount;    // Number of colors in image (0 if >=8bpp)
	BYTE bReserved;      // Reserved ( must be 0)
	WORD wPlanes;        // Color Planes
	WORD wBitCount;      // Bits per pixel
	DWORD dwBytesInRes;  // How many bytes in this resource?
	DWORD dwImageOffset; // Where in the file is this image?
} ICONDIRENTRY, * LPICONDIRENTRY;

typedef struct ICONDIR
{
	WORD idReserved;           // Reserved (must be 0)
	WORD idType;               // Resource Type (1 for icons)
	WORD idCount;              // How many images?
	ICONDIRENTRY idEntries[1]; // An entry for each image (idCount of 'em)
} ICONDIR, * LPICONDIR;

typedef struct ICONIMAGE
{
	BITMAPINFOHEADER icHeader; // DIB header
	RGBQUAD icColors[1];       // Color table
	BYTE icXOR[1];             // DIB bits for XOR mask
	BYTE icAND[1];             // DIB bits for AND mask
} ICONIMAGE, * LPICONIMAGE;
struct mempoolmsg_s
{
	LPVOID pObj;
	INT uMsg;
	WPARAM wParam;
	LPARAM lParam;
};

namespace HHBUI
{
	extern "C"
	{
		int WINAPI MessageBoxTimeoutA(IN HWND hWnd, IN LPCSTR lpText, IN LPCSTR lpCaption, IN UINT uType, IN WORD wLanguageId, IN DWORD dwMilliseconds);
		int WINAPI MessageBoxTimeoutW(IN HWND hWnd, IN LPCWSTR lpText, IN LPCWSTR lpCaption, IN UINT uType, IN WORD wLanguageId, IN DWORD dwMilliseconds);
	};
	class TOAPI UIWinApi
	{
	public:
		typedef DWORD(WINAPI* _RtlComputeCrc32Proc)(DWORD init_value, const void* ptr, DWORD size);
		typedef BOOL(WINAPI* _UpdateLayeredWindowIndirectProc)(HWND window, const UPDATELAYEREDWINDOWINFO* ulwi);
	
		static HRESULT Init(HINSTANCE hInstance);
		static void UnInit();
		static void GetWndScreenRect(HWND hWnd, RECT& rcMonitor, RECT& rcDesk);
		static void GetWndScreenRectEx(HWND hWnd, RECT& rcMonitor, RECT& rcDesk);

		inline static struct WinApi_s
		{
			_UpdateLayeredWindowIndirectProc UpdateLayeredWindowIndirect;
			HMODULE ntdll;
			HMODULE user32;
			HMODULE Msftedit;
			_RtlComputeCrc32Proc RtlComputeCrc32;
			LOGFONTW* drawing_default_fontLogFont;
			UIFont* default_font;
			HINSTANCE engine_instance;
			BOOL dwMajorVersion, dwDebug;
			FLOAT drawing_default_dpi, CapsdpiX, CapsdpiY;
			HICON hIcon;
			HICON hIconsm;
			UINT dwMessage;
			HHOOK hHookMsgBox;
			HMENU hMenuVS;
			HMENU hMenuHS;
			HMENU hMenuEdit;
		}ToList;
	};

}
