﻿#include "pch.h"
#include "tabs.h"

using namespace std;

HHBUI::UITabs::UITabs(UIBase *hParent, INT x, INT y, INT width, INT height, tabs_type type, INT nID, INT dwStyle, INT dwStyleEx, INT textFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-tabs", 0, dwStyle, dwStyleEx, nID, textFormat);
	p_data.type = type;
	SetColor(color_text_normal, UIColor(29, 29, 31, 255));
	SetColor(color_text_hover, UIColor(253, 253, 255, 255));
	SetHeaderSize();
}

void HHBUI::UITabs::SetTabsType(tabs_type type)
{
	if (p_data.type != type) {
		p_data.type = type;
		
		ReLine();
		RePane();
	}
}

void HHBUI::UITabs::SetTabsPoint(tabs_point point)
{
	if (p_data.point != point) {
		p_data.point = point;

		ReLine();
		RePane();
	}
}

void HHBUI::UITabs::EnableStretch(BOOL stretch)
{
	p_data.bstre = stretch;
	if(!p_data.bstre) {
		p_data.hsize = { UIEngine::fScale(80),UIEngine::fScale(36) };
	}
	ReLine();
	RePane();
}

void HHBUI::UITabs::SetHeaderSize(UINT width, UINT height)
{
	p_data.hsize.x = UIEngine::fScale(width);
	p_data.hsize.y = UIEngine::fScale(height);

	ReLine();
	RePane();
}

void HHBUI::UITabs::SetHeaderColor(UIColor hover, UIColor line)
{
	if (!hover.empty()) p_data.hclr[1] = hover;
	if (!line.empty()) p_data.hclr[0] = line;
}

INT HHBUI::UITabs::AddPane(LPCWSTR title, INT pID)
{
	tabs_panes nps;
	auto pane = new UIPage(this, 0, 0, 1, 1, title, eos_hidden, 0, pID);

	if (pane) {
		nps.view = pane;
		p_data.list.push_back(nps);
		RePane();
		return (INT)p_data.list.size();
	}
	return -1;
}

INT HHBUI::UITabs::DeletePane(INT index, INT pID)
{
	if (index >= 0 && index < (INT)p_data.list.size()) {
		if (p_data.list[index].view) {
			delete p_data.list[index].view;
			p_data.list.erase(p_data.list.begin() + index);
			RePane();
			return (INT)p_data.list.size();
		}
	}
	else if (pID != -1) {
		for (INT i = 0; i < (INT)p_data.list.size(); i++) {
			if (p_data.list[i].view->GetID() == pID) {
				delete p_data.list[i].view;
				p_data.list.erase(p_data.list.begin() + i);
				RePane();
				return (INT)p_data.list.size();
			}
		}
	}
	return -1;
}

BOOL HHBUI::UITabs::SetTitle(INT index, LPCWSTR title)
{
	if (index >= 0 && index < (INT)p_data.list.size()) {
		p_data.list[index].view->SetText(title);
	}
	return FALSE;
}

void HHBUI::UITabs::ClearPane()
{
	DeletePane(-1, 0);
}

HHBUI::UIPage* HHBUI::UITabs::GetPane(INT index, INT pID)
{
	if (index >= 0 && index < (INT)p_data.list.size()) {
		return p_data.list[index].view;
	}
	else if (pID != -1) {
		for (INT i = 0; i < (INT)p_data.list.size(); i++) {
			if (p_data.list[i].view->GetID() == pID) {
				return p_data.list[i].view;
			}
		}
	}
	return nullptr;
}

BOOL HHBUI::UITabs::SetPane(INT index, INT pID)
{
	if (index >= 0 && index < (INT)p_data.list.size()) {
		if (p_data.select >= 0) p_data.list[p_data.select].view->Show(FALSE);
		p_data.select = index;
		p_data.list[index].view->Show(TRUE);
		ReLine();
		Redraw();
		return TRUE;
	}
	else if (pID != -1) {
		for (INT i = 0; i < (INT)p_data.list.size(); i++) {
			if (p_data.list[i].view->GetID() == pID) {
				if (p_data.select >= 0) p_data.list[p_data.select].view->Show(FALSE);
				p_data.select = i;
				p_data.list[i].view->Show(TRUE);
				ReLine();
				Redraw();
				return TRUE;
			}
		}
	}
	return FALSE;
}


void HHBUI::UITabs::ReLine()
{
	if (p_data.point == left) {
		p_data.line.left = p_data.hsize.x - 4;
		p_data.line.top = p_data.select * p_data.hsize.y;
		p_data.line.right = p_data.hsize.x;
		p_data.line.bottom = p_data.line.top + p_data.hsize.y;
	}
	else if (p_data.point == top) {
		p_data.line.left = p_data.select * p_data.hsize.x;
		p_data.line.top = p_data.hsize.y - 4;
		p_data.line.right = p_data.line.left + p_data.hsize.x;
		p_data.line.bottom = p_data.hsize.y;
	}
	else if (p_data.point == right) {
		ExRectF rc;
		GetRect(rc, grt_client, TRUE);
		p_data.line.left = rc.right - p_data.hsize.x;
		p_data.line.top = p_data.select * p_data.hsize.y;
		p_data.line.right = p_data.line.left + 4;
		p_data.line.bottom = p_data.line.top + p_data.hsize.y;
	}
	else if (p_data.point == bottom) {
		ExRectF rc;
		GetRect(rc, grt_client, TRUE);
		p_data.line.left = p_data.select * p_data.hsize.x;
		p_data.line.top = rc.bottom - p_data.hsize.y;
		p_data.line.right = p_data.line.left + p_data.hsize.x;
		p_data.line.bottom = p_data.line.top + 4;
	}
}

void HHBUI::UITabs::RePane()
{
	if (p_data.list.size() <= 0) return;

	ExRectF rc;
	GetRect(rc, grt_client, TRUE);

	if (p_data.type == none) {
		p_data.hsize.x = 0, p_data.hsize.y = 0;
		for (INT i = 0; i < (INT)p_data.list.size(); i++) {
			p_data.list[i].view->SetPos(rc.left, rc.top, rc.right, rc.bottom);
		}
		return;
	}

	if (p_data.bstre) {
		if (p_data.point == left || p_data.point == right) {
			p_data.hsize.y = rc.bottom / static_cast<float>(p_data.list.size());
		}
		else {
			p_data.hsize.x = rc.right / static_cast<float>(p_data.list.size());
		}
	}

	INT x = 0, y = 0, w = 0, h = 0;
	if (p_data.point == left) {
		for (INT i = 0; i < (INT)p_data.list.size(); i++) {
			p_data.list[i].rc.left = 0;
			p_data.list[i].rc.top = i * p_data.hsize.y;
			p_data.list[i].rc.right = p_data.hsize.x;
			p_data.list[i].rc.bottom = p_data.list[i].rc.top + p_data.hsize.y;

			x = p_data.hsize.x / UIEngine::GetDefaultScale();
			w = rc.right / UIEngine::GetDefaultScale() - x;
			h = rc.bottom / UIEngine::GetDefaultScale();

			p_data.list[i].view->Move(x, y, w, h);
			//p_data.list[i].view->SetColor(color_border, UIColor(56, 33, 91, 255));
		}
	}
	else if (p_data.point == top) {
		for (INT i = 0; i < (INT)p_data.list.size(); i++) {
			p_data.list[i].rc.left = i * p_data.hsize.x;
			p_data.list[i].rc.top = 0;
			p_data.list[i].rc.right = p_data.list[i].rc.left + p_data.hsize.x;
			p_data.list[i].rc.bottom = p_data.hsize.y;

			y = p_data.hsize.y / UIEngine::GetDefaultScale();
			w = rc.right / UIEngine::GetDefaultScale();
			h = (rc.bottom / UIEngine::GetDefaultScale()) - y;

			p_data.list[i].view->Move(x, y, w, h);
			//p_data.list[i].view->SetColor(color_border, UIColor(56, 33, 91, 255));
		}
	}
	else if (p_data.point == right) {
		for (INT i = 0; i < (INT)p_data.list.size(); i++) {
			p_data.list[i].rc.left = rc.right - p_data.hsize.x;
			p_data.list[i].rc.top = i * p_data.hsize.y;
			p_data.list[i].rc.right = rc.right;
			p_data.list[i].rc.bottom = p_data.list[i].rc.top + p_data.hsize.y;

			w = (rc.right / UIEngine::GetDefaultScale()) - p_data.hsize.x / UIEngine::GetDefaultScale();
			h = rc.bottom / UIEngine::GetDefaultScale();

			p_data.list[i].view->Move(x, y, w, h);
			//p_data.list[i].view->SetColor(color_border, UIColor(56, 33, 91, 255));
		}
	}
	else if (p_data.point == bottom) {
		for (INT i = 0; i < (INT)p_data.list.size(); i++) {
			p_data.list[i].rc.left = i * p_data.hsize.x;
			p_data.list[i].rc.top = rc.bottom - p_data.hsize.y;
			p_data.list[i].rc.right = p_data.list[i].rc.left + p_data.hsize.x;
			p_data.list[i].rc.bottom = rc.bottom;

			w = rc.right / UIEngine::GetDefaultScale();
			h = (rc.bottom / UIEngine::GetDefaultScale()) - p_data.hsize.y / UIEngine::GetDefaultScale();
			p_data.list[i].view->Move(x, y, w, h);
			//p_data.list[i].view->SetColor(color_border, UIColor(56, 33, 91, 255));
		}
	}

}


LRESULT HHBUI::UITabs::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_LBUTTONDOWN) {
		if (p_data.hover == -1)
			return S_OK;
		if (p_data.list[p_data.hover].rc.PtInRect(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam))) {
			if (p_data.select != p_data.hover) {
				INT select = p_data.select;
				p_data.list[p_data.select].view->Show(FALSE);
				p_data.list[p_data.hover].view->Show(TRUE);
				p_data.select = p_data.hover;

				if (p_data.point == left || p_data.point == right) {
					p_data.line.top = p_data.hover * p_data.hsize.y;
					p_data.line.bottom = p_data.line.top + p_data.hsize.y;
				}
				else {
					p_data.line.left = p_data.hover * p_data.hsize.x;
					p_data.line.right = p_data.line.left + p_data.hsize.x;
				}
				Redraw();
			}
		}
	}
	else if (uMsg == WM_MOUSEMOVE)
	{
		p_data.hover = -1;
		for (auto i = 0; i < (INT)p_data.list.size(); i++) {
			if (p_data.list[i].rc.PtInRect(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam))) {
				p_data.hover = i;
				Redraw();
				break;
			}
		}
	}
	else if (uMsg == WM_MOUSELEAVE)
	{
		p_data.hover = -1;
		Redraw();
	}
	else if (uMsg == WM_SIZE)
	{
		ReLine();
		RePane();
	}
	else if (uMsg == WM_DESTROY)
	{
		p_data.list.clear();
	}
	return S_OK;
}

void HHBUI::UITabs::OnPaintProc(ps_context ps)
{
	auto br = UIBrush(p_data.hclr[0]);

	if (p_data.type == linear) {

		for (size_t i = 0; i < p_data.list.size(); i++) {
			UIColor textColor = (p_data.hover == i || p_data.select == i) ? p_data.hclr[1] : m_data.Color.crNormal;
			ps.hCanvas->DrawTextByColor(ps.hFont, p_data.list[i].view->GetText(), ps.dwTextFormat, p_data.list[i].rc.left, p_data.list[i].rc.top, p_data.list[i].rc.right, p_data.list[i].rc.bottom, textColor);
		}
		if (p_data.point == left || p_data.point == right)
			ps.hCanvas->FillRect(&br, p_data.line.left, 0, p_data.line.right, ps.uHeight);
		else if (p_data.point == top || p_data.point == bottom)
			ps.hCanvas->FillRect(&br, 0, p_data.line.top, ps.uWidth, p_data.line.bottom);

		br.SetColor(p_data.hclr[1]);
		ps.hCanvas->FillRect(&br, p_data.line.left, p_data.line.top, p_data.line.right, p_data.line.bottom);
	}
	else if (p_data.type == card) {
		br.SetColor(p_data.hclr[1]);
		for (size_t i = 0; i < p_data.list.size(); i++) {
			UIColor rColor = m_data.Color.crNormal;

			if (p_data.select == i) {
				rColor = m_data.Color.crHover;
				ps.hCanvas->FillRoundRect(&br, p_data.list[i].rc.left, p_data.list[i].rc.top, p_data.list[i].rc.right, p_data.list[i].rc.bottom, 2);
			}
			else if (p_data.hover == i) {
				rColor = p_data.hclr[1];
			}
			ps.hCanvas->DrawTextByColor(ps.hFont, p_data.list[i].view->GetText(), ps.dwTextFormat, p_data.list[i].rc.left, p_data.list[i].rc.top, p_data.list[i].rc.right, p_data.list[i].rc.bottom, rColor);
		}
		br.SetColor(p_data.hclr[0]);
		if (p_data.point == left)
			ps.hCanvas->DrawLine(&br, p_data.line.right, 0, p_data.line.right, ps.uHeight, 1);
		else if (p_data.point == top)
			ps.hCanvas->DrawLine(&br, 0, p_data.line.bottom, ps.uWidth, p_data.line.bottom, 1);
		else if (p_data.point == right)
			ps.hCanvas->DrawLine(&br, p_data.line.left, 0, p_data.line.left, ps.uHeight, 1);
		else if (p_data.point == bottom)
			ps.hCanvas->DrawLine(&br, 0, p_data.line.top, ps.uWidth, p_data.line.top, 1);
	}

}
