﻿#include "pch.h"
#include "status_handle.h"

#include <sstream>
#include "Exception.h"

namespace HHBUI
{

	LPCWSTR ExStatusGetText(HRESULT status, bool* free)
	{
		LPWSTR text = nullptr;
		*free = false;

		//通过系统API获取状态码信息
		FormatMessageW(
			FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS | FORMAT_MESSAGE_ALLOCATE_BUFFER,
			nullptr, status, 0, (LPWSTR)&text, 0, nullptr
		);

		if (text) {

			//去掉末尾的回车换行符
			size_t len = wcslen(text);
			if (len > 0) {
				if (text[len - 2] == L'\r') { text[len - 2] = L'\0'; }
				else if (text[len - 1] == L'\n') { text[len - 1] = L'\0'; }
			}

			*free = true;
		}
		return text;
	}
	void ExOutError(std::wstring cls, std::wstring error)
	{
#ifdef _WIN32
#ifdef _DEBUG
		OutputDebugStringW((L"\n" + cls + L" -> " + error).c_str());
#else
		std::wstring errinfo;
		errinfo = L"很抱歉，HHBUI库内部抛出了一个错误 :(\n程序将继续运行 但可能发生崩溃.\n错误信息:\n";
		errinfo += cls + L" -> " + error;
		MessageBoxW(0, errinfo.c_str(), L"HHBUI Library", MB_ICONERROR | MB_OK);
#endif // _DEBUG

#endif
	}

	HRESULT ExStatusHandle(HRESULT status, LPCWSTR file, int line, LPCWSTR text)
	{
		wchar_t buffer[1024]{};

		//生成状态文本信息
		bool free = false;
		LPCWSTR status_text = ExStatusGetText(status, &free);
		swprintf_s(buffer, L"0x%08X(%s) ", status, status_text);
		if (free) { LocalFree((HLOCAL)status_text); }

		//连接附加文本
		if (text) { wcscat_s(buffer, text); }


		//系统默认处理
		ExOutError(_M_DBG_INFO_(M_INFO_DBG), text);
		
		return status;
	}
}