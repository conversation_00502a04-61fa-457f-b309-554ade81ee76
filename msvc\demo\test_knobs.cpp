﻿#include "hhbui.h"
using namespace HHBUI;

UIKnobs* m_Knobs[8];
UIWaveRingView* m_WaveRing[2];
UILoading* m_loadings;
UISlider* m_slider;
UIMenu* m_bMenus;
UIStatic* m_times[3];

LRESULT CALLBACK OnWndKnoMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	if (uMsg == WM_TIMER)
	{
		float t = UIEngine::GetTime();
		float h = std::fabs(std::sin(t * 0.2f));
		float s = std::fabs(std::sin(t * 0.1f)) * 0.5f + 0.4f;

		auto highlight = UIColor::HSV(h, s, 0.75f, 1.0f);
		auto base = UIColor::HSV(h, s, 0.5f, 1.0f);
		auto lowlight = UIColor::HSV(h, s, 0.8f, 1.0f);
		for (size_t i = 0; i < 8; ++i) {
			m_Knobs[i]->SetColorV(base, lowlight, highlight);
		}
		//m_WaveRing[0]->SetOrColor(base);
		m_WaveRing[0]->SetFxColor(base);
		m_WaveRing[1]->SetOrColor(base);
		m_loadings->SetCrHue(base);
		m_loadings->Redraw();

		m_slider->SetSliderColor({}, base);
		m_slider->SetValue(m_WaveRing[0]->GetBassCurPosition());
		m_slider->Redraw();

		wchar_t buff[16];
		auto pTime = m_WaveRing[0]->GetBassPlayTime();
		int minutes = static_cast<int>(pTime) / 60;
		int seconds = static_cast<int>(pTime) % 60;
		swprintf_s(buff, L"%d:%.2d", minutes, seconds);
		m_times[1]->SetText(buff);

		window->SetShadowColor(base);
		window->SetBorderColor(lowlight);

		UINT curfps = window->GetLastFPS();
		auto fps = vstring::format(L"App: %.2f ms/frame (%.f FPS)", 100.0f / curfps, (float)curfps);
		m_times[2]->SetText(fps.c_str());
	}
	else if (uMsg == WM_RBUTTONUP)
	{
		m_bMenus->Popup(window, 0, 0, 0, {}, {}, UIColor(255, 255, 255));
	}
	else if (uMsg == WM_COMMAND)
	{
		if (wParam == 1000)
		{
			LPCWSTR retstrFile;
			if (UIfileOpenDlg(0, 1, L"所有支持的音频格式|*.mp3;*.wma;*.wav;*.flac;*.ogg;*.oga;*.m4a;*.mp4;*.cue;*.mp2;*.mp1;*.aif;*.aiff;*.asf;*.cda;*.fla;*.midi;*.mid;*.rmi;*.kar;*.aac;*.ape;*.mac|基本音频格式|*.mp3;*.wma;*.wav;*.flac;*.ogg;*.oga;*.m4a;*.mp4;*.cue;*.mp2;*.mp1;*.aif;*.aiff;*.asf|CD Audio|*.cda|Free Lossless Audio Codec|*.flac;*.fla|MIDI|*.midi;*.mid;*.rmi;*.kar|Windows Media Audio|*.wma|Advanced Audio Coding|*.aac|Monkey's Audio|*.ape;*.mac|所有文件|*.*||",
				0, L"打开音乐", &retstrFile))
			{
				auto handle = m_WaveRing[0]->SetBassPlayFile(retstrFile);
				if (handle)
				{
					window->SetTimer(1000, 100);
					auto totalTime = m_WaveRing[0]->GetBassPlayTotalTime();
					wchar_t buff[16];
					if (totalTime == 0)
						wcscpy_s(buff, L"--:--");
					else
					{
						int minutes = static_cast<int>(totalTime) / 60;       // 总秒数除以 60 得到分钟数
						int seconds = static_cast<int>(totalTime) % 60;       // 余下的秒数
						swprintf_s(buff, L"%d:%.2d", minutes, seconds);
					}
					m_times[0]->SetText(buff);
					m_WaveRing[1]->SetBassHandle(handle);
					output(retstrFile);

					m_loadings->SetText(UIGetFileName(retstrFile, true));
					m_bMenus->EnableItem(1001, FALSE);
					m_bMenus->EnableItem(1003, FALSE);
				}
			}
		}
		else if (wParam == 1001)
		{

		}
		else if (wParam == 1003)
		{

		}
	}
	else if (uMsg == MCM_GETCOLOR)
	{
		auto obj = (UIColorPicker*)UIView;

		UIColor Color;
		obj->GetColour(Color);
		m_WaveRing[0]->SetFxColor(Color);
		m_WaveRing[1]->SetFxColor(Color);
	}
	return S_OK;
}

LRESULT CALLBACK OnKnoEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	if (nCode == WMM_KNOB_VALUE)
	{
		auto obj = (UIKnobs*)UIView;
		if (obj == m_Knobs[0])
		{

		}
		else if (obj == m_Knobs[3])
		{
			m_WaveRing[0]->SetMaximumlight(obj->GetValue());
			m_WaveRing[1]->SetMaximumlight(obj->GetValue());
		}
		else if (obj == m_Knobs[4])
		{
			m_WaveRing[0]->SetBassVolPan(obj->GetValue());
		}
		else if (obj == m_Knobs[5])
		{
			m_WaveRing[0]->SetBassVolume(obj->GetValue());
		}
		output(obj->GetValue());
	}
	else if (nCode == WMM_LUP)
	{
		if (nID == 1001)
		{

		}
	}
	else if (nCode == TBM_GETPOS)
	{
		m_WaveRing[0]->SetBassCurPosition(lParam);
	}
	return S_OK;
}
void testknobs(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 950, 739, L"hello Knobs", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_MOVEABLE, hWnd, NULL, 0, OnWndKnoMsgProc);
	window->SetBackgColor(UIColor(0, 0, 0, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	info_objcaption Info{};
	Info.crTitle = UIColor(255, 255, 255, 255);
	Info.crbutton_normal = UIColor(255, 255, 255, 255);
	Info.crbutton_hover = UIColor(255, 255, 255, 255);
	Info.crbutton_down = UIColor(200, 200, 200, 255);
	//Info.dwTextFormat = Center | Middle | EndEllipsis;
	window->SetCaptionInfo(&Info);

	window->Layout_Init(elt_linear);
	window->Layout_SetProp(elp_linear_direction, elp_direction_h);
	window->Layout_SetProp(elp_linear_dalign, elp_linear_dalign_center);

	m_bMenus = new UIMenu();
	m_bMenus->Append(MF_STRING, 1000, L"打开文件");
	m_bMenus->Append(MF_STRING | MF_DISABLED, 1001, L"暂停播放");
	m_bMenus->Append(MF_SEPARATOR, 1002);
	m_bMenus->Append(MF_STRING | MF_DISABLED, 1003, L"继续播放\tCtrl+&Z");

	m_loadings = new UILoading(window, 70, 685 - 40, 800, 40);
	m_loadings->SetCrHue(UIColor(255, 255, 255));
	m_loadings->SetStyle(e_st_textfading);
	m_loadings->SetText(L"hello HHBUI");
	m_loadings->SetFontFromFamily(L"字魂甜豆体(商用需授权)", 16, FontStyle::Bold);

	m_slider = new UISlider(window, 100, 685, 500, 20);
	m_slider->SetEvent(TBM_GETPOS, OnKnoEvent);
	m_slider->SetType(UISlider::slider_type::stroke);
	m_slider->SetSliderColor(UIColor(194, 195, 201, 55), UIColor(202, 81, 0, 255));
	m_slider->SetRange(0, 1000);
	m_slider->SetBarSize(15);
	m_slider->IsShwoText(FALSE);

	m_times[0] = new UIStatic(window, 20, 685, 100, 20, L"00:00", 0, eos_ex_transparent);
	m_times[0]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));

	m_times[1] = new UIStatic(window, 590, 685, 100, 20, L"00:00", 0, eos_ex_transparent);
	m_times[1]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));

	m_times[2] = new UIStatic(window, 600, 685, 400, 20, L"FPS:0", 0, eos_ex_transparent);
	m_times[2]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));

	m_Knobs[0] = new UIKnobs(window, 30, 30, 60, 100, L"Gain");
	m_Knobs[0]->SetKnob(0.f, -6.0f, 6.0f, 0.1f, L"%.1fdB", KnobVariant_Tick, KnobFlags_Normal, 10);
	m_Knobs[0]->SetEvent(WMM_KNOB_VALUE, OnKnoEvent);
	m_Knobs[0]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	window->Layout_AddChild(m_Knobs[0]);

	m_Knobs[1] = new UIKnobs(window, 100, 30, 60, 100, L"Mix");
	m_Knobs[1]->SetKnob(0.f, -1.0f, 1.0f, 0.1f, L"%.1f", KnobVariant_Stepped);
	m_Knobs[1]->SetEvent(WMM_KNOB_VALUE, OnKnoEvent);
	m_Knobs[1]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	window->Layout_AddChild(m_Knobs[1]);

	m_Knobs[2] = new UIKnobs(window, 170, 30, 60, 100, L"Pitch");
	m_Knobs[2]->SetKnob(0.f, -6.0f, 6.0f, 0.1f, L"%.1f", KnobVariant_WiperOnly);
	m_Knobs[2]->SetEvent(WMM_KNOB_VALUE, OnKnoEvent);
	m_Knobs[2]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	window->Layout_AddChild(m_Knobs[2]);

	m_Knobs[3] = new UIKnobs(window, 240, 30, 60, 100, L"Dry");
	m_Knobs[3]->SetKnob(0.5f, 0.f, 0.9f, 0.1f, L"%.1f", KnobVariant_Stepped, KnobFlags_Normal, 1.f, 10, 1.570796f, 3.141592f);
	m_Knobs[3]->SetEvent(WMM_KNOB_VALUE, OnKnoEvent);
	m_Knobs[3]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	window->Layout_AddChild(m_Knobs[3]);

	m_Knobs[4] = new UIKnobs(window, 310, 30, 60, 100, L"Pan");
	m_Knobs[4]->SetKnob(0.f, -1.f, 1.f, 0.1f, L"%.1f", KnobVariant_Stepped, KnobFlags_Normal, 1.f, 20);
	m_Knobs[4]->SetEvent(WMM_KNOB_VALUE, OnKnoEvent);
	m_Knobs[4]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	window->Layout_AddChild(m_Knobs[4]);

	m_Knobs[5] = new UIKnobs(window, 380, 30, 60, 100, L"Vol");
	m_Knobs[5]->SetKnob(1.f, 0.f, 1.f, 0.1f, L"%.1f", KnobVariant_Space, KnobFlags_DragVertical, 1.f, 0);
	m_Knobs[5]->SetEvent(WMM_KNOB_VALUE, OnKnoEvent);
	m_Knobs[5]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	window->Layout_AddChild(m_Knobs[5]);

	m_Knobs[6] = new UIKnobs(window, 450, 30, 60, 100, L"Bass");
	m_Knobs[6]->SetKnob(-2.4f, -6.0, 6.0, 0.1f, L"%.1fdB", KnobVariant_Wiper);
	m_Knobs[6]->SetEvent(WMM_KNOB_VALUE, OnKnoEvent);
	m_Knobs[6]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	window->Layout_AddChild(m_Knobs[6]);

	m_Knobs[7] = new UIKnobs(window, 520, 30, 60, 100, L"High");
	m_Knobs[7]->SetKnob(4.f, -6.0, 6.0, 0.1f, L"%.1fdB", KnobVariant_WiperDot);
	m_Knobs[7]->SetEvent(WMM_KNOB_VALUE, OnKnoEvent);
	m_Knobs[7]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	window->Layout_AddChild(m_Knobs[7]);
	/*
	auto pickercr = new UIColorPicker(window, 950 - 200, 30, 110, 30);
	pickercr->SetColor(color_border, UIColor(255, 255, 255, 127));
	pickercr->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	pickercr->SetColour(UIColor(255, 255, 255, 127));
	pickercr->SetColourArrow(UIColor(194, 195, 201, 255));
	pickercr->SetRadius(15, 15, 15, 15);
	pickercr->SetMsgProc(OnWndKnoMsgProc);
	*/
	m_WaveRing[0] = new UIWaveRingView(window, (950 - 450) / 2, 100, 450, 450, L"HHBUI.2025");
	std::wstring wstr = L"bass\\bass.dll";
	std::wstring wstr_fx = L"bass\\bass_fx.dll";
#ifdef _WIN64
	wstr = L"bass\\bassX64.dll";
	wstr_fx = L"bass\\bass_fxX64.dll";
#endif
	auto handle = m_WaveRing[0]->SetBassDll(wstr.c_str(), TRUE);
	//m_WaveRing[0]->SetBassFXDll(wstr_fx.c_str());
	m_WaveRing[0]->SetColor(color_text_normal, UIColor(194, 195, 201, 255));
	m_WaveRing[0]->SetType(DyCircle);
	m_WaveRing[0]->SetAlbumSize(200);
	m_WaveRing[0]->SetAlbum(new UIImage(L"icons\\100.jpg"));
	m_WaveRing[0]->SetOrColor({});
	m_WaveRing[0]->SetOrDrawFx(TRUE);
	m_WaveRing[0]->SetFxORbloom(TRUE);
	m_WaveRing[0]->SetFontFromFamily(NULL, 32, FontStyle::Normal);

	m_WaveRing[1] = new UIWaveRingView(window, 130, 739 - 230, 700, 150);
	m_WaveRing[1]->SetType(RectangleFFT);
	m_WaveRing[1]->SetBassDllHandle(handle);//此方法免初始化 共享内存的句柄
	m_WaveRing[1]->SetOrDrawFx(TRUE);
	//m_WaveRing[1]->SetFxORbloom(TRUE);

	window->Show();
	window->MessageLoop();
}