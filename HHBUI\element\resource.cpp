﻿#include "pch.h"
#include "resource.h"
#include <iostream>
extern "C" {
#include "ThirdParty/zlib/unzip.h"
}

HHBUI::UIZip::UIZip(LPVOID pBytes, DWORD dwByteCounts, LPCSTR pszPsw)
{
    if (Open((LPBYTE)pBytes, dwByteCounts)) return;
    SetPassword(pszPsw);
	m_IsPath = FALSE;
}

HHBUI::UIZip::UIZip(LPCSTR path, LPCSTR pszPsw)
{
    INT w64 = 0;
#ifdef _WIN64
    w64 = 1;
#endif
    m_lpKey = pszPsw;
    m_crcTzip = unzOpen(path, w64);
	m_IsPath = TRUE;
}

HHBUI::UIZip::~UIZip()
{
    if (!m_IsPath)
    {
        Close();
    }
    else
    {
        unzClose(m_crcTzip);
        m_crcTzip = nullptr;
    }
}

void HHBUI::UIZip::EnumFile(LPCWSTR pszPath, EnumFileCallback funEnumCB, LPARAM lp)
{
    if (!m_IsPath)
    {
        EnumFile(pszPath, funEnumCB, lp);
    }
    else
    {
        if (m_crcTzip == nullptr) {
            return;
        }

        unz_global_info globalInfo;
        if (unzGetGlobalInfo(m_crcTzip, &globalInfo) != UNZ_OK) {
            std::cerr << "Error getting global info" << std::endl;
            unzClose(m_crcTzip);
            return;
        }
        for (uLong i = 0; i < globalInfo.number_entry; ++i) {
            char fileName[MAX_PATH];
            unz_file_info fileInfo;

            if (unzGetCurrentFileInfo(m_crcTzip, &fileInfo, fileName, MAX_PATH, nullptr, 0, nullptr, 0) != UNZ_OK) {
                std::cerr << "Error getting file info" << std::endl;
                break;
            }
            std::wstring wFileName = vstring::a2w(fileName);
            funEnumCB(wFileName.c_str(), lp);

            if (i < globalInfo.number_entry - 1) {
                if (unzGoToNextFile(m_crcTzip) != UNZ_OK) {
                    std::cerr << "Error going to next file" << std::endl;
                    break;
                }
            }
        }
    }
}

INT HHBUI::UIZip::GetCount()
{
    if (!m_IsPath)
    {
        return GetEntries();
    }
    else
    {
        unz_global_info64 GlobalInfo{ 0 };
        if (unzGetGlobalInfo64(m_crcTzip, &GlobalInfo) == NULL)
        {
            return GlobalInfo.number_entry;
        }
        return 0;
    }
}

BOOL HHBUI::UIZip::ReadSource(LPCWSTR lpname, LPVOID* retData, DWORD* uncompressed_size)
{
    if (!m_IsPath)
    {
        CZipFile zfProps;
        BOOL bIdx = GetFile(lpname, zfProps);
        if (!bIdx) return FALSE;

        LPVOID buffer = (LPVOID)malloc(zfProps.GetSize());
        if (buffer)
            memcpy(buffer, zfProps.GetData(), zfProps.GetSize());

        *retData = buffer;
        *uncompressed_size = zfProps.GetSize();
        zfProps.Close();
        return TRUE;
    }
    else
    {
        if (unzLocateFile(m_crcTzip, vstring::w2a(lpname).c_str(), 0) == NULL)
        {
            unzOpenCurrentFilePassword(m_crcTzip, m_lpKey);
            unz_file_info pfile_info{ 0 };
            unzGetCurrentFileInfo(m_crcTzip, &pfile_info, 0, 1024, 0, 0, 0, 0);//取当前文件
            if (pfile_info.uncompressed_size > 0)
            {
                char* buffer = (char*)malloc(pfile_info.uncompressed_size);
                unzReadCurrentFile(m_crcTzip, buffer, pfile_info.uncompressed_size);
                *uncompressed_size = pfile_info.uncompressed_size;
                *retData = buffer;
                unzCloseCurrentFile(m_crcTzip);
                return TRUE;
            }
        }
        return FALSE;
    }
}

BOOL HHBUI::UIZip::ReadRcSource(WORD lpname, LPCWSTR lpType, std::vector<CHAR>* retData)
{
    BOOL fOK = FALSE;
    /* 检查参数有效性 */
    if (lpname)
    {
        /* 查找资源 */
        HRSRC hRsrc = FindResourceW(NULL, MAKEINTRESOURCE(lpname), lpType);
        if (NULL == hRsrc)
            return (FALSE);

        /* 获取资源的大小 */
        DWORD dwSize = SizeofResource(NULL, hRsrc);
        if (dwSize == 0)
        {
            return (FALSE);
        }

        /* 加载资源 */
        HGLOBAL hGlobal = LoadResource(NULL, hRsrc);
        if (hGlobal == 0)
        {
            return (FALSE);
        }

        /* 锁定资源 */
        LPVOID pBuffer = LockResource(hGlobal);
        if (pBuffer == NULL)
        {
            return (FALSE);
        }

        (*retData).resize(dwSize);
        RtlMoveMemory((*retData).data(), pBuffer, dwSize);
        fOK = TRUE;
        /*
         * 在资源使用完毕后我们可以不需要使用 UnlockResource和FreeResource来手动地释放资源
         * 因为它们都是16位Windows遗留下来的，在Win32中，在使用完毕后系统会自动回收。
         */
        UnlockResource(hGlobal);
        FreeResource(hGlobal);
    }
    return (fOK);
}

