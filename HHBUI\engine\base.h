﻿#pragma once
#include <thread>
#include <condition_variable>
#include <map>
#include <unordered_map>
#include <queue>
using namespace std::chrono;
namespace HHBUI
{
	//[线程安全] 单例线程类 一般用于渲染线程
	class TOAPI UIRenderThread
	{
	public:
		UIRenderThread();
		~UIRenderThread();

		//创建线程 @param pause - 是否暂停线程
		virtual void Start(bool pause = false);
		//暂停线程
		virtual void Pause();
		//还原线程
		virtual void Resume();
		//关闭线程
		virtual void Stop();

		//线程调用函数
		virtual void RenderThread() = 0;

	private:
		void Thread();
		std::thread* m_thread = nullptr;
		std::condition_variable m_condition;
		std::mutex m_mutex;
		std::atomic_bool m_pause, m_stop;
	};
	class TOAPI UIFPSCounter
	{
	public:
		//计算FPS
		float CalcFPS();
		//设置最大FPS -1=无限制
		void SetMaxFPS(float fps);
		//获取最大FPS
		float GetMaxFPS();
		//限制FPS
		void LimitFPS();

	private:
		float fps = 0.0f;
		float fpstime = 0.0f;
		UINT frameCount = 0;
		steady_clock::time_point lastTime = steady_clock::now();
		steady_clock::time_point curTime = steady_clock::now();
		steady_clock::time_point m_BeginFrame = steady_clock::now();
		steady_clock::time_point m_EndFrame = steady_clock::now();
		UINT frame_count_per_second = 0;
		time_point<steady_clock, seconds>
			prev_time_in_seconds = time_point_cast<seconds>(m_BeginFrame);
		steady_clock::duration m_fpsLimit = {};
	};
	//[线程安全] 队列
	template <typename T>
	class TOAPI UIQueue
	{
	private:
		std::list<T> m_queue;
		std::mutex m_mutex;
	public:
		UIQueue() = default;

		bool empty()
		{
			std::unique_lock<std::mutex> lock(m_mutex);
			return m_queue.empty();
		}

		auto size()
		{
			std::unique_lock<std::mutex> lock(m_mutex);
			return m_queue.size();
		}

		void insertquque(T& t)
		{
			std::unique_lock<std::mutex> lock(m_mutex);
			m_queue.push_front(t);
		}

		void enqueue(T& t)
		{
			std::unique_lock<std::mutex> lock(m_mutex);
			m_queue.push_back(t);
		}

		bool dequeue(T& t)
		{
			std::unique_lock<std::mutex> lock(m_mutex);
			if (m_queue.empty())
				return false;
			t = std::move(m_queue.front());
			m_queue.pop_front();
			return true;
		}

		void clear()
		{
			std::unique_lock<std::mutex> lock(m_mutex);
			m_queue.clear();
		}
	};
	class TOAPI UIBase
	{
	public:
		LPVOID m_UIView = nullptr;
		LPVOID m_UIWindow = nullptr;
	protected:
		LPVOID m_objChildFirst = nullptr;
		LPVOID m_objChildLast = nullptr;
		friend class UIWnd;
		friend class UIControl;
		friend class UICanvas;
		friend class UILayout;
		friend class UIAnimation;
	};
	typedef struct tagTimerInfo
	{
		UIBase *pPropObj = nullptr;
		size_t								nLocalID = 0;
		HWND								hWnd = NULL;
		UINT								uWinTimer = 0;
		bool								bKilled = false;
	} TimerInfo;

	typedef std::vector<TimerInfo> VecTimerInfo;
}