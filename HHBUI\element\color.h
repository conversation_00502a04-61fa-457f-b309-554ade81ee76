﻿#pragma once
namespace HHBUI
{
	enum ColorEnum
	{
		AliceBlue = 0xF0F8FF,
		AntiqueWhite = 0xFAEBD7,
		Aqua = 0x00FFFF,
		Aquamarine = 0x7FFFD4,
		Azure = 0xF0FFFF,
		Beige = 0xF5F5DC,
		Bisque = 0xFFE4C4,
		Black = 0x000000,
		BlanchedAlmond = 0xFFEBCD,
		Blue = 0x0000FF,
		BlueViolet = 0x8A2BE2,
		<PERSON> = 0xA52A2A,
		BurlyWood = 0xDEB887,
		CadetBlue = 0x5F9EA0,
		Chartreuse = 0x7FFF00,
		Chocolate = 0xD2691E,
		Coral = 0xFF7F50,
		CornflowerBlue = 0x6495ED,
		Cornsilk = 0xFFF8DC,
		Crimson = 0xDC143C,
		Cyan = 0x00FFFF,
		DarkBlue = 0x00008B,
		DarkCyan = 0x008B8B,
		DarkGoldenrod = 0xB8860B,
		DarkGray = 0xA9A9A9,
		DarkGreen = 0x006400,
		DarkKhaki = 0xBDB76B,
		DarkMagenta = 0x8B008B,
		DarkOliveGreen = 0x556B2F,
		DarkOrange = 0xFF8C00,
		DarkOrchid = 0x9932CC,
		DarkRed = 0x8B0000,
		DarkSalmon = 0xE9967A,
		DarkSeaGreen = 0x8FBC8F,
		DarkSlateBlue = 0x483D8B,
		DarkSlateGray = 0x2F4F4F,
		DarkTurquoise = 0x00CED1,
		DarkViolet = 0x9400D3,
		DeepPink = 0xFF1493,
		DeepSkyBlue = 0x00BFFF,
		DimGray = 0x696969,
		DodgerBlue = 0x1E90FF,
		Firebrick = 0xB22222,
		FloralWhite = 0xFFFAF0,
		ForestGreen = 0x228B22,
		Fuchsia = 0xFF00FF,
		Gainsboro = 0xDCDCDC,
		GhostWhite = 0xF8F8FF,
		Gold = 0xFFD700,
		Goldenrod = 0xDAA520,
		Gray = 0x808080,
		Green = 0x008000,
		GreenYellow = 0xADFF2F,
		Honeydew = 0xF0FFF0,
		HotPink = 0xFF69B4,
		IndianRed = 0xCD5C5C,
		Indigo = 0x4B0082,
		Ivory = 0xFFFFF0,
		Khaki = 0xF0E68C,
		Lavender = 0xE6E6FA,
		LavenderBlush = 0xFFF0F5,
		LawnGreen = 0x7CFC00,
		LemonChiffon = 0xFFFACD,
		LightBlue = 0xADD8E6,
		LightCoral = 0xF08080,
		LightCyan = 0xE0FFFF,
		LightGoldenrodYellow = 0xFAFAD2,
		LightGreen = 0x90EE90,
		LightGray = 0xD3D3D3,
		LightPink = 0xFFB6C1,
		LightSalmon = 0xFFA07A,
		LightSeaGreen = 0x20B2AA,
		LightSkyBlue = 0x87CEFA,
		LightSlateGray = 0x778899,
		LightSteelBlue = 0xB0C4DE,
		LightYellow = 0xFFFFE0,
		Lime = 0x00FF00,
		LimeGreen = 0x32CD32,
		Linen = 0xFAF0E6,
		Magenta = 0xFF00FF,
		Maroon = 0x800000,
		MediumAquamarine = 0x66CDAA,
		MediumBlue = 0x0000CD,
		MediumOrchid = 0xBA55D3,
		MediumPurple = 0x9370DB,
		MediumSeaGreen = 0x3CB371,
		MediumSlateBlue = 0x7B68EE,
		MediumSpringGreen = 0x00FA9A,
		MediumTurquoise = 0x48D1CC,
		MediumVioletRed = 0xC71585,
		MidnightBlue = 0x191970,
		MintCream = 0xF5FFFA,
		MistyRose = 0xFFE4E1,
		Moccasin = 0xFFE4B5,
		NavajoWhite = 0xFFDEAD,
		Navy = 0x000080,
		OldLace = 0xFDF5E6,
		Olive = 0x808000,
		OliveDrab = 0x6B8E23,
		Orange = 0xFFA500,
		OrangeRed = 0xFF4500,
		Orchid = 0xDA70D6,
		PaleGoldenrod = 0xEEE8AA,
		PaleGreen = 0x98FB98,
		PaleTurquoise = 0xAFEEEE,
		PaleVioletRed = 0xDB7093,
		PapayaWhip = 0xFFEFD5,
		PeachPuff = 0xFFDAB9,
		Peru = 0xCD853F,
		Pink = 0xFFC0CB,
		Plum = 0xDDA0DD,
		PowderBlue = 0xB0E0E6,
		Purple = 0x800080,
		Red = 0xFF0000,
		RosyBrown = 0xBC8F8F,
		RoyalBlue = 0x4169E1,
		SaddleBrown = 0x8B4513,
		Salmon = 0xFA8072,
		SandyBrown = 0xF4A460,
		SeaGreen = 0x2E8B57,
		SeaShell = 0xFFF5EE,
		Sienna = 0xA0522D,
		Silver = 0xC0C0C0,
		SkyBlue = 0x87CEEB,
		SlateBlue = 0x6A5ACD,
		SlateGray = 0x708090,
		Snow = 0xFFFAFA,
		SpringGreen = 0x00FF7F,
		SteelBlue = 0x4682B4,
		Tan = 0xD2B48C,
		Teal = 0x008080,
		Thistle = 0xD8BFD8,
		Tomato = 0xFF6347,
		Turquoise = 0x40E0D0,
		Violet = 0xEE82EE,
		Wheat = 0xF5DEB3,
		White = 0xFFFFFF,
		WhiteSmoke = 0xF5F5F5,
		Yellow = 0xFFFF00,
		YellowGreen = 0x9ACD32,
	};
	class TOAPI UIColor
	{
	public:
		UIColor();
		//构造函数将从UIColor对象构建类
		UIColor(const UIColor& ColorObject);
		//构造函数将从D2D1_COLOR_F对象构建类
		UIColor(const D2D1_COLOR_F& ColorObject);
		/*
		* 构造函数将从ColorEnum值构建类
		* 温馨提示：由于此功能不会对颜色进行判断 参数的合法性更好
		*/
		UIColor(const ColorEnum& Color, const float& AlphaValue = 1.f);
		/*
		* 构造函数将从以下R G B A构建类
        * value提示：R、G、B、A的值不应该是无效值 并且以小数颜色赋值
		*/
		UIColor(const float& R, const float& G, const float& B, const float& A);
		UIColor(const float& R, const float& G, const float& B);
		/*
		* 构造函数将从以下R G B A构建类
		* value提示：R、G、B、A的值不应该是无效值 并且以0-255整数颜色赋值
		*/
		UIColor(const int& R, const int& G, const int& B, const int& A);
		UIColor(const int& R, const int& G, const int& B);
		//构造函数将从包含了 RGB 三个分量（8 位红色、8 位绿色和 8 位蓝色）的COLORREF构建
		UIColor(COLORREF rgb);
		//支持ARGB
		UIColor(INT rgba);
		//构造函数将从Hex类型构造
		UIColor(LPCWSTR Hex);

		static UIColor HSV(float h, float s, float v, float a = 1.0f) { float r, g, b; ColorConvertHSVtoRGB(h, s, v, r, g, b); return UIColor(r, g, b, a); }
		inline void SetHSV(float h, float s, float v, float a = 1.0f) { ColorConvertHSVtoRGB(h, s, v, DxColorObject.r, DxColorObject.g, DxColorObject.b); DxColorObject.a = a; }
		void ToHSV(float& out_h, float& out_s, float& out_v);
		static void HSLtoRGB(const float hsl[], float rgb[]);
		INT R(INT r = -1);
		INT G(INT g = -1);
		INT B(INT b = -1);
		INT A(INT a = -1);
		//获取此颜色的RGBA中的红色值
		float GetR() const;
		//获取此颜色的RGBA中的绿色值
		float GetG() const;
		//获取此颜色的RGBA中的蓝色值
		float GetB() const;
		//获取此颜色的RGBA中的alpha值
		float GetA() const;
		//获取DX颜色对象
		D2D1::ColorF GetDxObject() const;
		//获取RGB颜色值
		COLORREF GetRGB();
		INT GetARGB();
		//设置此颜色的RGBA中的红色值 指定的值不应该是无效值
		UIColor SetR(const float& R);
		//设置此颜色的RGBA中的绿色值 指定的值不应该是无效值
		UIColor SetG(const float& G);
		//设置此颜色的RGBA中的蓝色值 指定的值不应该是无效值
		UIColor SetB(const float& B);
		//设置此颜色的RGBA中的alpha值 指定的值不应该是无效值
		UIColor SetA(const float& A);
		//设置颜色深浅度，factor须≥0，值越小颜色越深，反之越淡，1为不变
		void SetColorLights(float factor);
		/*
	   * 根据R、G、B、a值制作一个UIColor对象
	   * 与通过构造函数构建UIColor不同 直接使用此函数可以构建BYTE值的颜色而不是float
	   * 因为这个函数不会判断颜色
	   */
		static UIColor FromBYTERGBA(BYTE R, BYTE G, BYTE B, BYTE A)
		{
			return UIColor(R / 255.f, G / 255.f, B / 255.f, A / 255.f);
		}
		static UIColor FromBYTERGBA(BYTE R, BYTE G, BYTE B)
		{
			return UIColor(R / 255.f, G / 255.f, B / 255.f, 1.f);
		}
		//检查颜色是否为空 如果每个通道颜色为0返回为TRUE 否则FALSE
		bool empty();
		//检查是否在正常范围
		bool isInRange();
	private:
		D2D1::ColorF DxColorObject{ 0.f,0.f,0.f,0.f };
		static void ColorConvertHSVtoRGB(float h, float s, float v, float& out_r, float& out_g, float& out_b);
	};
}
