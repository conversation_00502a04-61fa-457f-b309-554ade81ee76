﻿#pragma once

namespace HHBUI
{
	class TOAPI UITabs : public UIControl
	{
	public:
		enum tabs_type {
			linear,		//线型页头
			card,		//卡片式页头
			none		//无页头
		};
		enum tabs_point {
			left,		//左边
			top,		//顶边
			right,		//右边
			bottom		//底边
		};

		UITabs(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, tabs_type type = linear, INT nID = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT textFormat = -1);

		//置类型
		void SetTabsType(tabs_type type = linear);
		//置页头位置
		void SetTabsPoint(tabs_point point = top);
		/**
		 开启页头自撑开
		 point=top/bottom时，页头宽度撑满组件，此时设置页头宽度无效
		 point=left/right时，页头高度撑满组件，此时设置页头高度无效
		*/
		void EnableStretch(BOOL stretch);
		/**
		 * @brief 设置页头大小
		 * @param width 宽度，默认80
		 * @param height 高度，默认36
		 */
		void SetHeaderSize(UINT width = 80, UINT height = 36);

		/**
		 * @brief 设置页头颜色
		 * @param hover 选中高亮颜色，-1表示不修改
		 * @param line type=linear时线的颜色，-1表示不修改
		 */
		void SetHeaderColor(UIColor hover = {}, UIColor line = {});

		/**
		 * @brief 添加页面
		 * @param title 页头显示标题
		 * @param pID 页面ID，同组件nID
		 * @return 返回添加后总页数，返回-1表示添加失败
		 */
		INT AddPane(LPCWSTR title, INT pID = 0);
		/**
		 * @brief 通过索引或页面ID删除页面（二选一即可，优先使用index）
		 * @param Index 页面索引值，从0开始，-1表示不使用此参数
		 * @param pID 页面ID，-1表示不使用此参数
		 * @return 返回删除后总页数，返回-1表示删除失败或指定页面不存在
		 */
		INT DeletePane(INT index = -1, INT pID = -1);
		/**
		 * @brief 置页头标题（也可通过页面ID取到页面句柄后使用SetText设置）
		 * @param index 页面索引
		 * @param title 标题
		 * @return 返回是否设置成功
		 */
		BOOL SetTitle(INT index, LPCWSTR title);
		//删除所有页面
		void ClearPane();
		/**
		 * @brief 置显示页面（index和pID二选一即可，优先使用index）
		 * @param index 页面索引，从0开始，-1表示不使用此参数
		 * @param pID 页面ID，-1表示不使用此参数
		 * @return 返回是否设置成功
		 */
		BOOL SetPane(INT index = -1, INT pID = -1);
		/**
		 * @brief 取页面句柄（index和pID二选一即可，优先使用index）
		 * @param index 页面索引，从0开始，-1表示不使用此参数
		 * @param pID 页面ID，-1表示不使用此参数
		 * @return 成功返回页面句柄
		 */
		UIPage* GetPane(INT index = -1, INT pID = -1);

	protected:
		void ReLine();
		void RePane();
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		struct tabs_panes
		{
			ExRectF rc{};
			UIPage* view = nullptr;
		};
		struct tabs_info
		{
			std::vector<tabs_panes> list;
			tabs_type type = linear;
			tabs_point point = top;
			INT select = -1, hover = -1;
			ExPointF hsize{};
			ExRectF line{};
			BOOL bstre = FALSE;
			UIColor hclr[2] = { UIColor(208, 211, 217, 150), UIColor(20, 126, 255, 255)};
		}p_data;
		
	};
}
